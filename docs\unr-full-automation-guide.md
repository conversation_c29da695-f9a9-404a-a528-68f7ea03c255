# UNR完整自动化流程使用指南

## 概述

`unr-full-automation.js` 是一个完整的UNR申请自动化脚本，它整合了注册流程（`unr-complete-flow.js`）和申请流程（`unr-start-application.js`），实现端到端的自动化处理。

## 功能特性

- ✅ **完整流程自动化**：注册 → 申请一站式处理
- ✅ **批量处理**：支持处理多个学生账号
- ✅ **错误恢复**：单个学生失败不影响其他学生处理
- ✅ **详细报告**：提供完整的执行报告和统计信息
- ✅ **灵活配置**：支持指定处理范围和各种参数
- ✅ **实时监控**：可选的实时输出显示

## 使用方法

### 1. 基本使用

```bash
# 启动浏览器
npm run browser

# 运行完整自动化流程（处理所有学生）
npm run unr:automation

# 或者使用简短命令
npm run unr:auto
```

### 2. 指定处理范围

```bash
# 处理第1个学生（索引0）
npm run unr:auto 0 1

# 处理第1-5个学生（索引0-4）
npm run unr:auto 0 5

# 处理第3-8个学生（索引2-7）
npm run unr:auto 2 8
```

### 3. 使用环境变量

```bash
# Windows PowerShell
$env:START_INDEX=0; $env:END_INDEX=3; npm run unr:auto

# Windows CMD
set START_INDEX=0 && set END_INDEX=3 && npm run unr:auto

# Linux/Mac
START_INDEX=0 END_INDEX=3 npm run unr:auto
```

### 4. 高级配置

```bash
# 显示实时输出
$env:SHOW_REAL_TIME="true"; npm run unr:auto

# 设置学生之间的延迟时间（毫秒）
$env:DELAY_BETWEEN_STUDENTS=10000; npm run unr:auto

# 组合使用
$env:START_INDEX=0; $env:END_INDEX=5; $env:SHOW_REAL_TIME="true"; $env:DELAY_BETWEEN_STUDENTS=8000; npm run unr:auto
```

## 配置参数

| 参数 | 环境变量 | 默认值 | 说明 |
|------|----------|--------|------|
| 开始索引 | `START_INDEX` | 0 | 从第几个学生开始处理（从0开始） |
| 结束索引 | `END_INDEX` | null | 处理到第几个学生（不包含，null表示处理到最后） |
| 实时输出 | `SHOW_REAL_TIME` | false | 是否显示子进程的实时输出 |
| 学生间延迟 | `DELAY_BETWEEN_STUDENTS` | 5000 | 处理学生之间的延迟时间（毫秒） |

## 执行流程

对于每个学生，脚本会按以下顺序执行：

1. **📋 步骤1：注册流程**
   - 调用 `unr-complete-flow.js`
   - 填写注册表单
   - 获取邮箱验证码
   - 完成账号注册

2. **⏳ 等待间隔**
   - 注册完成后等待5秒

3. **📋 步骤2：申请流程**
   - 调用 `unr-start-application.js`
   - 启动申请流程
   - 填写申请表单
   - 完成申请提交

## 输出报告

脚本执行完成后会生成详细的报告：

```
🎉 完整自动化流程报告
========================
📊 总处理学生数: 5
✅ 成功完成数: 4
❌ 失败数: 1
📈 成功率: 80.0%

📋 详细结果:
✅ 学生 #1: John Smith
   📧 邮箱: <EMAIL>
   📋 注册: ✅
   📋 申请: ✅
   ⏱️  耗时: 245秒

❌ 学生 #2: Jane Doe
   📧 邮箱: <EMAIL>
   📋 注册: ✅
   📋 申请: ❌
   ⏱️  耗时: 180秒
   ❌ 错误: 申请失败: Emergency Contact页面填写失败
```

## 错误处理

- **单个学生失败**：不会影响其他学生的处理
- **注册失败**：跳过该学生的申请流程
- **申请失败**：记录错误但继续处理下一个学生
- **进程异常**：捕获并记录异常信息

## 注意事项

1. **浏览器准备**：运行前确保浏览器已启动（`npm run browser`）
2. **数据文件**：确保 `test.xlsx` 文件存在且格式正确
3. **网络连接**：确保网络连接稳定，邮箱服务可用
4. **系统资源**：长时间运行可能消耗较多系统资源
5. **错误恢复**：如果遇到问题，可以从失败的学生索引重新开始

## 故障排除

### 常见问题

1. **浏览器连接失败**
   ```bash
   # 重新启动浏览器
   npm run browser
   ```

2. **邮箱验证码获取失败**
   - 检查邮箱配置
   - 确认邮件转发设置
   - 检查网络连接

3. **表单填写失败**
   - 检查网页结构是否变化
   - 确认选择器是否正确
   - 查看错误日志

### 调试模式

```bash
# 启用实时输出查看详细过程
$env:SHOW_REAL_TIME="true"; npm run unr:auto 0 1
```

## 示例用法

```bash
# 示例1：处理前3个学生
npm run unr:auto 0 3

# 示例2：处理第5-10个学生，启用实时输出
$env:START_INDEX=4; $env:END_INDEX=10; $env:SHOW_REAL_TIME="true"; npm run unr:auto

# 示例3：处理单个学生（第8个）
npm run unr:auto 7 8

# 示例4：处理所有学生，设置较长的延迟时间
$env:DELAY_BETWEEN_STUDENTS=10000; npm run unr:auto
```
