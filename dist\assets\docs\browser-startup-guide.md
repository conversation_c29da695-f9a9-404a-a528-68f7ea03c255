# 浏览器启动指南

本指南介绍如何启动支持Puppeteer连接的浏览器实例。

## 🚀 快速开始

### 方法1：使用npm脚本（推荐）

```bash
# Node.js版本（跨平台）
npm run browser

# Windows批处理版本
npm run browser:bat

# PowerShell版本
npm run browser:ps1
```

### 方法2：直接运行脚本

```bash
# Node.js版本
node scripts/start-browser.js

# Windows批处理版本
scripts\start-browser.bat

# PowerShell版本
powershell -ExecutionPolicy Bypass -File scripts\start-browser.ps1
```

## 📋 启动参数说明

### 原始参数（保持指纹伪装）
- `--fingerprint=2024` - 指纹标识
- `--fingerprint-platform=macos` - 平台伪装为macOS
- `--fingerprint-platform-version=15.2.0` - 系统版本
- `--fingerprint-brand=Edge` - 浏览器品牌伪装
- `--fingerprint-hardware-concurrency=4` - 硬件并发数
- `--timezone=America/Los_Angeles` - 时区设置
- `--lang=en-US` - 界面语言
- `--accept-lang=en-US,zh-CN` - 接受语言
- `--proxy-server=socks5://127.0.0.1:7897` - 代理服务器
- `--disable-webgl` - 禁用WebGL
- `--disable-webgl2` - 禁用WebGL2
- `--disable-accelerated-2d-canvas` - 禁用2D画布加速
- `--user-data-dir=D:\chrome\bycache` - 用户数据目录

### 新增调试参数
- `--remote-debugging-port=9222` - 远程调试端口
- `--remote-allow-origins=*` - 允许所有来源连接

## 🔧 配置修改

如需修改配置，请编辑 `config/browser-config.js` 文件：

```javascript
const browserConfig = {
  // 修改浏览器路径
  paths: {
    chromium: "你的浏览器路径",
    executable: "chrome.exe"
  },
  
  // 修改调试端口
  debugging: {
    port: 9222,
    host: "localhost"
  },
  
  // 修改启动参数
  originalArgs: [
    // 在这里添加或修改参数
  ]
};
```

## 🌐 Puppeteer连接

启动浏览器后，使用以下代码连接：

```javascript
const puppeteer = require('puppeteer-core');

async function connectToBrowser() {
  const browser = await puppeteer.connect({
    browserWSEndpoint: 'ws://localhost:9222'
  });
  
  const page = await browser.newPage();
  await page.goto('https://example.com');
  
  // 进行自动化操作...
  
  await browser.disconnect();
}
```

## ⚠️ 注意事项

1. **端口占用**：确保端口9222未被其他程序占用
2. **代理设置**：确保代理服务器 `127.0.0.1:7897` 正常运行
3. **路径检查**：确认浏览器安装路径正确
4. **权限问题**：PowerShell脚本可能需要执行权限

## 🔍 故障排除

### 浏览器启动失败
- 检查浏览器路径是否正确
- 确认可执行文件名称（chrome.exe 或 chromium.exe）
- 检查用户数据目录权限

### 端口连接失败
- 确认端口9222未被占用
- 检查防火墙设置
- 尝试使用其他端口

### 代理连接问题
- 确认代理服务器运行状态
- 检查代理地址和端口
- 测试网络连接

## 📚 相关文档

- [Puppeteer官方文档](https://pptr.dev/)
- [Chrome DevTools Protocol](https://chromedevtools.github.io/devtools-protocol/)
- [浏览器启动参数参考](https://peter.sh/experiments/chromium-command-line-switches/)
