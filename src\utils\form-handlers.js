/**
 * 表单元素处理器集合
 * 提供各种表单元素的统一操作接口
 */

const { retry, ErrorType } = require('./retry-helper');

/**
 * 元素定位方式枚举
 */
const LocatorType = {
  CSS: 'css',
  XPATH: 'xpath',
  TEXT: 'text',
  PLACEHOLDER: 'placeholder',
  LABEL: 'label'
};

/**
 * 表单元素类型枚举
 */
const ElementType = {
  TEXT_INPUT: 'text_input',
  PASSWORD_INPUT: 'password_input',
  EMAIL_INPUT: 'email_input',
  NUMBER_INPUT: 'number_input',
  TEXTAREA: 'textarea',
  SELECT: 'select',
  CHECKBOX: 'checkbox',
  RADIO: 'radio',
  FILE_INPUT: 'file_input',
  BUTTON: 'button',
  SUBMIT: 'submit'
};

/**
 * 基础表单元素处理器
 */
class BaseFormHandler {
  constructor(page, options = {}) {
    this.page = page;
    this.options = {
      timeout: options.timeout || 30000,
      waitForVisible: options.waitForVisible !== false,
      waitForEnabled: options.waitForEnabled !== false,
      retryOptions: options.retryOptions || {
        maxRetries: 3,
        baseDelay: 500
      }
    };
  }

  /**
   * 根据定位器类型查找元素
   */
  async findElement(locator, locatorType = LocatorType.CSS) {
    return await retry(async () => {
      let selector;
      
      switch (locatorType) {
        case LocatorType.CSS:
          selector = locator;
          break;
        case LocatorType.XPATH:
          return await this.page.$x(locator);
        case LocatorType.TEXT:
          selector = `text=${locator}`;
          break;
        case LocatorType.PLACEHOLDER:
          selector = `[placeholder*="${locator}"]`;
          break;
        case LocatorType.LABEL:
          // 通过label文本查找关联的input
          const labelElement = await this.page.$(`label:has-text("${locator}")`);
          if (labelElement) {
            const forAttr = await labelElement.getAttribute('for');
            if (forAttr) {
              selector = `#${forAttr}`;
            }
          }
          break;
        default:
          selector = locator;
      }

      const element = await this.page.$(selector);
      if (!element) {
        throw new Error(`Element not found: ${locator} (${locatorType})`);
      }
      
      return element;
    }, this.options.retryOptions);
  }

  /**
   * 等待元素可见
   */
  async waitForVisible(element) {
    if (!this.options.waitForVisible) return;

    return await retry(async () => {
      const isVisible = await element.evaluate(el => {
        const style = window.getComputedStyle(el);
        return style.display !== 'none' && style.visibility !== 'hidden' && el.offsetParent !== null;
      });
      if (!isVisible) {
        throw new Error('Element is not visible');
      }
    }, this.options.retryOptions);
  }

  /**
   * 等待元素可用
   */
  async waitForEnabled(element) {
    if (!this.options.waitForEnabled) return;

    return await retry(async () => {
      const isEnabled = await element.evaluate(el => !el.disabled);
      if (!isEnabled) {
        throw new Error('Element is not enabled');
      }
    }, this.options.retryOptions);
  }

  /**
   * 滚动到元素位置
   */
  async scrollToElement(element) {
    await element.scrollIntoViewIfNeeded();
  }

  /**
   * 高亮元素（调试用）
   */
  async highlightElement(element, duration = 1000) {
    await element.evaluate((el, duration) => {
      const originalStyle = el.style.cssText;
      el.style.cssText += 'border: 2px solid red !important; background-color: yellow !important;';
      setTimeout(() => {
        el.style.cssText = originalStyle;
      }, duration);
    }, duration);
  }
}

/**
 * 文本输入处理器
 */
class TextInputHandler extends BaseFormHandler {
  /**
   * 填写文本输入框
   */
  async fill(locator, value, options = {}) {
    const locatorType = options.locatorType || LocatorType.CSS;
    const clearFirst = options.clearFirst !== false;
    const typeDelay = options.typeDelay || 50;

    return await retry(async () => {
      console.log(`📝 填写文本输入框: ${locator} = "${value}"`);
      
      const element = await this.findElement(locator, locatorType);
      await this.waitForVisible(element);
      await this.waitForEnabled(element);
      await this.scrollToElement(element);

      // 聚焦元素
      await element.focus();

      // 清空现有内容
      if (clearFirst) {
        await element.click({ clickCount: 3 }); // 选择所有文本
        await element.press('Delete');
      }

      // 输入新内容
      if (value) {
        await element.type(value, { delay: typeDelay });
      }

      // 验证输入结果
      const actualValue = await element.evaluate(el => el.value);
      if (actualValue !== value) {
        throw new Error(`Input verification failed. Expected: "${value}", Actual: "${actualValue}"`);
      }

      console.log(`✅ 文本输入完成: ${locator}`);
    }, this.options.retryOptions);
  }

  /**
   * 获取输入框的值
   */
  async getValue(locator, locatorType = LocatorType.CSS) {
    const element = await this.findElement(locator, locatorType);
    return await element.evaluate(el => el.value);
  }

  /**
   * 清空输入框
   */
  async clear(locator, locatorType = LocatorType.CSS) {
    return await this.fill(locator, '', { locatorType, clearFirst: true });
  }
}

/**
 * 下拉框处理器
 */
class SelectHandler extends BaseFormHandler {
  /**
   * 选择下拉框选项
   */
  async select(locator, value, options = {}) {
    const locatorType = options.locatorType || LocatorType.CSS;
    const selectBy = options.selectBy || 'value'; // 'value', 'text', 'index'

    return await retry(async () => {
      console.log(`📋 选择下拉框: ${locator} = "${value}"`);
      
      const element = await this.findElement(locator, locatorType);
      await this.waitForVisible(element);
      await this.waitForEnabled(element);
      await this.scrollToElement(element);

      let selectedValue;
      
      switch (selectBy) {
        case 'value':
          await element.select(value);
          selectedValue = value;
          break;
        case 'text':
          selectedValue = await element.evaluate((el, text) => {
            const option = Array.from(el.options).find(opt => opt.text === text);
            if (option) {
              el.value = option.value;
              el.dispatchEvent(new Event('change', { bubbles: true }));
              return option.value;
            }
            throw new Error(`Option with text "${text}" not found`);
          }, value);
          break;
        case 'index':
          selectedValue = await element.evaluate((el, index) => {
            if (index < el.options.length) {
              el.selectedIndex = index;
              el.dispatchEvent(new Event('change', { bubbles: true }));
              return el.value;
            }
            throw new Error(`Option at index ${index} not found`);
          }, parseInt(value));
          break;
        default:
          await element.select(value);
          selectedValue = value;
      }

      console.log(`✅ 下拉框选择完成: ${locator} = ${selectedValue}`);
      return selectedValue;
    }, this.options.retryOptions);
  }

  /**
   * 获取下拉框的选中值
   */
  async getSelectedValue(locator, locatorType = LocatorType.CSS) {
    const element = await this.findElement(locator, locatorType);
    return await element.evaluate(el => el.value);
  }

  /**
   * 获取下拉框的所有选项
   */
  async getOptions(locator, locatorType = LocatorType.CSS) {
    const element = await this.findElement(locator, locatorType);
    return await element.$$eval('option', options => 
      options.map(option => ({
        value: option.value,
        text: option.textContent,
        selected: option.selected
      }))
    );
  }
}

/**
 * 复选框处理器
 */
class CheckboxHandler extends BaseFormHandler {
  /**
   * 设置复选框状态
   */
  async setChecked(locator, checked = true, options = {}) {
    const locatorType = options.locatorType || LocatorType.CSS;

    return await retry(async () => {
      console.log(`☑️  设置复选框: ${locator} = ${checked}`);
      
      const element = await this.findElement(locator, locatorType);
      await this.waitForVisible(element);
      await this.waitForEnabled(element);
      await this.scrollToElement(element);

      const isCurrentlyChecked = await element.evaluate(el => el.checked);

      if (isCurrentlyChecked !== checked) {
        await element.click();
      }

      // 验证状态
      const finalState = await element.evaluate(el => el.checked);
      if (finalState !== checked) {
        throw new Error(`Checkbox state verification failed. Expected: ${checked}, Actual: ${finalState}`);
      }

      console.log(`✅ 复选框状态设置完成: ${locator} = ${checked}`);
    }, this.options.retryOptions);
  }

  /**
   * 勾选复选框
   */
  async check(locator, locatorType = LocatorType.CSS) {
    return await this.setChecked(locator, true, { locatorType });
  }

  /**
   * 取消勾选复选框
   */
  async uncheck(locator, locatorType = LocatorType.CSS) {
    return await this.setChecked(locator, false, { locatorType });
  }

  /**
   * 获取复选框状态
   */
  async isChecked(locator, locatorType = LocatorType.CSS) {
    const element = await this.findElement(locator, locatorType);
    return await element.evaluate(el => el.checked);
  }
}

/**
 * 单选框处理器
 */
class RadioHandler extends BaseFormHandler {
  /**
   * 选择单选框
   */
  async select(locator, value, options = {}) {
    const locatorType = options.locatorType || LocatorType.CSS;

    return await retry(async () => {
      console.log(`🔘 选择单选框: ${locator} = "${value}"`);
      
      let element;

      if (locatorType === LocatorType.CSS && value && !locator.includes('[')) {
        // 如果locator是简单的name，尝试通过name和value定位
        element = await this.page.$(`input[name="${locator}"][value="${value}"]`);
      }

      if (!element) {
        element = await this.findElement(locator, locatorType);
      }

      await this.waitForVisible(element);
      await this.waitForEnabled(element);
      await this.scrollToElement(element);

      await element.click();

      // 验证选中状态
      const isChecked = await element.evaluate(el => el.checked);
      if (!isChecked) {
        throw new Error(`Radio button selection failed: ${locator}`);
      }

      console.log(`✅ 单选框选择完成: ${locator} = "${value}"`);
    }, this.options.retryOptions);
  }

  /**
   * 获取选中的单选框值
   */
  async getSelectedValue(name) {
    const checkedRadio = await this.page.$(`input[name="${name}"]:checked`);
    if (checkedRadio) {
      return await checkedRadio.evaluate(el => el.value);
    }
    return null;
  }
}

/**
 * 文件上传处理器
 */
class FileUploadHandler extends BaseFormHandler {
  /**
   * 上传文件
   */
  async upload(locator, filePath, options = {}) {
    const locatorType = options.locatorType || LocatorType.CSS;
    const multiple = options.multiple || false;

    return await retry(async () => {
      console.log(`📁 上传文件: ${locator} = "${filePath}"`);

      const element = await this.findElement(locator, locatorType);
      await this.waitForVisible(element);
      await this.scrollToElement(element);

      // 验证是否为文件输入框
      const inputType = await element.getAttribute('type');
      if (inputType !== 'file') {
        throw new Error(`Element is not a file input: ${locator}`);
      }

      // 上传文件
      if (Array.isArray(filePath)) {
        if (!multiple) {
          throw new Error('Multiple files provided but element does not support multiple uploads');
        }
        await element.setInputFiles(filePath);
      } else {
        await element.setInputFiles(filePath);
      }

      console.log(`✅ 文件上传完成: ${locator}`);
    }, this.options.retryOptions);
  }

  /**
   * 清空文件选择
   */
  async clear(locator, locatorType = LocatorType.CSS) {
    const element = await this.findElement(locator, locatorType);
    await element.setInputFiles([]);
  }

  /**
   * 获取已选择的文件信息
   */
  async getSelectedFiles(locator, locatorType = LocatorType.CSS) {
    const element = await this.findElement(locator, locatorType);
    return await element.evaluate(input => {
      const files = Array.from(input.files);
      return files.map(file => ({
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      }));
    });
  }
}

/**
 * 按钮处理器
 */
class ButtonHandler extends BaseFormHandler {
  /**
   * 点击按钮
   */
  async click(locator, options = {}) {
    const locatorType = options.locatorType || LocatorType.CSS;
    const waitForNavigation = options.waitForNavigation || false;
    const doubleClick = options.doubleClick || false;

    return await retry(async () => {
      console.log(`🔘 点击按钮: ${locator}`);

      const element = await this.findElement(locator, locatorType);
      await this.waitForVisible(element);
      await this.waitForEnabled(element);
      await this.scrollToElement(element);

      if (waitForNavigation) {
        // 等待页面导航
        await Promise.all([
          this.page.waitForNavigation({ timeout: this.options.timeout }),
          doubleClick ? element.dblclick() : element.click()
        ]);
      } else {
        if (doubleClick) {
          await element.dblclick();
        } else {
          await element.click();
        }
      }

      console.log(`✅ 按钮点击完成: ${locator}`);
    }, this.options.retryOptions);
  }

  /**
   * 提交表单
   */
  async submit(locator, options = {}) {
    const locatorType = options.locatorType || LocatorType.CSS;
    const waitForResponse = options.waitForResponse || false;

    return await retry(async () => {
      console.log(`📤 提交表单: ${locator}`);

      const element = await this.findElement(locator, locatorType);
      await this.waitForVisible(element);
      await this.waitForEnabled(element);
      await this.scrollToElement(element);

      if (waitForResponse) {
        // 等待网络响应
        const [response] = await Promise.all([
          this.page.waitForResponse(response => response.status() === 200, { timeout: this.options.timeout }),
          element.click()
        ]);
        console.log(`✅ 表单提交完成，响应状态: ${response.status()}`);
        return response;
      } else {
        await element.click();
        console.log(`✅ 表单提交完成: ${locator}`);
      }
    }, this.options.retryOptions);
  }

  /**
   * 获取按钮文本
   */
  async getText(locator, locatorType = LocatorType.CSS) {
    const element = await this.findElement(locator, locatorType);
    return await element.evaluate(el => el.textContent);
  }

  /**
   * 检查按钮是否可用
   */
  async isEnabled(locator, locatorType = LocatorType.CSS) {
    const element = await this.findElement(locator, locatorType);
    return await element.evaluate(el => !el.disabled);
  }
}

/**
 * 统一表单处理器管理类
 */
class FormHandlerManager {
  constructor(page, options = {}) {
    this.page = page;
    this.options = options;

    // 初始化各种处理器
    this.textInput = new TextInputHandler(page, options);
    this.select = new SelectHandler(page, options);
    this.checkbox = new CheckboxHandler(page, options);
    this.radio = new RadioHandler(page, options);
    this.fileUpload = new FileUploadHandler(page, options);
    this.button = new ButtonHandler(page, options);
  }

  /**
   * 智能表单元素处理
   * 根据元素类型自动选择合适的处理器
   */
  async handleElement(locator, value, options = {}) {
    const locatorType = options.locatorType || LocatorType.CSS;
    const elementType = options.elementType;

    // 如果没有指定元素类型，尝试自动检测
    let detectedType = elementType;
    if (!detectedType) {
      detectedType = await this.detectElementType(locator, locatorType);
    }

    console.log(`🔍 处理表单元素: ${locator} (类型: ${detectedType})`);

    switch (detectedType) {
      case ElementType.TEXT_INPUT:
      case ElementType.PASSWORD_INPUT:
      case ElementType.EMAIL_INPUT:
      case ElementType.NUMBER_INPUT:
      case ElementType.TEXTAREA:
        return await this.textInput.fill(locator, value, options);

      case ElementType.SELECT:
        return await this.select.select(locator, value, options);

      case ElementType.CHECKBOX:
        return await this.checkbox.setChecked(locator, value, options);

      case ElementType.RADIO:
        return await this.radio.select(locator, value, options);

      case ElementType.FILE_INPUT:
        return await this.fileUpload.upload(locator, value, options);

      case ElementType.BUTTON:
      case ElementType.SUBMIT:
        return await this.button.click(locator, options);

      default:
        throw new Error(`Unsupported element type: ${detectedType}`);
    }
  }

  /**
   * 自动检测元素类型
   */
  async detectElementType(locator, locatorType = LocatorType.CSS) {
    const element = await this.findElement(locator, locatorType);

    const tagName = await element.evaluate(el => el.tagName.toLowerCase());
    const type = await element.evaluate(el => el.type?.toLowerCase());
    const role = await element.evaluate(el => el.getAttribute('role'));

    // 根据标签和属性判断元素类型
    if (tagName === 'input') {
      switch (type) {
        case 'text':
        case 'search':
        case 'url':
          return ElementType.TEXT_INPUT;
        case 'password':
          return ElementType.PASSWORD_INPUT;
        case 'email':
          return ElementType.EMAIL_INPUT;
        case 'number':
        case 'range':
          return ElementType.NUMBER_INPUT;
        case 'checkbox':
          return ElementType.CHECKBOX;
        case 'radio':
          return ElementType.RADIO;
        case 'file':
          return ElementType.FILE_INPUT;
        case 'submit':
          return ElementType.SUBMIT;
        case 'button':
          return ElementType.BUTTON;
        default:
          return ElementType.TEXT_INPUT; // 默认为文本输入
      }
    } else if (tagName === 'textarea') {
      return ElementType.TEXTAREA;
    } else if (tagName === 'select') {
      return ElementType.SELECT;
    } else if (tagName === 'button' || role === 'button') {
      return ElementType.BUTTON;
    }

    // 如果无法确定，默认为文本输入
    return ElementType.TEXT_INPUT;
  }

  /**
   * 查找元素（复用BaseFormHandler的方法）
   */
  async findElement(locator, locatorType = LocatorType.CSS) {
    const baseHandler = new BaseFormHandler(this.page, this.options);
    return await baseHandler.findElement(locator, locatorType);
  }

  /**
   * 批量处理表单元素
   */
  async fillForm(formData, options = {}) {
    const results = [];

    for (const [locator, config] of Object.entries(formData)) {
      try {
        let value, elementOptions;

        if (typeof config === 'object' && config !== null) {
          value = config.value;
          elementOptions = { ...options, ...config };
        } else {
          value = config;
          elementOptions = options;
        }

        await this.handleElement(locator, value, elementOptions);
        results.push({ locator, success: true });

      } catch (error) {
        console.error(`❌ 处理元素失败: ${locator} - ${error.message}`);
        results.push({ locator, success: false, error: error.message });

        if (options.stopOnError) {
          throw error;
        }
      }
    }

    return results;
  }

  /**
   * 等待表单加载完成
   */
  async waitForFormReady(formSelector, options = {}) {
    const timeout = options.timeout || this.options.timeout || 30000;

    return await retry(async () => {
      console.log(`⏳ 等待表单加载: ${formSelector}`);

      // 等待表单容器出现
      await this.page.waitForSelector(formSelector, { timeout });

      // 等待表单内的必需元素
      if (options.requiredElements) {
        for (const selector of options.requiredElements) {
          await this.page.waitForSelector(selector, { timeout });
        }
      }

      console.log(`✅ 表单加载完成: ${formSelector}`);
    }, { maxRetries: 3, baseDelay: 1000 });
  }
}

module.exports = {
  LocatorType,
  ElementType,
  BaseFormHandler,
  TextInputHandler,
  SelectHandler,
  CheckboxHandler,
  RadioHandler,
  FileUploadHandler,
  ButtonHandler,
  FormHandlerManager
};
