/**
 * UNR申请启动模块
 * 专门用于启动UNR大学申请流程
 */

const { FormFiller } = require('../form-filler');
const { ExcelReader } = require('../utils/excel-reader');
const path = require('path');

/**
 * UNR申请启动处理器
 */
class UNRApplicationStarter {
  constructor(options = {}) {
    this.options = {
      enableLogging: options.enableLogging !== false,
      enableScreenshots: options.enableScreenshots !== false,
      screenshotPath: options.screenshotPath || './screenshots',
      waitAfterClick: options.waitAfterClick || 2000,
      dialogTimeout: options.dialogTimeout || 10000,
      ...options
    };
    
    this.formFiller = null;
    this.studentData = null;
  }

  /**
   * 初始化申请启动处理器
   */
  async initialize() {
    this.formFiller = new FormFiller({
      enableLogging: this.options.enableLogging,
      enableScreenshots: this.options.enableScreenshots,
      screenshotPath: this.options.screenshotPath,
      stopOnError: false
    });

    await this.formFiller.initialize();
    this.log('🚀 UNR申请启动处理器初始化完成');
  }

  /**
   * 执行完整申请启动流程
   */
  async startApplication(studentData) {
    this.studentData = studentData;
    this.log(`\n📝 开始完整申请启动流程: ${studentData.firstName} ${studentData.lastName}`);
    this.log(`📧 邮箱: ${studentData.email}`);

    try {
      // 步骤1: 启动申请流程
      await this.step1_StartApplicationProcess();

      // 步骤2: 验证申请状态
      await this.step2_VerifyApplicationStatus();

      // 步骤3: 打开申请表单
      await this.step3_OpenApplicationForm();

      // 步骤4: 填写申请信息表单
      await this.step4_FillApplicationInformationForm();

      // 步骤5: 处理Nondegree申请说明页面
      await this.step5_HandleNondegreeInstructions();

      // 步骤6: 处理申请学期页面
      await this.step6_HandleApplicationTerm();

      // 步骤7: 处理个人背景页面
      await this.step7_HandlePersonalBackground();

      // 步骤8: 处理背景信息续页
      await this.step8_HandleBackgroundContinued();

      // 步骤9: 处理紧急联系人页面
      await this.step9_HandleEmergencyContact();

      // 步骤10: 处理学术历史页面
      await this.step10_HandleAcademicHistory();

      // 步骤11: 处理项目选择页面
      await this.step11_HandleProgramSelection();

      // 步骤12: 处理签名页面
      await this.step12_HandleSignature();

      // 步骤13: 处理审核页面
      await this.step13_HandleReview();

      // 最终验证
      const result = await this.verifyCompleteWorkflow();

      this.log(`✅ 学生 ${studentData.firstName} ${studentData.lastName} 完整申请启动流程完成`);

      // 关闭浏览器为下一个账号做准备
      await this.closeBrowserForNextAccount();

      return {
        success: true,
        studentData: this.studentData,
        result: result
      };

    } catch (error) {
      this.log(`❌ 学生 ${studentData.firstName} ${studentData.lastName} 申请启动失败: ${error.message}`);

      // 即使出错也要关闭浏览器
      await this.closeBrowserForNextAccount();

      return {
        success: false,
        studentData: this.studentData,
        error: error.message
      };
    }
  }

  /**
   * 关闭浏览器为下一个账号做准备
   */
  async closeBrowserForNextAccount() {
    this.log('🔄 关闭浏览器为下一个账号做准备...');

    try {
      if (this.formFiller && this.formFiller.browser) {
        await this.formFiller.browser.close();
        this.log('✅ 浏览器已关闭');

        // 清理formFiller引用
        this.formFiller = null;
      }
    } catch (error) {
      this.log(`⚠️  关闭浏览器失败: ${error.message}`);
    }
  }

  /**
   * 步骤1: 启动申请流程
   */
  async step1_StartApplicationProcess() {
    this.log('\n📋 步骤1: 启动申请流程');

    try {
      // 确保在正确的页面
      await this.ensureOnApplicationPage();

      // 检查是否已有现有申请
      const existingApplication = await this.checkForExistingApplication();

      if (existingApplication.found) {
        this.log('✅ 找到现有申请，使用现有申请继续流程');
        // 点击现有申请
        await this.clickExistingApplication(existingApplication);
      } else {
        this.log('ℹ️  未找到现有申请，创建新申请');
        // 点击开始申请链接
        await this.clickStartApplicationLink();

        // 检测并处理申请类型对话框
        await this.handleApplicationDialog();
      }

      this.log('✅ 步骤1完成: 申请流程已启动');

    } catch (error) {
      this.log(`❌ 步骤1失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 检查现有申请
   */
  async checkForExistingApplication() {
    this.log('🔍 检查是否存在现有申请...');

    try {
      const contentTableSelector = '#content > table > tbody > tr';

      // 等待内容表格加载
      await this.formFiller.currentPage.waitForSelector('#content', {
        timeout: this.options.dialogTimeout
      });

      // 检查是否有表格行
      const tableRows = await this.formFiller.currentPage.$$(contentTableSelector);

      if (tableRows.length === 0) {
        this.log('ℹ️  未找到申请表格，可能是首次申请');
        return { found: false };
      }

      this.log(`📊 找到 ${tableRows.length} 个申请记录，检查状态...`);

      // 检查每一行的状态
      for (let i = 0; i < tableRows.length; i++) {
        const row = tableRows[i];

        try {
          // 获取行信息
          const rowInfo = await row.evaluate(el => {
            const cells = el.querySelectorAll('td');
            if (cells.length >= 2) {
              const linkCell = cells[0];
              const statusCell = cells[1];

              const link = linkCell.querySelector('a');

              return {
                hasLink: !!link,
                linkText: link ? link.textContent.trim() : '',
                linkHref: link ? link.href : '',
                status: statusCell.textContent.trim(),
                linkElement: !!link
              };
            }
            return null;
          });

          if (rowInfo) {
            this.log(`📋 申请 ${i + 1}:`);
            this.log(`   链接文本: "${rowInfo.linkText}"`);
            this.log(`   状态: "${rowInfo.status}"`);
            this.log(`   有链接: ${rowInfo.hasLink}`);

            // 检查是否是"In Progress"状态
            if (rowInfo.status.toLowerCase().includes('in progress') ||
                rowInfo.status.toLowerCase().includes('进行中')) {

              this.log('✅ 找到进行中的申请');
              return {
                found: true,
                rowIndex: i,
                rowElement: row,
                linkText: rowInfo.linkText,
                status: rowInfo.status
              };
            }
          }
        } catch (rowError) {
          this.log(`⚠️  检查申请行 ${i + 1} 失败: ${rowError.message}`);
        }
      }

      this.log('ℹ️  未找到进行中的申请');
      return { found: false };

    } catch (error) {
      this.log(`⚠️  检查现有申请失败: ${error.message}`);
      return { found: false };
    }
  }

  /**
   * 点击现有申请
   */
  async clickExistingApplication(existingApplication) {
    this.log('🔘 点击现有申请...');

    try {
      // 在现有申请行中查找链接
      const link = await existingApplication.rowElement.$('td:first-child a');

      if (!link) {
        throw new Error('现有申请中找不到链接');
      }

      // 截图：点击现有申请前
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('before-click-existing-application');
      }

      this.log(`✅ 点击现有申请: ${existingApplication.linkText}`);
      await link.click();

      // 等待页面响应
      await new Promise(resolve => setTimeout(resolve, this.options.waitAfterClick));

      // 处理可能出现的申请详情对话框
      await this.handleApplicationDetailsDialog();

    } catch (error) {
      this.log(`❌ 点击现有申请失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 步骤2: 验证申请状态
   */
  async step2_VerifyApplicationStatus() {
    this.log('\n📋 步骤2: 验证申请状态');

    try {
      // 等待页面更新
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 检查当前页面状态
      const currentUrl = this.formFiller.currentPage.url();
      this.log(`📍 当前页面: ${currentUrl}`);

      // 如果已经在申请表单页面，跳过状态验证
      if (currentUrl.includes('/application/') || currentUrl.includes('/form/') || currentUrl.includes('/frm')) {
        this.log('ℹ️  已在申请表单页面，跳过状态验证步骤');
        return;
      }

      // 检查内容区域
      await this.checkContentArea();

      // 定位申请链接
      await this.locateApplicationLink();

      // 验证状态为"In Progress"
      await this.verifyInProgressStatus();

      this.log('✅ 步骤2完成: 申请状态验证通过');

    } catch (error) {
      this.log(`❌ 步骤2失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 步骤3: 打开申请表单
   */
  async step3_OpenApplicationForm() {
    this.log('\n📋 步骤3: 打开申请表单');

    try {
      // 检查当前页面状态
      const currentUrl = this.formFiller.currentPage.url();
      this.log(`📍 当前页面: ${currentUrl}`);

      // 如果已经在申请表单页面，跳过打开步骤
      if (currentUrl.includes('/application/') || currentUrl.includes('/form/') || currentUrl.includes('/frm')) {
        this.log('ℹ️  已在申请表单页面，跳过打开申请表单步骤');
        await this.verifyApplicationFormLoaded();
        return;
      }

      // 点击申请链接
      await this.clickApplicationLink();

      // 处理申请详情对话框
      await this.handleApplicationDetailsDialog();

      // 验证申请表单加载
      await this.verifyApplicationFormLoaded();

      this.log('✅ 步骤3完成: 申请表单已打开');

    } catch (error) {
      this.log(`❌ 步骤3失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 步骤4: 填写申请信息表单
   */
  async step4_FillApplicationInformationForm() {
    this.log('\n📋 步骤4: 填写申请信息表单');

    try {
      // 检测页面完成状态
      const completionStatus = await this.detectPageCompletionStatus('Application Information');

      if (completionStatus.isCompleted) {
        this.log('✅ 申请信息表单已完成，跳过填写步骤');
        return;
      }

      if (completionStatus.isPartiallyFilled) {
        this.log('⚠️  申请信息表单部分已填写，继续完成剩余部分');
      }

      // 验证表单容器
      await this.verifyFormContainer();

      // 执行顺序选择流程（智能跳过已填写的选项）
      await this.executeSequentialFormSelection(completionStatus);

      // 完成表单提交
      await this.completeFormSubmission();

      this.log('✅ 步骤4完成: 申请信息表单已填写');

    } catch (error) {
      this.log(`❌ 步骤4失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 确保在申请管理页面
   */
  async ensureOnApplicationPage() {
    this.log('🔍 检查当前页面...');

    const currentUrl = this.formFiller.currentPage.url();
    this.log(`📍 当前页面: ${currentUrl}`);

    // 检查是否为连续模式（从注册流程直接过来）
    const isContinuousMode = process.env.CONTINUOUS_MODE === 'true';
    const skipNavigation = process.env.SKIP_NAVIGATION === 'true';

    if (isContinuousMode && skipNavigation) {
      this.log('🔄 连续模式：跳过页面导航，直接使用当前页面');

      // 检查当前页面是否已经是申请页面
      if (currentUrl.includes('/apply/')) {
        this.log('✅ 当前已在申请管理页面，无需导航');
      } else {
        this.log('⚠️  当前页面不是申请页面，但连续模式下仍然尝试导航');
        await this.formFiller.currentPage.goto('https://admissions.unr.edu/apply/', {
          waitUntil: 'domcontentloaded',
          timeout: this.options.dialogTimeout
        });
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    } else {
      // 普通模式：如果不在申请页面，导航到申请页面
      if (!currentUrl.includes('/apply/')) {
        this.log('🌐 导航到申请管理页面...');
        await this.formFiller.currentPage.goto('https://admissions.unr.edu/apply/', {
          waitUntil: 'domcontentloaded',
          timeout: this.options.dialogTimeout
        });

        // 等待页面加载
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    this.log('✅ 已在申请管理页面');

    // 截图：申请管理页面
    if (this.options.enableScreenshots) {
      await this.takeScreenshot('application-management-page');
    }
  }

  /**
   * 点击开始申请链接
   */
  async clickStartApplicationLink() {
    this.log('🔘 查找并点击开始申请链接...');

    try {
      // 等待开始申请链接出现
      await this.formFiller.currentPage.waitForSelector('#start_application_link', { 
        timeout: this.options.dialogTimeout 
      });

      // 截图：点击前
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('before-click-start-application');
      }

      // 点击开始申请链接
      const startLink = await this.formFiller.currentPage.$('#start_application_link');
      if (!startLink) {
        throw new Error('找不到开始申请链接');
      }

      this.log('✅ 找到开始申请链接，准备点击');
      await startLink.click();
      this.log('✅ 开始申请链接已点击');

      // 等待页面响应
      await new Promise(resolve => setTimeout(resolve, this.options.waitAfterClick));

    } catch (error) {
      this.log(`❌ 点击开始申请链接失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 处理申请类型对话框
   */
  async handleApplicationDialog() {
    this.log('🔍 检测申请类型对话框...');

    try {
      // 等待对话框出现
      const dialogSelector = 'body > div.dialog_host.ui-draggable > div > div > form';

      this.log(`🔍 等待对话框出现: ${dialogSelector}`);
      this.log(`⏱️  超时时间: ${this.options.dialogTimeout}ms`);

      // 检查对话框是否出现，使用更长的等待时间
      let dialogExists = false;
      let attempts = 0;
      const maxAttempts = 3;

      while (!dialogExists && attempts < maxAttempts) {
        attempts++;
        this.log(`🔄 第${attempts}次尝试检测对话框...`);

        try {
          await this.formFiller.currentPage.waitForSelector(dialogSelector, {
            timeout: this.options.dialogTimeout
          });
          dialogExists = true;
          this.log('✅ 对话框检测成功');
        } catch (error) {
          this.log(`⚠️  第${attempts}次检测失败: ${error.message}`);

          if (attempts < maxAttempts) {
            this.log('⏳ 等待2秒后重试...');
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 检查页面状态
            await this.debugPageState();
          }
        }
      }

      if (!dialogExists) {
        this.log('❌ 未检测到申请类型对话框');

        // 尝试查找其他可能的对话框选择器
        await this.tryAlternativeDialogSelectors();
        return;
      }

      this.log('✅ 成功检测到申请类型对话框');

      // 等待对话框完全加载
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 截图：对话框出现
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('application-dialog-detected');
      }

      // 验证对话框内容
      await this.verifyDialogContent();

      // 选择申请类型
      await this.selectApplicationPeriod();

      // 点击确认按钮
      await this.clickConfirmButton();

      // 验证对话框消失
      await this.verifyDialogDismissal();

    } catch (error) {
      this.log(`❌ 处理申请类型对话框失败: ${error.message}`);

      // 详细错误调试
      await this.debugDialogError(error);
      throw error;
    }
  }

  /**
   * 调试页面状态
   */
  async debugPageState() {
    try {
      const currentUrl = this.formFiller.currentPage.url();
      const pageTitle = await this.formFiller.currentPage.title();

      this.log('🔍 当前页面状态调试:');
      this.log(`   📍 URL: ${currentUrl}`);
      this.log(`   📄 标题: ${pageTitle}`);

      // 检查页面上是否有任何对话框
      const allDialogs = await this.formFiller.currentPage.$$eval('div[class*="dialog"]',
        elements => elements.map(el => ({
          className: el.className,
          visible: el.offsetParent !== null,
          innerHTML: el.innerHTML.substring(0, 200)
        }))
      );

      if (allDialogs.length > 0) {
        this.log('🔍 发现的对话框元素:');
        allDialogs.forEach((dialog, index) => {
          this.log(`   ${index + 1}. 类名: ${dialog.className}`);
          this.log(`      可见: ${dialog.visible}`);
          this.log(`      内容: ${dialog.innerHTML.substring(0, 100)}...`);
        });
      } else {
        this.log('❌ 页面上未发现任何对话框元素');
      }

    } catch (error) {
      this.log(`⚠️  页面状态调试失败: ${error.message}`);
    }
  }

  /**
   * 尝试其他对话框选择器
   */
  async tryAlternativeDialogSelectors() {
    this.log('🔍 尝试其他可能的对话框选择器...');

    const alternativeSelectors = [
      '.dialog_host',
      'div.dialog_host',
      '[class*="dialog"]',
      '.ui-dialog',
      '.modal',
      'form[class*="dialog"]'
    ];

    for (const selector of alternativeSelectors) {
      try {
        this.log(`🔍 尝试选择器: ${selector}`);
        const element = await this.formFiller.currentPage.$(selector);

        if (element) {
          const isVisible = await element.evaluate(el => el.offsetParent !== null);
          this.log(`   找到元素，可见性: ${isVisible}`);

          if (isVisible) {
            this.log(`✅ 找到可见的对话框: ${selector}`);
            return true;
          }
        }
      } catch (error) {
        this.log(`   ❌ 选择器失败: ${error.message}`);
      }
    }

    this.log('❌ 所有备用选择器都未找到对话框');
    return false;
  }

  /**
   * 验证对话框内容
   */
  async verifyDialogContent() {
    this.log('🔍 验证对话框内容...');

    try {
      const dialogSelector = 'body > div.dialog_host.ui-draggable > div > div > form';

      // 检查表单是否存在
      const form = await this.formFiller.currentPage.$(dialogSelector);
      if (!form) {
        throw new Error('对话框表单不存在');
      }

      // 检查期间选择框是否存在
      const periodSelect = await this.formFiller.currentPage.$('#period');
      if (!periodSelect) {
        throw new Error('期间选择框不存在');
      }

      // 检查确认按钮是否存在
      const confirmButton = await this.formFiller.currentPage.$('body > div.dialog_host.ui-draggable > div > div > form > div.action > button.default');
      if (!confirmButton) {
        throw new Error('确认按钮不存在');
      }

      this.log('✅ 对话框内容验证通过');
      this.log('   ✅ 表单存在');
      this.log('   ✅ 期间选择框存在');
      this.log('   ✅ 确认按钮存在');

    } catch (error) {
      this.log(`❌ 对话框内容验证失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证对话框消失
   */
  async verifyDialogDismissal() {
    this.log('🔍 验证对话框是否消失...');

    try {
      const dialogSelector = 'body > div.dialog_host.ui-draggable > div > div > form';

      // 等待对话框消失
      await this.formFiller.currentPage.waitForFunction(
        (selector) => {
          const element = document.querySelector(selector);
          return !element || element.offsetParent === null;
        },
        { timeout: this.options.dialogTimeout },
        dialogSelector
      );

      this.log('✅ 对话框已成功消失');

    } catch (error) {
      this.log(`⚠️  对话框消失验证超时: ${error.message}`);
      // 不抛出错误，继续处理
    }
  }

  /**
   * 调试对话框错误
   */
  async debugDialogError(error) {
    this.log('🔍 对话框错误详细调试:');
    this.log(`   错误类型: ${error.name}`);
    this.log(`   错误消息: ${error.message}`);

    try {
      // 截图当前状态
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('dialog-error-debug');
      }

      // 获取页面HTML片段
      const bodyHTML = await this.formFiller.currentPage.evaluate(() => {
        return document.body.innerHTML.substring(0, 2000);
      });

      this.log('📄 页面HTML片段:');
      this.log(bodyHTML.substring(0, 500) + '...');

    } catch (debugError) {
      this.log(`⚠️  错误调试失败: ${debugError.message}`);
    }
  }

  /**
   * 选择申请期间
   */
  async selectApplicationPeriod() {
    this.log('📋 选择申请期间...');

    try {
      // 查找期间选择下拉框
      const periodSelector = '#period';

      this.log(`🔍 等待期间选择框: ${periodSelector}`);
      await this.formFiller.currentPage.waitForSelector(periodSelector, {
        timeout: this.options.dialogTimeout
      });

      // 等待选择框完全加载
      await new Promise(resolve => setTimeout(resolve, 1000));

      const periodSelect = await this.formFiller.currentPage.$(periodSelector);
      if (!periodSelect) {
        throw new Error('找不到申请期间选择框');
      }

      this.log('✅ 找到申请期间选择框');

      // 等待选项加载
      this.log('⏳ 等待选项加载...');
      await this.formFiller.currentPage.waitForFunction(
        () => {
          const select = document.querySelector('#period');
          return select && select.options.length > 1; // 等待至少有一个非默认选项
        },
        { timeout: this.options.dialogTimeout }
      );

      // 获取所有选项
      const options = await this.formFiller.currentPage.$$eval('#period option', options =>
        options.map((option, index) => ({
          index: index,
          value: option.value,
          text: option.textContent.trim(),
          selected: option.selected,
          disabled: option.disabled
        }))
      );

      this.log('📋 申请期间选项详情:');
      options.forEach((option, index) => {
        const status = option.selected ? '[已选中]' : option.disabled ? '[禁用]' : '[可选]';
        this.log(`   ${index + 1}. ${option.text} ${status}`);
        this.log(`      值: "${option.value}"`);
      });

      // 过滤可用选项（排除空值和禁用选项）
      const availableOptions = options.filter(option =>
        option.value &&
        option.value.trim() !== '' &&
        !option.disabled &&
        option.text.trim() !== ''
      );

      if (availableOptions.length === 0) {
        throw new Error('没有找到可用的申请期间选项');
      }

      this.log(`📊 找到 ${availableOptions.length} 个可用选项`);

      // 查找2025 Undergraduate Application选项（特定值）
      let targetOption = availableOptions.find(option =>
        option.value === 'ed5a5d2d-5c2e-471d-a98a-c36990b31016'
      );

      // 如果没找到特定值，尝试按文本查找
      if (!targetOption) {
        targetOption = availableOptions.find(option =>
          option.text.toLowerCase().includes('2025') &&
          option.text.toLowerCase().includes('undergraduate')
        );
      }

      // 如果还没找到，使用第一个可用选项
      if (!targetOption) {
        this.log('⚠️  未找到2025 Undergraduate Application，使用第一个可用选项');
        targetOption = availableOptions[0];
      }

      this.log(`🎯 目标选项: ${targetOption.text}`);
      this.log(`🎯 目标值: "${targetOption.value}"`);

      // 执行选择
      this.log('🔄 执行选择操作...');

      // 方法1：使用select方法
      try {
        await this.formFiller.currentPage.select(periodSelector, targetOption.value);
        this.log('✅ 使用select方法选择成功');
      } catch (selectError) {
        this.log(`⚠️  select方法失败: ${selectError.message}`);

        // 方法2：使用点击方法
        this.log('🔄 尝试点击选择方法...');
        await this.formFiller.currentPage.click(periodSelector);
        await new Promise(resolve => setTimeout(resolve, 500));

        // 点击特定选项
        const optionSelector = `#period option[value="${targetOption.value}"]`;
        await this.formFiller.currentPage.click(optionSelector);
        this.log('✅ 使用点击方法选择成功');
      }

      // 等待选择生效
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 验证选择结果
      await this.verifyPeriodSelection(targetOption);

      // 截图：选择完成
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('period-selected');
      }

    } catch (error) {
      this.log(`❌ 选择申请期间失败: ${error.message}`);

      // 详细错误调试
      await this.debugPeriodSelectionError();
      throw error;
    }
  }

  /**
   * 验证期间选择结果
   */
  async verifyPeriodSelection(expectedOption) {
    this.log('🔍 验证期间选择结果...');

    try {
      // 获取当前选中的值
      const selectedValue = await this.formFiller.currentPage.$eval('#period', el => el.value);
      const selectedText = await this.formFiller.currentPage.$eval('#period', el => {
        const selectedOption = el.options[el.selectedIndex];
        return selectedOption ? selectedOption.textContent.trim() : '';
      });

      this.log('📊 选择验证结果:');
      this.log(`   期望值: "${expectedOption.value}"`);
      this.log(`   实际值: "${selectedValue}"`);
      this.log(`   期望文本: "${expectedOption.text}"`);
      this.log(`   实际文本: "${selectedText}"`);

      const valueMatch = selectedValue === expectedOption.value;
      const textMatch = selectedText === expectedOption.text;

      if (valueMatch && textMatch) {
        this.log('✅ 期间选择验证完全通过');
      } else if (valueMatch) {
        this.log('✅ 期间选择值匹配（文本可能不同）');
      } else {
        this.log('❌ 期间选择验证失败');
        throw new Error(`选择验证失败: 期望="${expectedOption.value}", 实际="${selectedValue}"`);
      }

    } catch (error) {
      this.log(`❌ 期间选择验证失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 调试期间选择错误
   */
  async debugPeriodSelectionError() {
    this.log('🔍 期间选择错误调试:');

    try {
      // 检查选择框状态
      const selectInfo = await this.formFiller.currentPage.$eval('#period', el => ({
        exists: !!el,
        disabled: el.disabled,
        value: el.value,
        optionsCount: el.options.length,
        selectedIndex: el.selectedIndex
      }));

      this.log('📊 选择框状态:');
      this.log(`   存在: ${selectInfo.exists}`);
      this.log(`   禁用: ${selectInfo.disabled}`);
      this.log(`   当前值: "${selectInfo.value}"`);
      this.log(`   选项数量: ${selectInfo.optionsCount}`);
      this.log(`   选中索引: ${selectInfo.selectedIndex}`);

    } catch (debugError) {
      this.log(`⚠️  期间选择调试失败: ${debugError.message}`);
    }
  }

  /**
   * 点击确认按钮
   */
  async clickConfirmButton() {
    this.log('🔘 点击确认按钮...');

    try {
      // 查找确认按钮
      const confirmButtonSelector = 'body > div.dialog_host.ui-draggable > div > div > form > div.action > button.default';

      this.log(`🔍 等待确认按钮: ${confirmButtonSelector}`);
      await this.formFiller.currentPage.waitForSelector(confirmButtonSelector, {
        timeout: this.options.dialogTimeout
      });

      // 验证按钮状态
      const buttonInfo = await this.formFiller.currentPage.$eval(confirmButtonSelector, el => ({
        exists: !!el,
        disabled: el.disabled,
        visible: el.offsetParent !== null,
        text: el.textContent.trim(),
        className: el.className
      }));

      this.log('📊 确认按钮状态:');
      this.log(`   存在: ${buttonInfo.exists}`);
      this.log(`   禁用: ${buttonInfo.disabled}`);
      this.log(`   可见: ${buttonInfo.visible}`);
      this.log(`   文本: "${buttonInfo.text}"`);
      this.log(`   类名: "${buttonInfo.className}"`);

      if (buttonInfo.disabled) {
        this.log('⚠️  确认按钮被禁用，等待启用...');

        // 等待按钮启用
        await this.formFiller.currentPage.waitForFunction(
          (selector) => {
            const button = document.querySelector(selector);
            return button && !button.disabled;
          },
          { timeout: this.options.dialogTimeout },
          confirmButtonSelector
        );

        this.log('✅ 确认按钮已启用');
      }

      if (!buttonInfo.visible) {
        throw new Error('确认按钮不可见');
      }

      const confirmButton = await this.formFiller.currentPage.$(confirmButtonSelector);
      if (!confirmButton) {
        throw new Error('找不到确认按钮');
      }

      // 截图：点击确认按钮前
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('before-click-confirm');
      }

      this.log('✅ 找到确认按钮，准备点击');

      // 尝试多种点击方法
      let clickSuccess = false;

      // 方法1：普通点击
      try {
        await confirmButton.click();
        this.log('✅ 使用普通点击成功');
        clickSuccess = true;
      } catch (clickError) {
        this.log(`⚠️  普通点击失败: ${clickError.message}`);

        // 方法2：强制点击
        try {
          await confirmButton.click({ force: true });
          this.log('✅ 使用强制点击成功');
          clickSuccess = true;
        } catch (forceClickError) {
          this.log(`⚠️  强制点击失败: ${forceClickError.message}`);

          // 方法3：JavaScript点击
          try {
            await this.formFiller.currentPage.evaluate((selector) => {
              const button = document.querySelector(selector);
              if (button) {
                button.click();
                return true;
              }
              return false;
            }, confirmButtonSelector);
            this.log('✅ 使用JavaScript点击成功');
            clickSuccess = true;
          } catch (jsClickError) {
            this.log(`❌ JavaScript点击失败: ${jsClickError.message}`);
          }
        }
      }

      if (!clickSuccess) {
        throw new Error('所有点击方法都失败了');
      }

      this.log('✅ 确认按钮已点击');

      // 等待对话框消失和页面响应
      this.log('⏳ 等待页面响应...');
      await new Promise(resolve => setTimeout(resolve, this.options.waitAfterClick));

      // 等待页面导航或更新
      try {
        this.log('⏳ 等待页面导航...');
        await this.formFiller.currentPage.waitForNavigation({
          timeout: this.options.dialogTimeout,
          waitUntil: 'domcontentloaded'
        });
        this.log('✅ 页面导航完成');
      } catch (navigationError) {
        this.log(`⚠️  页面导航超时: ${navigationError.message}`);

        // 检查页面是否有变化
        const currentUrl = this.formFiller.currentPage.url();
        this.log(`📍 当前URL: ${currentUrl}`);

        // 继续处理，可能是AJAX更新而非页面导航
        this.log('⚠️  可能是AJAX更新，继续处理');
      }

      // 截图：点击确认按钮后
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('after-click-confirm');
      }

    } catch (error) {
      this.log(`❌ 点击确认按钮失败: ${error.message}`);

      // 详细错误调试
      await this.debugConfirmButtonError();
      throw error;
    }
  }

  /**
   * 调试确认按钮错误
   */
  async debugConfirmButtonError() {
    this.log('🔍 确认按钮错误调试:');

    try {
      // 查找所有可能的按钮
      const allButtons = await this.formFiller.currentPage.$$eval('button', buttons =>
        buttons.map((button, index) => ({
          index: index,
          text: button.textContent.trim(),
          className: button.className,
          disabled: button.disabled,
          visible: button.offsetParent !== null,
          type: button.type
        }))
      );

      this.log('📊 页面上的所有按钮:');
      allButtons.forEach(button => {
        this.log(`   ${button.index + 1}. "${button.text}" [${button.type}] ${button.disabled ? '[禁用]' : ''} ${button.visible ? '[可见]' : '[隐藏]'}`);
        this.log(`      类名: ${button.className}`);
      });

      // 查找对话框中的按钮
      const dialogButtons = await this.formFiller.currentPage.$$eval(
        'body > div.dialog_host.ui-draggable button',
        buttons => buttons.map(button => ({
          text: button.textContent.trim(),
          className: button.className,
          disabled: button.disabled,
          visible: button.offsetParent !== null
        }))
      );

      if (dialogButtons.length > 0) {
        this.log('📊 对话框中的按钮:');
        dialogButtons.forEach((button, index) => {
          this.log(`   ${index + 1}. "${button.text}" ${button.disabled ? '[禁用]' : ''} ${button.visible ? '[可见]' : '[隐藏]'}`);
          this.log(`      类名: ${button.className}`);
        });
      }

    } catch (debugError) {
      this.log(`⚠️  确认按钮调试失败: ${debugError.message}`);
    }
  }

  /**
   * 验证申请启动结果
   */
  async verifyApplicationStart() {
    this.log('🔍 验证申请启动结果...');

    try {
      // 等待页面稳定
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 获取当前页面信息
      const currentUrl = this.formFiller.currentPage.url();
      const pageTitle = await this.formFiller.currentPage.title();
      
      this.log(`📍 当前页面URL: ${currentUrl}`);
      this.log(`📄 页面标题: ${pageTitle}`);

      // 截图：申请启动后页面
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('application-started-page');
      }

      // 获取页面内容
      const pageText = await this.formFiller.currentPage.evaluate(() => {
        return document.body.innerText.substring(0, 1000);
      });

      // 检查申请是否成功启动
      const startSuccess = this.checkApplicationStartSuccess(currentUrl, pageTitle, pageText);
      
      const result = {
        url: currentUrl,
        title: pageTitle,
        textPreview: pageText.substring(0, 300),
        startSuccess: startSuccess.success,
        message: startSuccess.message,
        timestamp: new Date().toISOString()
      };

      if (startSuccess.success) {
        this.log(`✅ 申请启动成功: ${startSuccess.message}`);
      } else {
        this.log(`❌ 申请启动失败: ${startSuccess.message}`);
        throw new Error(`申请启动验证失败: ${startSuccess.message}`);
      }

      return result;

    } catch (error) {
      this.log(`❌ 验证申请启动结果失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 检查内容区域
   */
  async checkContentArea() {
    this.log('🔍 检查内容区域...');

    try {
      const contentSelector = '#content';

      this.log(`🔍 等待内容区域: ${contentSelector}`);
      await this.formFiller.currentPage.waitForSelector(contentSelector, {
        timeout: this.options.dialogTimeout
      });

      const contentArea = await this.formFiller.currentPage.$(contentSelector);
      if (!contentArea) {
        throw new Error('找不到内容区域');
      }

      this.log('✅ 内容区域检查通过');

      // 截图：内容区域
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('content-area-loaded');
      }

    } catch (error) {
      this.log(`❌ 内容区域检查失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 定位申请链接
   */
  async locateApplicationLink() {
    this.log('🔍 定位申请链接...');

    try {
      const linkSelector = '#content > table > tbody > tr > td:nth-child(1) > div > a';

      this.log(`🔍 等待申请链接: ${linkSelector}`);
      await this.formFiller.currentPage.waitForSelector(linkSelector, {
        timeout: this.options.dialogTimeout
      });

      const applicationLink = await this.formFiller.currentPage.$(linkSelector);
      if (!applicationLink) {
        throw new Error('找不到申请链接');
      }

      // 获取链接信息
      const linkInfo = await this.formFiller.currentPage.$eval(linkSelector, el => ({
        text: el.textContent.trim(),
        href: el.href,
        visible: el.offsetParent !== null
      }));

      this.log('📊 申请链接信息:');
      this.log(`   文本: "${linkInfo.text}"`);
      this.log(`   链接: ${linkInfo.href}`);
      this.log(`   可见: ${linkInfo.visible}`);

      if (!linkInfo.visible) {
        throw new Error('申请链接不可见');
      }

      this.log('✅ 申请链接定位成功');

    } catch (error) {
      this.log(`❌ 申请链接定位失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证"In Progress"状态
   */
  async verifyInProgressStatus() {
    this.log('🔍 验证申请状态...');

    try {
      const statusSelector = '#content > table > tbody > tr > td:nth-child(2)';

      this.log(`🔍 等待状态单元格: ${statusSelector}`);
      await this.formFiller.currentPage.waitForSelector(statusSelector, {
        timeout: this.options.dialogTimeout
      });

      const statusCell = await this.formFiller.currentPage.$(statusSelector);
      if (!statusCell) {
        throw new Error('找不到状态单元格');
      }

      // 获取状态文本
      const statusText = await this.formFiller.currentPage.$eval(statusSelector, el =>
        el.textContent.trim()
      );

      // 获取HTML内容以验证具体格式
      const statusHTML = await this.formFiller.currentPage.$eval(statusSelector, el =>
        el.outerHTML
      );

      this.log('📊 申请状态信息:');
      this.log(`   状态文本: "${statusText}"`);
      this.log(`   HTML内容: ${statusHTML}`);

      // 验证状态是否为"In Progress"
      const isInProgress = statusText.toLowerCase().includes('in progress') ||
                          statusText.toLowerCase().includes('进行中') ||
                          statusHTML.includes('<td>In Progress</td>');

      if (isInProgress) {
        this.log('✅ 申请状态验证通过: In Progress');
      } else {
        this.log(`❌ 申请状态不正确: "${statusText}"`);
        throw new Error(`申请状态不是"In Progress": "${statusText}"`);
      }

    } catch (error) {
      this.log(`❌ 申请状态验证失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 点击申请链接
   */
  async clickApplicationLink() {
    this.log('🔘 点击申请链接...');

    try {
      const linkSelector = '#content > table > tbody > tr > td:nth-child(1) > div > a';

      const applicationLink = await this.formFiller.currentPage.$(linkSelector);
      if (!applicationLink) {
        throw new Error('找不到申请链接');
      }

      // 截图：点击申请链接前
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('before-click-application-link');
      }

      this.log('✅ 找到申请链接，准备点击');
      await applicationLink.click();
      this.log('✅ 申请链接已点击');

      // 等待页面响应
      await new Promise(resolve => setTimeout(resolve, this.options.waitAfterClick));

    } catch (error) {
      this.log(`❌ 点击申请链接失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 处理申请详情对话框
   */
  async handleApplicationDetailsDialog() {
    this.log('🔍 处理申请详情对话框...');

    try {
      const dialogSelector = 'body > div.dialog_host.ui-draggable > div > div > form';

      this.log(`🔍 等待申请详情对话框: ${dialogSelector}`);
      const dialogExists = await this.formFiller.currentPage.waitForSelector(dialogSelector, {
        timeout: this.options.dialogTimeout
      }).then(() => true).catch(() => false);

      if (!dialogExists) {
        this.log('⚠️  未检测到申请详情对话框，可能直接进入表单');
        return;
      }

      this.log('✅ 检测到申请详情对话框');

      // 截图：申请详情对话框
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('application-details-dialog');
      }

      // 点击确认/打开按钮
      await this.clickApplicationDetailsConfirm();

    } catch (error) {
      this.log(`❌ 处理申请详情对话框失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 点击申请详情确认按钮
   */
  async clickApplicationDetailsConfirm() {
    this.log('🔘 点击申请详情确认按钮...');

    try {
      const confirmButtonSelector = 'body > div.dialog_host.ui-draggable > div > div > form > div.action > button.default';

      await this.formFiller.currentPage.waitForSelector(confirmButtonSelector, {
        timeout: this.options.dialogTimeout
      });

      const confirmButton = await this.formFiller.currentPage.$(confirmButtonSelector);
      if (!confirmButton) {
        throw new Error('找不到申请详情确认按钮');
      }

      this.log('✅ 找到申请详情确认按钮，准备点击');
      await confirmButton.click();
      this.log('✅ 申请详情确认按钮已点击');

      // 等待页面响应
      await new Promise(resolve => setTimeout(resolve, this.options.waitAfterClick));

      // 等待页面导航
      try {
        await this.formFiller.currentPage.waitForNavigation({
          timeout: this.options.dialogTimeout,
          waitUntil: 'domcontentloaded'
        });
        this.log('✅ 页面导航完成');
      } catch (error) {
        this.log('⚠️  页面导航超时，但继续处理');
      }

    } catch (error) {
      this.log(`❌ 点击申请详情确认按钮失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证申请表单加载
   */
  async verifyApplicationFormLoaded() {
    this.log('🔍 验证申请表单加载...');

    try {
      // 等待页面稳定
      await new Promise(resolve => setTimeout(resolve, 3000));

      const currentUrl = this.formFiller.currentPage.url();
      const pageTitle = await this.formFiller.currentPage.title();

      this.log(`📍 当前页面URL: ${currentUrl}`);
      this.log(`📄 页面标题: ${pageTitle}`);

      // 截图：申请表单页面
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('application-form-loaded');
      }

      // 检查是否成功加载申请表单
      const formLoaded = this.checkApplicationFormLoaded(currentUrl, pageTitle);

      if (formLoaded.success) {
        this.log(`✅ 申请表单加载成功: ${formLoaded.message}`);
      } else {
        this.log(`❌ 申请表单加载失败: ${formLoaded.message}`);
        throw new Error(`申请表单加载验证失败: ${formLoaded.message}`);
      }

    } catch (error) {
      this.log(`❌ 验证申请表单加载失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 检查申请表单是否加载
   */
  checkApplicationFormLoaded(url, title) {
    // 成功指标
    const successIndicators = [
      url.includes('/application/'),
      url.includes('/form/'),
      url.includes('/frm'),
      title.toLowerCase().includes('application'),
      title.toLowerCase().includes('form'),
      !url.includes('/login'),
      !url.includes('/error')
    ];

    const successCount = successIndicators.filter(Boolean).length;

    if (successCount >= 2) {
      return {
        success: true,
        message: `检测到${successCount}个成功指标`
      };
    }

    if (url.includes('/application/') || url.includes('frm')) {
      return {
        success: true,
        message: '已进入申请表单页面'
      };
    }

    return {
      success: false,
      message: '无法确认申请表单加载状态'
    };
  }

  /**
   * 步骤5: 处理Nondegree申请说明页面
   */
  async step5_HandleNondegreeInstructions() {
    this.log('\n📋 步骤5: 处理Nondegree申请说明页面');

    try {
      // 导航到页面
      await this.navigateToMenuPage(3, 'Nondegree Application Instructions');

      // 检测页面完成状态
      const completionStatus = await this.detectPageCompletionStatus('Nondegree Application Instructions');

      if (completionStatus.isCompleted) {
        this.log('✅ Nondegree申请说明页面已完成，跳过处理步骤');
        return;
      }

      // 点击完成按钮
      await this.clickPageCompletionButton('Nondegree Application Instructions');

      this.log('✅ 步骤5完成: Nondegree申请说明页面已处理');

    } catch (error) {
      this.log(`❌ 步骤5失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 步骤6: 处理申请学期页面
   */
  async step6_HandleApplicationTerm() {
    this.log('\n📋 步骤6: 处理申请学期页面');

    try {
      // 导航到页面
      await this.navigateToMenuPage(4, 'Application Term');

      // 检测页面完成状态
      const completionStatus = await this.detectPageCompletionStatus('Application Term');

      if (completionStatus.isCompleted) {
        this.log('✅ 申请学期页面已完成，跳过处理步骤');
        return;
      }

      // 检查申请学期选择是否已填写
      const termSelected = await this.checkFormElementSelected('#form_6c9860b5-6412-4e1c-a82b-6dc0bfa77ca7_1');

      if (!termSelected) {
        // 点击表单选择元素
        await this.clickFormElement('#form_6c9860b5-6412-4e1c-a82b-6dc0bfa77ca7_1', '申请学期选择');
      } else {
        this.log('ℹ️  申请学期已选择，跳过选择步骤');
      }

      // 点击完成按钮
      await this.clickPageCompletionButton('Application Term');

      this.log('✅ 步骤6完成: 申请学期页面已处理');

    } catch (error) {
      this.log(`❌ 步骤6失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 步骤7: 处理个人背景页面
   */
  async step7_HandlePersonalBackground() {
    this.log('\n📋 步骤7: 处理个人背景页面');

    try {
      // 导航到页面
      await this.navigateToMenuPage(5, 'Personal Background');

      // 检测页面完成状态
      const completionStatus = await this.detectPageCompletionStatus('Personal Background');

      if (completionStatus.isCompleted) {
        this.log('✅ 个人背景页面已完成，跳过处理步骤');
        return;
      }

      // 智能填写地址信息（检查是否已填写）
      await this.smartFillAddressInformation(completionStatus);

      // 点击邮寄地址链接（条件性）
      await this.clickMailingAddressLink();

      // 再次检查地址验证对话框（邮寄地址操作后可能触发）
      await this.handleAddressValidationDialog();

      // 智能填写个人信息（检查是否已填写）
      await this.smartFillPersonalInformation(completionStatus);

      // 智能选择种族和民族信息（检查是否已选择）
      await this.smartSelectEthnicityAndRace(completionStatus);

      // 点击完成按钮（Personal Background页面需要地址验证检查）
      await this.clickPageCompletionButton('Personal Background');

      this.log('✅ 步骤7完成: 个人背景页面已处理');

    } catch (error) {
      this.log(`❌ 步骤7失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 导航到菜单页面
   */
  async navigateToMenuPage(childIndex, expectedTitle) {
    this.log(`🌐 导航到菜单项 ${childIndex}: ${expectedTitle}`);

    try {
      const menuSelector = `#menu > ul > li:nth-child(${childIndex}) > a`;

      this.log(`🔍 等待菜单项: ${menuSelector}`);
      await this.formFiller.currentPage.waitForSelector(menuSelector, {
        timeout: this.options.dialogTimeout
      });

      const menuItem = await this.formFiller.currentPage.$(menuSelector);
      if (!menuItem) {
        throw new Error(`找不到菜单项: ${menuSelector}`);
      }

      // 获取菜单项信息
      const menuInfo = await this.formFiller.currentPage.$eval(menuSelector, el => ({
        text: el.textContent.trim(),
        href: el.href,
        visible: el.offsetParent !== null
      }));

      this.log('📊 菜单项信息:');
      this.log(`   文本: "${menuInfo.text}"`);
      this.log(`   链接: ${menuInfo.href}`);
      this.log(`   可见: ${menuInfo.visible}`);

      // 截图：点击菜单前
      if (this.options.enableScreenshots) {
        await this.takeScreenshot(`before-menu-${childIndex}`);
      }

      // 点击菜单项
      await menuItem.click();
      this.log(`✅ 菜单项 ${childIndex} 已点击`);

      // 等待页面加载
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 验证页面标题
      await this.verifyPageTitle(expectedTitle);

      // 截图：页面加载后
      if (this.options.enableScreenshots) {
        await this.takeScreenshot(`page-loaded-${childIndex}`);
      }

    } catch (error) {
      this.log(`❌ 导航到菜单页面失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证页面标题
   */
  async verifyPageTitle(expectedTitle) {
    this.log(`🔍 验证页面标题包含: ${expectedTitle}`);

    try {
      const pageTitle = await this.formFiller.currentPage.title();
      this.log(`📄 当前页面标题: ${pageTitle}`);

      if (pageTitle.toLowerCase().includes(expectedTitle.toLowerCase())) {
        this.log('✅ 页面标题验证通过');
      } else {
        this.log(`⚠️  页面标题不匹配，但继续处理`);
      }

    } catch (error) {
      this.log(`⚠️  页面标题验证失败: ${error.message}`);
    }
  }

  /**
   * 点击表单元素
   */
  async clickFormElement(selector, description) {
    this.log(`🔘 点击表单元素: ${description}`);
    this.log(`   选择器: ${selector}`);

    try {
      await this.formFiller.currentPage.waitForSelector(selector, {
        timeout: this.options.dialogTimeout
      });

      const element = await this.formFiller.currentPage.$(selector);
      if (!element) {
        throw new Error(`找不到表单元素: ${selector}`);
      }

      // 验证元素状态
      const elementInfo = await this.formFiller.currentPage.$eval(selector, el => ({
        visible: el.offsetParent !== null,
        disabled: el.disabled,
        text: el.textContent.trim()
      }));

      this.log('📊 表单元素信息:');
      this.log(`   可见: ${elementInfo.visible}`);
      this.log(`   禁用: ${elementInfo.disabled}`);
      this.log(`   文本: "${elementInfo.text}"`);

      if (!elementInfo.visible) {
        throw new Error('表单元素不可见');
      }

      if (elementInfo.disabled) {
        throw new Error('表单元素被禁用');
      }

      // 点击元素
      await element.click();
      this.log(`✅ ${description} 已点击`);

      // 等待选择生效
      await new Promise(resolve => setTimeout(resolve, 1000));

    } catch (error) {
      this.log(`❌ 点击表单元素失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 点击页面完成按钮
   */
  async clickPageCompletionButton(pageContext = null) {
    this.log('🔘 点击页面完成按钮...');

    try {
      const buttonSelector = '#main > form > div.action > button';

      await this.formFiller.currentPage.waitForSelector(buttonSelector, {
        timeout: this.options.dialogTimeout
      });

      const button = await this.formFiller.currentPage.$(buttonSelector);
      if (!button) {
        throw new Error('找不到完成按钮');
      }

      // 截图：点击完成按钮前
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('before-page-completion');
      }

      await button.click();
      this.log('✅ 页面完成按钮已点击');

      // 等待页面响应
      await new Promise(resolve => setTimeout(resolve, this.options.waitAfterClick));

      // 只在Personal Background页面检查地址验证对话框
      if (pageContext === 'Personal Background') {
        this.log('🔍 Personal Background页面 - 检查地址验证对话框...');
        await this.handleAddressValidationDialog();
      } else {
        this.log(`ℹ️  ${pageContext || '当前页面'} - 跳过地址验证检查`);
      }

      // 等待导航完成
      try {
        await this.formFiller.currentPage.waitForNavigation({
          timeout: this.options.dialogTimeout,
          waitUntil: 'domcontentloaded'
        });
        this.log('✅ 页面导航完成');
      } catch (error) {
        this.log('⚠️  页面导航超时，但继续处理');
      }

    } catch (error) {
      this.log(`❌ 点击页面完成按钮失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 步骤8: 处理背景信息续页
   */
  async step8_HandleBackgroundContinued() {
    this.log('\n📋 步骤8: 处理背景信息续页');

    try {
      // 导航到页面
      await this.navigateToMenuPage(6, 'Background Continued');

      // 检测页面完成状态
      const completionStatus = await this.detectPageCompletionStatus('Background Continued');

      if (completionStatus.isCompleted) {
        this.log('✅ 背景信息续页已完成，跳过处理步骤');
        return;
      }

      // 强制填写Background Continued页面（确保执行）
      this.log('🔄 强制执行Background Continued页面填写...');

      // 选择语言：English
      await this.selectLanguage('English');

      // 根据性别选择代词
      const gender = this.studentData.gender || this.studentData['性别'];
      if (gender) {
        const pronoun = gender.toLowerCase().includes('male') || gender.toLowerCase() === 'm' ? 'He/Him' : 'She/Her';
        await this.selectPronounOption(pronoun);
      }

      // 根据性别选择性别认知
      if (gender) {
        const genderIdentity = gender.toLowerCase().includes('male') || gender.toLowerCase() === 'm' ? 'Man' : 'Woman';
        await this.selectGenderIdentityOption(genderIdentity);
      }

      // 选择性取向：Asexual
      await this.selectSexualOrientation('Asexual');

      // 点击完成按钮
      await this.clickPageCompletionButton('Background Continued');

      this.log('✅ 步骤8完成: 背景信息续页已处理');

    } catch (error) {
      this.log(`❌ 步骤8失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 选择语言
   */
  async selectLanguage() {
    this.log('🌐 选择语言: English');

    try {
      const languageSelector = '#form_f0cd62fe-6c76-4421-b049-a27bf3ce1ff3';

      await this.formFiller.currentPage.waitForSelector(languageSelector, {
        timeout: this.options.dialogTimeout
      });

      // 获取所有语言选项
      const options = await this.formFiller.currentPage.$$eval(`${languageSelector} option`, options =>
        options.map(option => ({
          value: option.value,
          text: option.textContent.trim()
        }))
      );

      this.log('📋 可用语言选项:');
      options.forEach((option, index) => {
        this.log(`   ${index + 1}. ${option.text} (值: ${option.value})`);
      });

      // 查找English选项
      const englishOption = options.find(option =>
        option.text.toLowerCase().includes('english') ||
        option.value.toLowerCase().includes('english')
      );

      if (englishOption) {
        await this.formFiller.currentPage.select(languageSelector, englishOption.value);
        this.log(`✅ 语言选择成功: ${englishOption.text}`);
      } else {
        // 如果没找到English，使用第一个可用选项
        const availableOptions = options.filter(option => option.value && option.value.trim() !== '');
        if (availableOptions.length > 0) {
          await this.formFiller.currentPage.select(languageSelector, availableOptions[0].value);
          this.log(`⚠️  未找到English选项，使用: ${availableOptions[0].text}`);
        } else {
          throw new Error('没有可用的语言选项');
        }
      }

    } catch (error) {
      this.log(`❌ 选择语言失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 选择代词
   */
  async selectPronouns() {
    this.log('👤 选择代词...');

    try {
      const gender = this.studentData.gender || this.studentData['性别'];

      if (!gender) {
        this.log('⚠️  学生数据中没有性别信息，跳过代词选择');
        return;
      }

      this.log(`📊 学生性别: ${gender}`);

      // 性别到代词的映射
      const pronounMapping = {
        'Male': 'He/Him',
        'male': 'He/Him',
        'M': 'He/Him',
        '男': 'He/Him',
        'Female': 'She/Her',
        'female': 'She/Her',
        'F': 'She/Her',
        '女': 'She/Her'
      };

      const targetPronoun = pronounMapping[gender];

      if (!targetPronoun) {
        this.log(`⚠️  未知性别类型: ${gender}，使用默认代词 He/Him`);
        await this.selectPronounOption('He/Him');
      } else {
        this.log(`🎯 目标代词: ${targetPronoun}`);
        await this.selectPronounOption(targetPronoun);
      }

    } catch (error) {
      this.log(`❌ 选择代词失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 选择代词选项
   */
  async selectPronounOption(targetPronoun) {
    this.log(`🔘 选择代词选项: ${targetPronoun}`);

    try {
      // 根据Background Continued文件的实际选择器
      const pronounSelectors = {
        'He/Him': '#form_d1335bf7-8685-41fe-8da2-a3ae35b14455_1',
        'She/Her': '#form_d1335bf7-8685-41fe-8da2-a3ae35b14455_2',
        'They/Them': '#form_d1335bf7-8685-41fe-8da2-a3ae35b14455_3'
      };

      const targetSelector = pronounSelectors[targetPronoun];

      if (targetSelector) {
        this.log(`🎯 使用精确选择器: ${targetSelector}`);

        await this.formFiller.currentPage.waitForSelector(targetSelector, {
          timeout: this.options.dialogTimeout
        });

        const element = await this.formFiller.currentPage.$(targetSelector);
        if (!element) {
          throw new Error(`找不到代词选择器: ${targetSelector}`);
        }

        // 检查元素状态
        const isVisible = await element.evaluate(el => el.offsetParent !== null);
        const isDisabled = await element.evaluate(el => el.disabled);

        this.log(`📊 代词选项状态: 可见=${isVisible}, 禁用=${isDisabled}`);

        if (!isVisible) {
          throw new Error('代词选项不可见');
        }

        if (isDisabled) {
          throw new Error('代词选项被禁用');
        }

        await element.click();
        this.log(`✅ 代词选择完成: ${targetPronoun}`);
        return;
      }

      // 回退到通用查找方法
      this.log('🔍 使用通用查找方法...');
      const allCheckboxes = await this.formFiller.currentPage.$$('input[type="checkbox"]');

      for (const checkbox of allCheckboxes) {
        const dataText = await checkbox.evaluate(el => el.getAttribute('data-text'));
        const label = await this.getPronounLabel(checkbox);

        this.log(`🔍 检查复选框: data-text="${dataText}", 标签="${label}"`);

        if (dataText === targetPronoun || label.includes(targetPronoun)) {
          await checkbox.click();
          this.log(`✅ 通过通用方法找到代词选项: ${dataText || label}`);
          return;
        }
      }

      throw new Error(`未找到代词选项: ${targetPronoun}`);

    } catch (error) {
      this.log(`❌ 选择代词选项失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取代词标签
   */
  async getPronounLabel(radioElement) {
    try {
      // 尝试多种方式获取标签文本
      const label = await this.formFiller.currentPage.evaluate((radio) => {
        // 方法1: 通过for属性关联的label
        if (radio.id) {
          const label = document.querySelector(`label[for="${radio.id}"]`);
          if (label) return label.textContent.trim();
        }

        // 方法2: 父级label
        const parentLabel = radio.closest('label');
        if (parentLabel) return parentLabel.textContent.trim();

        // 方法3: 相邻的文本节点
        const nextSibling = radio.nextSibling;
        if (nextSibling && nextSibling.nodeType === Node.TEXT_NODE) {
          return nextSibling.textContent.trim();
        }

        // 方法4: 相邻的元素
        const nextElement = radio.nextElementSibling;
        if (nextElement) return nextElement.textContent.trim();

        return '';
      }, radioElement);

      return label || '';
    } catch (error) {
      return '';
    }
  }

  /**
   * 选择性别认知
   */
  async selectGenderIdentity() {
    this.log('🏳️‍⚧️ 选择性别认知...');

    try {
      const gender = this.studentData.gender || this.studentData['性别'];

      if (!gender) {
        this.log('⚠️  学生数据中没有性别信息，跳过性别认知选择');
        return;
      }

      // 性别到性别认知的映射
      const genderIdentityMapping = {
        'Male': 'Man',
        'male': 'Man',
        'M': 'Man',
        '男': 'Man',
        'Female': 'Woman',
        'female': 'Woman',
        'F': 'Woman',
        '女': 'Woman'
      };

      const targetIdentity = genderIdentityMapping[gender];

      if (!targetIdentity) {
        this.log(`⚠️  未知性别类型: ${gender}，使用默认性别认知 Man`);
        await this.selectGenderIdentityOption('Man');
      } else {
        this.log(`🎯 目标性别认知: ${targetIdentity}`);
        await this.selectGenderIdentityOption(targetIdentity);
      }

    } catch (error) {
      this.log(`❌ 选择性别认知失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 选择性别认知选项
   */
  async selectGenderIdentityOption(targetIdentity) {
    this.log(`🔘 选择性别认知选项: ${targetIdentity}`);

    try {
      // 根据Background Continued文件的实际选择器
      const genderSelectors = {
        'Man': '#form_143994f8-37a6-42ca-9e2a-4029ae774a38_1',
        'Woman': '#form_143994f8-37a6-42ca-9e2a-4029ae774a38_4',
        'Non-binary': '#form_143994f8-37a6-42ca-9e2a-4029ae774a38_2',
        'Transgender': '#form_143994f8-37a6-42ca-9e2a-4029ae774a38_3'
      };

      const targetSelector = genderSelectors[targetIdentity];

      if (targetSelector) {
        this.log(`🎯 使用精确选择器: ${targetSelector}`);

        await this.formFiller.currentPage.waitForSelector(targetSelector, {
          timeout: this.options.dialogTimeout
        });

        const element = await this.formFiller.currentPage.$(targetSelector);
        if (!element) {
          throw new Error(`找不到性别认知选择器: ${targetSelector}`);
        }

        // 检查元素状态
        const isVisible = await element.evaluate(el => el.offsetParent !== null);
        const isDisabled = await element.evaluate(el => el.disabled);

        this.log(`📊 性别认知选项状态: 可见=${isVisible}, 禁用=${isDisabled}`);

        if (!isVisible) {
          throw new Error('性别认知选项不可见');
        }

        if (isDisabled) {
          throw new Error('性别认知选项被禁用');
        }

        await element.click();
        this.log(`✅ 性别认知选择完成: ${targetIdentity}`);
        return;
      }

      // 回退到通用查找方法
      this.log('🔍 使用通用查找方法...');
      const allCheckboxes = await this.formFiller.currentPage.$$('input[type="checkbox"]');

      for (const checkbox of allCheckboxes) {
        const dataText = await checkbox.evaluate(el => el.getAttribute('data-text'));
        const label = await this.getGenderIdentityLabel(checkbox);

        this.log(`🔍 检查性别认知复选框: data-text="${dataText}", 标签="${label}"`);

        if (dataText === targetIdentity || label.toLowerCase().includes(targetIdentity.toLowerCase())) {
          await checkbox.click();
          this.log(`✅ 通过通用方法找到性别认知选项: ${dataText || label}`);
          return;
        }
      }

      throw new Error(`未找到性别认知选项: ${targetIdentity}`);

    } catch (error) {
      this.log(`❌ 选择性别认知选项失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取性别认知标签
   */
  async getGenderIdentityLabel(radioElement) {
    try {
      const label = await this.formFiller.currentPage.evaluate((radio) => {
        if (radio.id) {
          const label = document.querySelector(`label[for="${radio.id}"]`);
          if (label) return label.textContent.trim();
        }

        const parentLabel = radio.closest('label');
        if (parentLabel) return parentLabel.textContent.trim();

        const nextSibling = radio.nextSibling;
        if (nextSibling && nextSibling.nodeType === Node.TEXT_NODE) {
          return nextSibling.textContent.trim();
        }

        const nextElement = radio.nextElementSibling;
        if (nextElement) return nextElement.textContent.trim();

        return '';
      }, radioElement);

      return label || '';
    } catch (error) {
      return '';
    }
  }

  /**
   * 选择性取向
   */
  async selectSexualOrientation() {
    this.log('🏳️‍🌈 选择性取向: Asexual');

    try {
      // 根据Background Continued文件的实际选择器
      const asexualSelector = '#form_34f11396-96b6-4768-8933-784747f5ce3d_1';

      this.log(`🎯 使用精确选择器: ${asexualSelector}`);

      await this.formFiller.currentPage.waitForSelector(asexualSelector, {
        timeout: this.options.dialogTimeout
      });

      const element = await this.formFiller.currentPage.$(asexualSelector);
      if (!element) {
        throw new Error(`找不到Asexual选择器: ${asexualSelector}`);
      }

      // 检查元素状态
      const isVisible = await element.evaluate(el => el.offsetParent !== null);
      const isDisabled = await element.evaluate(el => el.disabled);
      const dataText = await element.evaluate(el => el.getAttribute('data-text'));

      this.log(`📊 Asexual选项状态: 可见=${isVisible}, 禁用=${isDisabled}, data-text="${dataText}"`);

      if (!isVisible) {
        throw new Error('Asexual选项不可见');
      }

      if (isDisabled) {
        throw new Error('Asexual选项被禁用');
      }

      await element.click();
      this.log('✅ 性取向选择完成: Asexual');
      return;

    } catch (error) {
      this.log(`⚠️  精确选择器失败，尝试通用方法: ${error.message}`);

      try {
        // 回退到通用查找方法
        this.log('🔍 使用通用查找方法...');
        const allCheckboxes = await this.formFiller.currentPage.$$('input[type="checkbox"]');

        for (const checkbox of allCheckboxes) {
          const dataText = await checkbox.evaluate(el => el.getAttribute('data-text'));
          const label = await this.getSexualOrientationLabel(checkbox);

          this.log(`🔍 检查性取向复选框: data-text="${dataText}", 标签="${label}"`);

          if (dataText === 'Asexual' || label.toLowerCase().includes('asexual')) {
            await checkbox.click();
            this.log(`✅ 通过通用方法找到Asexual选项: ${dataText || label}`);
            return;
          }
        }

        // 显示所有可能的性取向选项
        this.log('⚠️  未找到Asexual选项，显示所有性取向选项...');
        for (const checkbox of allCheckboxes) {
          const dataText = await checkbox.evaluate(el => el.getAttribute('data-text'));
          const name = await checkbox.evaluate(el => el.name);

          if (name && name.includes('34f11396-96b6-4768-8933-784747f5ce3d')) {
            this.log(`📋 性取向选项: "${dataText}"`);
          }
        }

        throw new Error('未找到Asexual性取向选项');

      } catch (fallbackError) {
        this.log(`❌ 选择性取向失败: ${fallbackError.message}`);
        throw fallbackError;
      }
    }
  }

  /**
   * 获取性取向标签
   */
  async getSexualOrientationLabel(radioElement) {
    try {
      const label = await this.formFiller.currentPage.evaluate((radio) => {
        if (radio.id) {
          const label = document.querySelector(`label[for="${radio.id}"]`);
          if (label) return label.textContent.trim();
        }

        const parentLabel = radio.closest('label');
        if (parentLabel) return parentLabel.textContent.trim();

        const nextSibling = radio.nextSibling;
        if (nextSibling && nextSibling.nodeType === Node.TEXT_NODE) {
          return nextSibling.textContent.trim();
        }

        const nextElement = radio.nextElementSibling;
        if (nextElement) return nextElement.textContent.trim();

        return '';
      }, radioElement);

      return label || '';
    } catch (error) {
      return '';
    }
  }

  /**
   * 检测页面完成状态
   */
  async detectPageCompletionStatus(pageTitle) {
    this.log(`🔍 检测页面完成状态: ${pageTitle}`);

    try {
      const completionStatus = {
        isCompleted: false,
        isPartiallyFilled: false,
        completionIndicators: [],
        filledFields: [],
        emptyFields: [],
        reasoning: ''
      };

      // 1. 检查页面级完成指标
      const pageIndicators = await this.checkPageCompletionIndicators();
      completionStatus.completionIndicators = pageIndicators;

      // 2. 检查表单字段状态
      const fieldStatus = await this.checkFormFieldsStatus();
      completionStatus.filledFields = fieldStatus.filled;
      completionStatus.emptyFields = fieldStatus.empty;

      // 3. 检查提交按钮状态
      const buttonStatus = await this.checkSubmitButtonStatus();

      // 4. 分析完成状态
      const analysis = this.analyzeCompletionStatus(pageIndicators, fieldStatus, buttonStatus);
      completionStatus.isCompleted = analysis.isCompleted;
      completionStatus.isPartiallyFilled = analysis.isPartiallyFilled;
      completionStatus.reasoning = analysis.reasoning;

      // 5. 记录检测结果
      this.logCompletionStatus(pageTitle, completionStatus);

      return completionStatus;

    } catch (error) {
      this.log(`⚠️  页面完成状态检测失败: ${error.message}`);
      return {
        isCompleted: false,
        isPartiallyFilled: false,
        completionIndicators: [],
        filledFields: [],
        emptyFields: [],
        reasoning: '检测失败，默认为未完成'
      };
    }
  }

  /**
   * 检查页面完成指标
   */
  async checkPageCompletionIndicators() {
    this.log('🔍 检查页面完成指标...');

    const indicators = [];

    try {
      // 检查完成状态的CSS类
      const completedClasses = await this.formFiller.currentPage.$$eval('*', elements => {
        const classes = [];
        elements.forEach(el => {
          if (el.className && typeof el.className === 'string') {
            const classList = el.className.toLowerCase();
            if (classList.includes('completed') ||
                classList.includes('finished') ||
                classList.includes('done') ||
                classList.includes('submitted')) {
              classes.push({
                element: el.tagName,
                class: el.className,
                text: el.textContent.trim().substring(0, 50)
              });
            }
          }
        });
        return classes;
      });

      if (completedClasses.length > 0) {
        indicators.push({
          type: 'css_class',
          description: '找到完成状态的CSS类',
          details: completedClasses
        });
      }

      // 检查进度指标
      const progressIndicators = await this.formFiller.currentPage.$$eval('*', elements => {
        const progress = [];
        elements.forEach(el => {
          const text = el.textContent.toLowerCase();
          if (text.includes('✓') || text.includes('✔') ||
              text.includes('complete') || text.includes('finished')) {
            progress.push({
              element: el.tagName,
              text: el.textContent.trim().substring(0, 50)
            });
          }
        });
        return progress;
      });

      if (progressIndicators.length > 0) {
        indicators.push({
          type: 'progress_indicator',
          description: '找到进度完成指标',
          details: progressIndicators
        });
      }

      // 检查导航状态
      const navigationStatus = await this.checkNavigationStatus();
      if (navigationStatus.hasActiveIndicator) {
        indicators.push({
          type: 'navigation_status',
          description: '导航显示当前页面状态',
          details: navigationStatus
        });
      }

    } catch (error) {
      this.log(`⚠️  检查页面完成指标失败: ${error.message}`);
    }

    return indicators;
  }

  /**
   * 检查表单字段状态
   */
  async checkFormFieldsStatus() {
    this.log('📝 检查表单字段状态...');

    const fieldStatus = {
      filled: [],
      empty: [],
      total: 0
    };

    try {
      // 检查输入字段
      const inputFields = await this.formFiller.currentPage.$$eval('input', inputs => {
        return inputs.map(input => ({
          type: input.type,
          name: input.name || input.id,
          value: input.value,
          checked: input.checked,
          disabled: input.disabled,
          required: input.required,
          selector: input.id ? `#${input.id}` : `input[name="${input.name}"]`
        }));
      });

      // 检查选择框
      const selectFields = await this.formFiller.currentPage.$$eval('select', selects => {
        return selects.map(select => ({
          name: select.name || select.id,
          selectedIndex: select.selectedIndex,
          value: select.value,
          disabled: select.disabled,
          required: select.required,
          optionsCount: select.options.length,
          selector: select.id ? `#${select.id}` : `select[name="${select.name}"]`
        }));
      });

      // 检查文本区域
      const textareaFields = await this.formFiller.currentPage.$$eval('textarea', textareas => {
        return textareas.map(textarea => ({
          name: textarea.name || textarea.id,
          value: textarea.value,
          disabled: textarea.disabled,
          required: textarea.required,
          selector: textarea.id ? `#${textarea.id}` : `textarea[name="${textarea.name}"]`
        }));
      });

      // 分析字段状态
      [...inputFields, ...selectFields, ...textareaFields].forEach(field => {
        fieldStatus.total++;

        const isFilled = this.isFieldFilledByData(field);

        if (isFilled) {
          fieldStatus.filled.push({
            selector: field.selector,
            type: field.type || 'select',
            value: field.value,
            name: field.name
          });
        } else {
          fieldStatus.empty.push({
            selector: field.selector,
            type: field.type || 'select',
            name: field.name,
            required: field.required
          });
        }
      });

    } catch (error) {
      this.log(`⚠️  检查表单字段状态失败: ${error.message}`);
    }

    return fieldStatus;
  }

  /**
   * 判断字段是否已填写
   */
  isFieldFilled(field) {
    // 单选框和复选框
    if (field.type === 'radio' || field.type === 'checkbox') {
      return field.checked;
    }

    // 选择框
    if (field.selectedIndex !== undefined) {
      return field.selectedIndex > 0; // 0通常是默认的空选项
    }

    // 文本字段
    if (field.value !== undefined) {
      return field.value.trim().length > 0;
    }

    return false;
  }

  /**
   * 检查提交按钮状态
   */
  async checkSubmitButtonStatus() {
    this.log('🔘 检查提交按钮状态...');

    try {
      const buttonStatus = await this.formFiller.currentPage.$$eval('button, input[type="submit"]', buttons => {
        return buttons.map(button => ({
          text: button.textContent.trim(),
          disabled: button.disabled,
          type: button.type,
          className: button.className,
          visible: button.offsetParent !== null
        }));
      });

      return {
        buttons: buttonStatus,
        hasDisabledSubmit: buttonStatus.some(btn => btn.disabled && btn.type === 'submit'),
        hasCompletedIndicator: buttonStatus.some(btn =>
          btn.text.toLowerCase().includes('completed') ||
          btn.text.toLowerCase().includes('submitted')
        )
      };

    } catch (error) {
      this.log(`⚠️  检查提交按钮状态失败: ${error.message}`);
      return { buttons: [], hasDisabledSubmit: false, hasCompletedIndicator: false };
    }
  }

  /**
   * 检查导航状态
   */
  async checkNavigationStatus() {
    this.log('🧭 检查导航状态...');

    try {
      const navigationInfo = await this.formFiller.currentPage.$$eval('#menu li', menuItems => {
        return menuItems.map((item, index) => ({
          index: index,
          text: item.textContent.trim(),
          className: item.className,
          isActive: item.classList.contains('active') || item.classList.contains('current'),
          isCompleted: item.classList.contains('completed') || item.classList.contains('done'),
          hasCheckmark: item.textContent.includes('✓') || item.textContent.includes('✔')
        }));
      });

      return {
        menuItems: navigationInfo,
        hasActiveIndicator: navigationInfo.some(item => item.isActive || item.isCompleted || item.hasCheckmark)
      };

    } catch (error) {
      this.log(`⚠️  检查导航状态失败: ${error.message}`);
      return { menuItems: [], hasActiveIndicator: false };
    }
  }

  /**
   * 分析完成状态
   */
  analyzeCompletionStatus(pageIndicators, fieldStatus, buttonStatus) {
    this.log('🧠 分析页面完成状态...');

    let isCompleted = false;
    let isPartiallyFilled = false;
    let reasoning = '';

    // 计算填写比例
    const fillRatio = fieldStatus.total > 0 ? fieldStatus.filled.length / fieldStatus.total : 0;

    // 判断逻辑 - 更严格的完成检测
    if (pageIndicators.length > 0 && fillRatio > 0.7) {
      isCompleted = true;
      reasoning = `检测到${pageIndicators.length}个完成指标且填写率${Math.round(fillRatio * 100)}%`;
    } else if (buttonStatus.hasCompletedIndicator) {
      isCompleted = true;
      reasoning = '提交按钮显示已完成状态';
    } else if (buttonStatus.hasDisabledSubmit && fillRatio > 0.8) {
      isCompleted = true;
      reasoning = '提交按钮被禁用且大部分字段已填写';
    } else if (fillRatio >= 0.9) {
      isCompleted = true;
      reasoning = `表单填写完成度${Math.round(fillRatio * 100)}%`;
    } else if (fillRatio >= 0.3) {
      isPartiallyFilled = true;
      reasoning = `表单部分填写，完成度${Math.round(fillRatio * 100)}%`;
    } else {
      reasoning = `表单基本为空，完成度${Math.round(fillRatio * 100)}%`;
    }

    return { isCompleted, isPartiallyFilled, reasoning };
  }

  /**
   * 记录完成状态
   */
  logCompletionStatus(pageTitle, status) {
    this.log(`📊 页面完成状态分析: ${pageTitle}`);
    this.log(`   完成状态: ${status.isCompleted ? '✅ 已完成' : '❌ 未完成'}`);
    this.log(`   部分填写: ${status.isPartiallyFilled ? '⚠️  是' : '❌ 否'}`);
    this.log(`   分析原因: ${status.reasoning}`);
    this.log(`   已填字段: ${status.filledFields.length}个`);
    this.log(`   空白字段: ${status.emptyFields.length}个`);
    this.log(`   完成指标: ${status.completionIndicators.length}个`);

    if (status.filledFields.length > 0) {
      this.log('📝 已填写字段:');
      status.filledFields.slice(0, 5).forEach(field => {
        this.log(`   ${field.name || field.selector}: "${field.value}"`);
      });
      if (status.filledFields.length > 5) {
        this.log(`   ... 还有${status.filledFields.length - 5}个字段`);
      }
    }
  }

  /**
   * 检查表单元素是否已选择
   */
  async checkFormElementSelected(selector) {
    try {
      const element = await this.formFiller.currentPage.$(selector);
      if (!element) {
        return false;
      }

      const isSelected = await element.evaluate(el => {
        if (el.type === 'radio' || el.type === 'checkbox') {
          return el.checked;
        }
        if (el.tagName === 'SELECT') {
          return el.selectedIndex > 0;
        }
        return el.value && el.value.trim().length > 0;
      });

      return isSelected;
    } catch (error) {
      return false;
    }
  }

  /**
   * 智能填写地址信息
   */
  async smartFillAddressInformation(completionStatus) {
    this.log('🏠 智能填写地址信息...');

    try {
      // 检查地址字段是否已填写
      const addressFields = [
        '#address_1_street',
        '#address_1_city',
        '#address_1_region',
        '#address_1_postal'
      ];

      const filledAddressFields = completionStatus.filledFields.filter(field =>
        addressFields.includes(field.selector)
      );

      if (filledAddressFields.length >= 3) {
        this.log('ℹ️  地址信息已基本填写完成，跳过地址填写步骤');
        this.log('📊 已填写的地址字段:');
        filledAddressFields.forEach(field => {
          this.log(`   ${field.selector}: "${field.value}"`);
        });
        return;
      }

      this.log('📝 地址信息需要填写，继续填写流程');
      await this.fillAddressInformation();

    } catch (error) {
      this.log(`⚠️  智能地址填写失败，使用标准填写: ${error.message}`);
      await this.fillAddressInformation();
    }
  }

  /**
   * 智能填写个人信息
   */
  async smartFillPersonalInformation(completionStatus) {
    this.log('👤 智能填写个人信息...');

    try {
      // 检查个人信息字段是否已填写
      const personalFields = [
        '#phone',
        '#business',
        '#mobile',
        '#sex',
        '#birthplace',
        '#birthregion',
        '#citizenship1',
        '#ssn'
      ];

      const filledPersonalFields = completionStatus.filledFields.filter(field =>
        personalFields.includes(field.selector)
      );

      if (filledPersonalFields.length >= 4) {
        this.log('ℹ️  个人信息已基本填写完成，跳过个人信息填写步骤');
        this.log('📊 已填写的个人信息字段:');
        filledPersonalFields.forEach(field => {
          this.log(`   ${field.selector}: "${field.value}"`);
        });
        return;
      }

      this.log('📝 个人信息需要填写，继续填写流程');
      await this.fillPersonalInformation();

    } catch (error) {
      this.log(`⚠️  智能个人信息填写失败，使用标准填写: ${error.message}`);
      await this.fillPersonalInformation();
    }
  }

  /**
   * 智能选择种族和民族信息
   */
  async smartSelectEthnicityAndRace(completionStatus) {
    this.log('🏳️‍🌈 智能选择种族和民族信息...');

    try {
      // 检查种族和民族选项是否已选择
      const ethnicityRaceFields = completionStatus.filledFields.filter(field =>
        field.selector.includes('hispanic') ||
        field.selector.includes('race') ||
        field.type === 'radio'
      );

      if (ethnicityRaceFields.length >= 2) {
        this.log('ℹ️  种族和民族信息已选择完成，跳过选择步骤');
        this.log('📊 已选择的种族民族选项:');
        ethnicityRaceFields.forEach(field => {
          this.log(`   ${field.selector}: 已选择`);
        });
        return;
      }

      this.log('📝 种族和民族信息需要选择，继续选择流程');
      await this.selectEthnicityAndRace();

    } catch (error) {
      this.log(`⚠️  智能种族民族选择失败，使用标准选择: ${error.message}`);
      await this.selectEthnicityAndRace();
    }
  }

  /**
   * 智能选择语言
   */
  async smartSelectLanguage(completionStatus) {
    this.log('🌐 智能选择语言...');

    try {
      // 检查语言是否已选择
      const languageField = completionStatus.filledFields.find(field =>
        field.selector.includes('form_f0cd62fe-6c76-4421-b049-a27bf3ce1ff3')
      );

      if (languageField) {
        this.log(`ℹ️  语言已选择: ${languageField.value}，跳过语言选择步骤`);
        return;
      }

      this.log('📝 语言需要选择，继续选择流程');
      await this.selectLanguage();

    } catch (error) {
      this.log(`⚠️  智能语言选择失败，使用标准选择: ${error.message}`);
      await this.selectLanguage();
    }
  }

  /**
   * 智能选择代词
   */
  async smartSelectPronouns(completionStatus) {
    this.log('👤 智能选择代词...');

    try {
      // 检查代词是否已选择
      const pronounFields = completionStatus.filledFields.filter(field =>
        field.type === 'radio' && (
          field.value.includes('He/Him') ||
          field.value.includes('She/Her') ||
          field.value.includes('They/Them')
        )
      );

      if (pronounFields.length > 0) {
        this.log(`ℹ️  代词已选择: ${pronounFields[0].value}，跳过代词选择步骤`);
        return;
      }

      this.log('📝 代词需要选择，继续选择流程');
      await this.selectPronouns();

    } catch (error) {
      this.log(`⚠️  智能代词选择失败，使用标准选择: ${error.message}`);
      await this.selectPronouns();
    }
  }

  /**
   * 智能选择性别认知
   */
  async smartSelectGenderIdentity(completionStatus) {
    this.log('🏳️‍⚧️ 智能选择性别认知...');

    try {
      // 检查性别认知是否已选择
      const genderFields = completionStatus.filledFields.filter(field =>
        field.type === 'radio' && (
          field.value.toLowerCase().includes('man') ||
          field.value.toLowerCase().includes('woman') ||
          field.value.toLowerCase().includes('non-binary')
        )
      );

      if (genderFields.length > 0) {
        this.log(`ℹ️  性别认知已选择: ${genderFields[0].value}，跳过性别认知选择步骤`);
        return;
      }

      this.log('📝 性别认知需要选择，继续选择流程');
      await this.selectGenderIdentity();

    } catch (error) {
      this.log(`⚠️  智能性别认知选择失败，使用标准选择: ${error.message}`);
      await this.selectGenderIdentity();
    }
  }

  /**
   * 智能选择性取向
   */
  async smartSelectSexualOrientation(completionStatus) {
    this.log('🏳️‍🌈 智能选择性取向...');

    try {
      // 检查性取向是否已选择
      const orientationFields = completionStatus.filledFields.filter(field =>
        field.type === 'radio' && (
          field.value.toLowerCase().includes('asexual') ||
          field.value.toLowerCase().includes('heterosexual') ||
          field.value.toLowerCase().includes('homosexual') ||
          field.value.toLowerCase().includes('bisexual')
        )
      );

      if (orientationFields.length > 0) {
        this.log(`ℹ️  性取向已选择: ${orientationFields[0].value}，跳过性取向选择步骤`);
        return;
      }

      this.log('📝 性取向需要选择，继续选择流程');
      await this.selectSexualOrientation();

    } catch (error) {
      this.log(`⚠️  智能性取向选择失败，使用标准选择: ${error.message}`);
      await this.selectSexualOrientation();
    }
  }

  /**
   * 步骤9: 处理紧急联系人页面
   */
  async step9_HandleEmergencyContact() {
    this.log('\n📋 步骤9: 处理紧急联系人页面');
    this.log('🔍 开始执行Emergency Contact页面处理...');

    try {
      // 截图：步骤9开始前
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step9-start');
      }

      // 导航到页面
      this.log('🧭 导航到Emergency Contact页面...');
      await this.navigateToMenuPage(7, 'Emergency Contact');

      // 截图：导航后
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step9-after-navigation');
      }

      // 检查关键字段是否已填写
      this.log('🔍 检查Emergency Contact关键字段填写状态...');
      const keyFieldsStatus = await this.checkEmergencyContactKeyFields();

      this.log(`📊 关键字段检查结果:`);
      this.log(`   姓名字段已填写: ${keyFieldsStatus.nameFieldsFilled}`);
      this.log(`   地址字段已填写: ${keyFieldsStatus.addressFieldsFilled}`);
      this.log(`   联系字段已填写: ${keyFieldsStatus.contactFieldsFilled}`);
      this.log(`   需要填写: ${keyFieldsStatus.needsFilling}`);

      if (!keyFieldsStatus.needsFilling) {
        this.log('✅ Emergency Contact关键字段已填写完成，跳过填写步骤');
        return;
      }

      // 填写紧急联系人信息
      this.log('📝 开始填写紧急联系人信息...');
      await this.fillEmergencyContactInformation();

      // 截图：填写完成后
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step9-after-filling');
      }

      // 点击完成按钮
      this.log('🔘 点击Emergency Contact页面完成按钮...');
      await this.clickPageCompletionButton('Emergency Contact');

      this.log('✅ 步骤9完成: 紧急联系人页面已处理');

    } catch (error) {
      this.log(`❌ 步骤9失败: ${error.message}`);
      this.log(`❌ 错误堆栈: ${error.stack}`);

      // 截图：错误时
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step9-error');
      }

      throw error;
    }
  }

  /**
   * 检查Emergency Contact关键字段
   */
  async checkEmergencyContactKeyFields() {
    this.log('🔍 检查Emergency Contact关键字段...');

    try {
      // 检查姓名字段
      const firstNameFilled = await this.isFieldFilled('#form_6a88bc22-daf7-4a6b-b573-3026467437ac');
      const lastNameFilled = await this.isFieldFilled('#form_f7281ea7-5538-4695-a7c9-615cb6dbb378');

      // 检查地址字段
      const streetFilled = await this.isFieldFilled('#form_6ec61e72-6946-4456-a1eb-67c692cbf515_street');
      const cityFilled = await this.isFieldFilled('#form_6ec61e72-6946-4456-a1eb-67c692cbf515_city');

      // 检查联系字段
      const phoneFilled = await this.isFieldFilled('#form_f1f59cb1-fc93-433a-ab58-4337e8627666');
      const emailFilled = await this.isFieldFilled('#form_413476ec-1b40-47e7-80c4-a4a0dfce33f4');

      this.log('📊 字段填写状态:');
      this.log(`   First Name: ${firstNameFilled}`);
      this.log(`   Last Name: ${lastNameFilled}`);
      this.log(`   Street: ${streetFilled}`);
      this.log(`   City: ${cityFilled}`);
      this.log(`   Phone: ${phoneFilled}`);
      this.log(`   Email: ${emailFilled}`);

      const nameFieldsFilled = firstNameFilled && lastNameFilled;
      const addressFieldsFilled = streetFilled && cityFilled;
      const contactFieldsFilled = phoneFilled && emailFilled;

      // 如果所有关键字段都已填写，则不需要填写
      const needsFilling = !(nameFieldsFilled && addressFieldsFilled && contactFieldsFilled);

      return {
        nameFieldsFilled,
        addressFieldsFilled,
        contactFieldsFilled,
        needsFilling
      };

    } catch (error) {
      this.log(`⚠️  检查关键字段失败: ${error.message}`);
      // 如果检查失败，默认需要填写
      return {
        nameFieldsFilled: false,
        addressFieldsFilled: false,
        contactFieldsFilled: false,
        needsFilling: true
      };
    }
  }

  /**
   * 根据字段数据判断是否已填写
   */
  isFieldFilledByData(field) {
    try {
      if (field.type === 'checkbox' || field.type === 'radio') {
        return field.checked === true;
      }

      if (field.type === 'select' || field.selectedIndex !== undefined) {
        return field.selectedIndex > 0;
      }

      const value = field.value || '';
      return value.trim().length > 0;

    } catch (error) {
      this.log(`⚠️  检查字段数据 ${field.name || field.selector} 失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 检查单个字段是否已填写
   */
  async isFieldFilled(selector) {
    try {
      const element = await this.formFiller.currentPage.$(selector);
      if (!element) {
        return false;
      }

      const value = await element.evaluate(el => {
        if (el.tagName === 'SELECT') {
          return el.selectedIndex > 0 ? el.value : '';
        }
        return el.value || '';
      });

      const isFilled = value.trim().length > 0;
      this.log(`🔍 字段 ${selector}: "${value}" (已填写: ${isFilled})`);
      return isFilled;

    } catch (error) {
      this.log(`⚠️  检查字段 ${selector} 失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 智能填写紧急联系人信息
   */
  async smartFillEmergencyContactInformation(completionStatus) {
    this.log('👥 智能填写紧急联系人信息...');

    try {
      // 检查紧急联系人字段是否已填写
      const emergencyContactFields = [
        '#form_6a88bc22-daf7-4a6b-b573-3026467437ac', // First Name
        '#form_f7281ea7-5538-4695-a7c9-615cb6dbb378', // Last Name
        '#form_6ec61e72-6946-4456-a1eb-67c692cbf515_street', // Street
        '#form_f1f59cb1-fc93-433a-ab58-4337e8627666', // Mobile Number
        '#form_413476ec-1b40-47e7-80c4-a4a0dfce33f4' // Email Address
      ];

      const filledEmergencyFields = completionStatus.filledFields.filter(field =>
        emergencyContactFields.includes(field.selector)
      );

      if (filledEmergencyFields.length >= 4) {
        this.log('ℹ️  紧急联系人信息已基本填写完成，跳过填写步骤');
        this.log('📊 已填写的紧急联系人字段:');
        filledEmergencyFields.forEach(field => {
          this.log(`   ${field.selector}: "${field.value}"`);
        });
        return;
      }

      this.log('📝 紧急联系人信息需要填写，继续填写流程');
      await this.fillEmergencyContactInformation();

    } catch (error) {
      this.log(`⚠️  智能紧急联系人填写失败，使用标准填写: ${error.message}`);
      await this.fillEmergencyContactInformation();
    }
  }

  /**
   * 填写紧急联系人信息
   */
  async fillEmergencyContactInformation() {
    this.log('👥 填写紧急联系人信息...');

    try {
      // 生成随机联系人数据
      this.log('🎲 生成随机联系人数据...');
      const contactData = this.generateEmergencyContactData();
      this.log(`📊 生成的联系人数据: ${JSON.stringify(contactData)}`);

      // 填写姓名
      this.log('📝 开始填写紧急联系人姓名...');
      await this.fillEmergencyContactName(contactData);
      this.log('✅ 紧急联系人姓名填写完成');

      // 填写地址信息
      this.log('🏠 开始填写紧急联系人地址...');
      await this.fillEmergencyContactAddress();
      this.log('✅ 紧急联系人地址填写完成');

      // 填写联系方式
      this.log('📞 开始填写紧急联系人联系方式...');
      await this.fillEmergencyContactDetails(contactData);
      this.log('✅ 紧急联系人联系方式填写完成');

      // 选择关系和语言
      this.log('🔘 开始选择紧急联系人选项...');
      await this.selectEmergencyContactOptions();
      this.log('✅ 紧急联系人选项选择完成');

      this.log('✅ 紧急联系人信息填写完成');

    } catch (error) {
      this.log(`❌ 填写紧急联系人信息失败: ${error.message}`);
      this.log(`❌ 错误堆栈: ${error.stack}`);
      throw error;
    }
  }

  /**
   * 生成紧急联系人数据
   */
  generateEmergencyContactData() {
    this.log('🎲 生成随机紧急联系人数据...');

    const firstNames = ['John', 'Sarah', 'Michael', 'Emily', 'David', 'Jessica', 'Robert', 'Ashley', 'James', 'Amanda', 'Christopher', 'Lisa', 'Matthew', 'Karen', 'Daniel', 'Nancy'];
    const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez', 'Wilson', 'Anderson', 'Taylor', 'Thomas', 'Jackson', 'White'];
    const emailDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com', 'icloud.com'];

    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
    const domain = emailDomains[Math.floor(Math.random() * emailDomains.length)];

    // 生成更真实的邮箱格式
    const emailFormats = [
      `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${domain}`,
      `${firstName.toLowerCase()}${lastName.toLowerCase()}@${domain}`,
      `${firstName.toLowerCase()}${Math.floor(Math.random() * 999)}@${domain}`,
      `${firstName.toLowerCase()}_${lastName.toLowerCase()}@${domain}`,
      `${firstName.charAt(0).toLowerCase()}${lastName.toLowerCase()}@${domain}`
    ];

    const email = emailFormats[Math.floor(Math.random() * emailFormats.length)];

    const contactData = {
      firstName: firstName,
      lastName: lastName,
      email: email
    };

    this.log('📊 生成的联系人数据:');
    this.log(`   姓名: ${contactData.firstName} ${contactData.lastName}`);
    this.log(`   邮箱: ${contactData.email}`);

    return contactData;
  }

  /**
   * 填写紧急联系人姓名
   */
  async fillEmergencyContactName(contactData) {
    this.log('📝 填写紧急联系人姓名...');

    try {
      // 填写名字
      this.log(`📝 填写名字: ${contactData.firstName}`);
      await this.fillFormField('#form_6a88bc22-daf7-4a6b-b573-3026467437ac', contactData.firstName, '紧急联系人名字');

      // 填写姓氏
      this.log(`📝 填写姓氏: ${contactData.lastName}`);
      await this.fillFormField('#form_f7281ea7-5538-4695-a7c9-615cb6dbb378', contactData.lastName, '紧急联系人姓氏');

    } catch (error) {
      this.log(`❌ 填写紧急联系人姓名失败: ${error.message}`);
      this.log(`❌ 错误详情: ${error.stack}`);
      throw error;
    }
  }

  /**
   * 填写紧急联系人地址
   */
  async fillEmergencyContactAddress() {
    this.log('🏠 填写紧急联系人地址...');

    try {
      const completeAddress = this.studentData.completeAddress;

      this.log(`📊 学生数据检查:`);
      this.log(`   completeAddress: ${completeAddress || '未提供'}`);

      if (!completeAddress) {
        this.log('⚠️  学生数据中没有完整地址信息，跳过地址填写');
        return;
      }

      this.log(`📍 使用学生地址: ${completeAddress}`);

      // 解析地址
      this.log('🔍 解析地址信息...');
      const addressParts = this.parseCompleteAddress(completeAddress);

      this.log(`📊 地址解析结果:`);
      this.log(`   街道: "${addressParts.street}"`);
      this.log(`   城市: "${addressParts.city}"`);
      this.log(`   州: "${addressParts.state}" (全名: "${addressParts.stateFull}")`);
      this.log(`   邮编: "${addressParts.postal}"`);

      // 填写街道地址（textarea）
      if (addressParts.street) {
        this.log(`📝 填写街道地址: ${addressParts.street}`);
        await this.fillTextareaField('#form_6ec61e72-6946-4456-a1eb-67c692cbf515_street', addressParts.street, '紧急联系人街道地址');
      } else {
        this.log('⚠️  街道地址为空，跳过');
      }

      // 填写城市
      if (addressParts.city) {
        this.log(`📝 填写城市: ${addressParts.city}`);
        await this.fillFormField('#form_6ec61e72-6946-4456-a1eb-67c692cbf515_city', addressParts.city, '紧急联系人城市');
      } else {
        this.log('⚠️  城市为空，跳过');
      }

      // 填写邮编
      if (addressParts.postal) {
        this.log(`📝 填写邮编: ${addressParts.postal}`);
        await this.fillFormField('#form_6ec61e72-6946-4456-a1eb-67c692cbf515_postal', addressParts.postal, '紧急联系人邮编');
      } else {
        this.log('⚠️  邮编为空，跳过');
      }

      // 选择州
      if (addressParts.state) {
        this.log(`📝 选择州: ${addressParts.stateFull} (${addressParts.state})`);
        await this.selectFormOption('#form_6ec61e72-6946-4456-a1eb-67c692cbf515_region', addressParts.stateFull, '紧急联系人州');
      } else {
        this.log('⚠️  州信息为空，跳过');
      }

    } catch (error) {
      this.log(`❌ 填写紧急联系人地址失败: ${error.message}`);
      this.log(`❌ 错误详情: ${error.stack}`);
      throw error;
    }
  }

  /**
   * 填写紧急联系人详细信息
   */
  async fillEmergencyContactDetails(contactData) {
    this.log('📞 填写紧急联系人详细信息...');

    try {
      // 填写电话号码（清理格式：移除连字符、括号和空格）
      const phone = this.studentData.phone;

      this.log(`📊 电话信息检查:`);
      this.log(`   原始电话: ${phone || '未提供'}`);

      if (phone) {
        const cleanPhone = phone.replace(/[-\s\(\)]/g, ''); // 移除连字符、空格、括号
        this.log(`📞 清理电话号码: "${phone}" → "${cleanPhone}"`);
        this.log(`📝 填写电话号码: ${cleanPhone}`);
        await this.fillFormField('#form_f1f59cb1-fc93-433a-ab58-4337e8627666', cleanPhone, '紧急联系人电话');
      } else {
        this.log('⚠️  电话号码为空，跳过电话填写');
      }

      // 填写邮箱地址
      this.log(`📝 填写邮箱地址: ${contactData.email}`);
      await this.fillFormField('#form_413476ec-1b40-47e7-80c4-a4a0dfce33f4', contactData.email, '紧急联系人邮箱');

    } catch (error) {
      this.log(`❌ 填写紧急联系人详细信息失败: ${error.message}`);
      this.log(`❌ 错误详情: ${error.stack}`);
      throw error;
    }
  }

  /**
   * 选择紧急联系人选项
   */
  async selectEmergencyContactOptions() {
    this.log('🔘 选择紧急联系人选项...');

    try {
      // 选择关系 - "Other Relationship"
      this.log('📋 选择与申请人关系: Other Relationship');
      await this.selectFormOption('#form_1098cd41-1d4e-4b3c-8111-a527f4647edc', 'Other Relationship', '与申请人关系');

      // 选择语言偏好 - "English"
      this.log('📋 选择语言偏好: English');
      await this.selectFormOption('#form_15ac7861-7d72-4b55-a398-65e17f0ab1d8', 'English', '语言偏好');

    } catch (error) {
      this.log(`❌ 选择紧急联系人选项失败: ${error.message}`);
      this.log(`❌ 错误详情: ${error.stack}`);
      throw error;
    }
  }

  /**
   * 步骤10: 处理学术历史页面
   */
  async step10_HandleAcademicHistory() {
    this.log('\n📋 步骤10: 处理学术历史页面');
    this.log('🔍 开始执行Academic History页面处理...');

    try {
      // 截图：步骤10开始前
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step10-start');
      }

      // 导航到页面
      this.log('🧭 导航到Academic History页面...');
      await this.navigateToMenuPage(8, 'Academic History');

      // 截图：导航后
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step10-after-navigation');
      }

      // 检查是否已有学校记录
      this.log('🔍 检查现有学校记录...');
      const hasExistingSchools = await this.checkExistingSchools();

      if (hasExistingSchools) {
        this.log('✅ 学术历史已有学校记录，跳过添加步骤');
      } else {
        this.log('📝 未发现学校记录，开始添加学术历史...');
        await this.addAcademicHistory();
      }

      // 截图：处理完成后
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step10-after-processing');
      }

      // 点击完成按钮
      this.log('🔘 点击Academic History页面完成按钮...');
      await this.clickPageCompletionButton('Academic History');

      this.log('✅ 步骤10完成: 学术历史页面已处理');

    } catch (error) {
      this.log(`❌ 步骤10失败: ${error.message}`);
      this.log(`❌ 错误堆栈: ${error.stack}`);

      // 截图：错误时
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step10-error');
      }

      throw error;
    }
  }

  /**
   * 检查现有学校记录
   */
  async checkExistingSchools() {
    this.log('🔍 检查学术历史表格中的现有学校...');

    try {
      // 查找学校表格
      const tableSelector = '#form_question_eea3d6a0-5b3f-4305-8a55-7a6cf37e55cf > div > table';

      const tableExists = await this.formFiller.currentPage.waitForSelector(tableSelector, {
        timeout: 5000
      }).then(() => true).catch(() => false);

      if (!tableExists) {
        this.log('⚠️  学术历史表格不存在');
        return false;
      }

      // 等待表格完全加载
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 注释：如果需要删除现有记录，可以在这里添加逻辑

      // 检查表格中的数据行（排除表头和"Add School"行）
      const schoolRows = await this.formFiller.currentPage.evaluate((selector) => {
        const table = document.querySelector(selector);
        if (!table) return [];

        const tbody = table.querySelector('tbody');
        if (!tbody) return [];

        // 查找所有包含学校数据的行
        // 根据HTML结构，学校记录行有class="widget_table_row"
        const schoolDataRows = tbody.querySelectorAll('tr.widget_table_row');
        const schoolRows = [];

        schoolDataRows.forEach((row, index) => {
          const cells = row.querySelectorAll('td');
          if (cells.length >= 3) { // 学校记录应该有3列：School Name, School Type, Dates Attended
            const schoolName = cells[0].textContent.trim();
            const schoolType = cells[1].textContent.trim();
            const datesAttended = cells[2].textContent.trim();

            // 检查是否有实际的学校数据
            if (schoolName && schoolName.length > 2 && schoolType && datesAttended) {
              schoolRows.push({
                index: index,
                schoolName: schoolName,
                schoolType: schoolType,
                datesAttended: datesAttended,
                hasContent: true
              });
            }
          }
        });

        return schoolRows;
      }, tableSelector);

      this.log(`📊 表格分析结果:`);
      this.log(`   总数据行: ${schoolRows.length}`);

      if (schoolRows.length > 0) {
        this.log(`📚 发现的学校记录:`);
        schoolRows.forEach((row, index) => {
          this.log(`   ${index + 1}. 学校: "${row.schoolName}"`);
          this.log(`      内容: [${row.content.join(', ')}]`);
        });

        this.log('✅ 检测到已添加的学校记录');
        return true;
      } else {
        this.log('⚠️  未发现学校记录');
        return false;
      }

    } catch (error) {
      this.log(`⚠️  检查现有学校失败: ${error.message}`);
      this.log(`❌ 错误详情: ${error.stack}`);
      return false;
    }
  }

  /**
   * 添加学术历史
   */
  async addAcademicHistory() {
    this.log('📚 添加学术历史信息...');

    try {
      // 获取高中信息
      const schoolData = await this.getHighSchoolData();

      // 点击添加学校按钮
      await this.clickAddSchoolButton();

      // 填写学校信息表单
      await this.fillSchoolInformationForm(schoolData);

      // 保存学校信息
      await this.saveSchoolInformation();

      this.log('✅ 学术历史添加完成');

    } catch (error) {
      this.log(`❌ 添加学术历史失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取高中信息数据
   */
  async getHighSchoolData() {
    this.log('📊 获取高中信息数据...');

    try {
      // 读取高中信息工作表
      const XLSX = require('xlsx');
      const workbook = XLSX.readFile('./test.xlsx');
      const highSchoolSheet = workbook.Sheets['高中信息'];
      const highSchoolData = XLSX.utils.sheet_to_json(highSchoolSheet);

      if (highSchoolData.length === 0) {
        throw new Error('高中信息工作表中没有数据');
      }

      // 根据当前学生姓名查找对应的高中信息
      const studentName = `${this.studentData.firstName} ${this.studentData.lastName}`;
      let schoolInfo = highSchoolData.find(school => school['学生姓名'] === studentName);

      if (!schoolInfo) {
        this.log(`⚠️  未找到学生 ${studentName} 的高中信息，使用第一条记录`);
        schoolInfo = highSchoolData[0];
      }

      this.log(`📚 使用高中信息: ${schoolInfo['学校名称']}`);

      return {
        schoolName: schoolInfo['学校名称'],
        schoolId: schoolInfo['学校ID'],
        address: schoolInfo['地址'],
        city: schoolInfo['城市'],
        state: schoolInfo['州'],
        zipCode: schoolInfo['邮编'],
        phone: schoolInfo['电话']
      };

    } catch (error) {
      this.log(`❌ 获取高中信息失败: ${error.message}`);
      // 使用默认数据
      return {
        schoolName: 'Maplewood High School',
        schoolId: '390513904083',
        address: '7075 STATE ROUTE 88',
        city: 'RAVENNA',
        state: 'OH',
        zipCode: '44266',
        phone: '(*************'
      };
    }
  }

  /**
   * 点击添加学校按钮
   */
  async clickAddSchoolButton() {
    this.log('🔘 点击添加学校按钮...');

    try {
      const addButtonSelector = '#form_question_eea3d6a0-5b3f-4305-8a55-7a6cf37e55cf > div > table > thead > tr.row_hover > td > a';

      this.log(`🔍 查找添加学校按钮: ${addButtonSelector}`);
      await this.formFiller.currentPage.waitForSelector(addButtonSelector, {
        timeout: this.options.dialogTimeout
      });

      const addButton = await this.formFiller.currentPage.$(addButtonSelector);
      if (!addButton) {
        throw new Error('找不到添加学校按钮');
      }

      // 检查按钮状态
      const isVisible = await addButton.evaluate(el => el.offsetParent !== null);
      const buttonText = await addButton.evaluate(el => el.textContent.trim());

      this.log(`📊 添加学校按钮状态: 可见=${isVisible}, 文本="${buttonText}"`);

      if (!isVisible) {
        throw new Error('添加学校按钮不可见');
      }

      // 截图：点击前
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('before-click-add-school');
      }

      await addButton.click();
      this.log('✅ 添加学校按钮已点击');

      // 等待对话框出现
      await this.waitForSchoolDialog();

    } catch (error) {
      this.log(`❌ 点击添加学校按钮失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 等待学校对话框出现
   */
  async waitForSchoolDialog() {
    this.log('⏳ 等待学校信息对话框出现...');

    try {
      const dialogSelector = 'body > div.dialog_host.ui-draggable > div > form';

      await this.formFiller.currentPage.waitForSelector(dialogSelector, {
        timeout: this.options.dialogTimeout
      });

      this.log('✅ 学校信息对话框已出现');

      // 截图：对话框出现后
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('school-dialog-appeared');
      }

    } catch (error) {
      this.log(`❌ 等待学校对话框失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 填写学校信息表单
   */
  async fillSchoolInformationForm(schoolData) {
    this.log('📝 填写学校信息表单...');

    try {
      this.log(`📚 学校信息: ${JSON.stringify(schoolData)}`);

      // 填写学校名称并搜索
      await this.fillSchoolNameAndSearch(schoolData.schoolName);

      // 选择学校
      await this.selectSchoolFromDropdown();

      // 填写开始日期
      await this.fillStartDate();

      // 填写结束日期
      await this.fillEndDate();

      // 选择毕业状态
      await this.selectGraduationStatus();

      this.log('✅ 学校信息表单填写完成');

    } catch (error) {
      this.log(`❌ 填写学校信息表单失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 填写学校名称并搜索
   */
  async fillSchoolNameAndSearch(schoolName) {
    this.log(`🔍 填写学校名称并搜索: ${schoolName}`);

    try {
      const schoolNameSelector = '#form_898992fd-b0ef-440b-8a1f-ab54dfe744fb';

      await this.formFiller.currentPage.waitForSelector(schoolNameSelector, {
        timeout: this.options.dialogTimeout
      });

      const schoolNameField = await this.formFiller.currentPage.$(schoolNameSelector);
      if (!schoolNameField) {
        throw new Error('找不到学校名称字段');
      }

      // 清空字段
      await schoolNameField.click({ clickCount: 3 });
      await this.formFiller.currentPage.keyboard.press('Delete');

      // 逐字符输入学校名称以触发自动完成
      this.log(`⌨️  逐字符输入学校名称: ${schoolName}`);
      for (let i = 0; i < schoolName.length; i++) {
        await schoolNameField.type(schoolName[i], { delay: 150 });

        // 每输入几个字符后稍作停顿，让自动完成有时间响应
        if (i > 3 && i % 4 === 0) {
          await new Promise(resolve => setTimeout(resolve, 300));
        }
      }

      this.log(`✅ 学校名称已填写: ${schoolName}`);

      // 等待更长时间让自动完成加载
      this.log('⏳ 等待自动完成搜索结果...');
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 触发搜索（如果需要）
      await this.formFiller.currentPage.keyboard.press('ArrowDown');
      await new Promise(resolve => setTimeout(resolve, 1000));

    } catch (error) {
      this.log(`❌ 填写学校名称失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 从下拉列表选择学校
   */
  async selectSchoolFromDropdown() {
    this.log('📋 从下拉列表选择学校...');

    try {
      // 等待更长时间让搜索结果加载
      this.log('⏳ 等待学校搜索结果加载...');
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 尝试多种下拉选项选择器
      const dropdownSelectors = [
        'ul.ui-autocomplete li.ui-menu-item',
        'ul.ui-autocomplete li',
        '.ui-autocomplete .ui-menu-item',
        '.ui-autocomplete li',
        'div.ui-autocomplete li',
        '[role="listbox"] li',
        '.autocomplete-suggestions div',
        '.suggestions li'
      ];

      let dropdownOptions = [];
      let usedSelector = '';

      // 尝试每个选择器
      for (const selector of dropdownSelectors) {
        try {
          dropdownOptions = await this.formFiller.currentPage.$$(selector);
          if (dropdownOptions.length > 0) {
            usedSelector = selector;
            this.log(`✅ 使用选择器找到选项: ${selector}`);
            break;
          }
        } catch (error) {
          this.log(`⚠️  选择器 ${selector} 失败: ${error.message}`);
        }
      }

      if (dropdownOptions.length === 0) {
        this.log('⚠️  未找到学校下拉选项，尝试其他方法...');

        // 尝试按下箭头键激活下拉菜单
        this.log('🔽 尝试按下箭头键激活下拉菜单...');
        await this.formFiller.currentPage.keyboard.press('ArrowDown');
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 再次查找选项
        for (const selector of dropdownSelectors) {
          try {
            dropdownOptions = await this.formFiller.currentPage.$$(selector);
            if (dropdownOptions.length > 0) {
              usedSelector = selector;
              this.log(`✅ 按下箭头键后找到选项: ${selector}`);
              break;
            }
          } catch (error) {
            // 忽略错误，继续尝试
          }
        }
      }

      if (dropdownOptions.length === 0) {
        this.log('⚠️  仍未找到学校下拉选项，尝试强制确认输入');

        // 尝试多种确认方法
        const confirmMethods = [
          async () => {
            this.log('🔄 方法1: 按Enter键确认');
            await this.formFiller.currentPage.keyboard.press('Enter');
          },
          async () => {
            this.log('🔄 方法2: 按Tab键移动到下一字段');
            await this.formFiller.currentPage.keyboard.press('Tab');
          },
          async () => {
            this.log('🔄 方法3: 点击其他字段确认输入');
            const nextField = await this.formFiller.currentPage.$('#form_6f1f23d2-b19c-475d-93a7-92c1ee45487a_m');
            if (nextField) {
              await nextField.click();
            }
          }
        ];

        for (const method of confirmMethods) {
          try {
            await method();
            await new Promise(resolve => setTimeout(resolve, 1000));
            this.log('✅ 学校名称确认完成');
            return;
          } catch (error) {
            this.log(`⚠️  确认方法失败: ${error.message}`);
          }
        }

        this.log('⚠️  所有确认方法都失败，继续处理');
        return;
      }

      this.log(`📋 找到 ${dropdownOptions.length} 个学校选项 (使用选择器: ${usedSelector})`);

      // 获取所有选项的文本
      const optionTexts = [];
      for (let i = 0; i < Math.min(dropdownOptions.length, 5); i++) {
        try {
          const optionText = await dropdownOptions[i].evaluate(el => el.textContent.trim());
          optionTexts.push(optionText);
          this.log(`   选项 ${i + 1}: "${optionText}"`);
        } catch (error) {
          this.log(`⚠️  获取选项 ${i + 1} 文本失败: ${error.message}`);
        }
      }

      // 选择第一个选项（通常是最匹配的）
      const selectedIndex = 0;
      const selectedOption = dropdownOptions[selectedIndex];

      try {
        const optionText = await selectedOption.evaluate(el => el.textContent.trim());
        this.log(`🎯 选择学校选项 ${selectedIndex + 1}: "${optionText}"`);

        // 截图：选择前
        if (this.options.enableScreenshots) {
          await this.takeScreenshot('before-select-school-option');
        }

        await selectedOption.click();
        this.log('✅ 学校选择完成');

        // 等待选择生效
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (clickError) {
        this.log(`❌ 点击选项失败: ${clickError.message}`);
        // 尝试使用键盘选择
        this.log('🔽 尝试使用键盘选择...');
        await this.formFiller.currentPage.keyboard.press('ArrowDown');
        await this.formFiller.currentPage.keyboard.press('Enter');
        this.log('✅ 使用键盘选择完成');
      }

    } catch (error) {
      this.log(`❌ 选择学校失败: ${error.message}`);
      // 最后的备选方案：Tab键移动到下一个字段
      try {
        this.log('🔄 使用Tab键作为最后备选方案...');
        await this.formFiller.currentPage.keyboard.press('Tab');
        this.log('✅ 使用Tab键确认学校选择');
      } catch (tabError) {
        this.log(`❌ Tab键确认也失败: ${tabError.message}`);
        throw error;
      }
    }
  }

  /**
   * 填写开始日期
   */
  async fillStartDate() {
    this.log('📅 填写开始日期: August 2020');

    try {
      // 填写开始月份 - August
      this.log('📝 填写开始月份: August');
      await this.selectFormOption('#form_cf46b0f9-397e-472c-88ec-43b338e10792_m', 'August', '开始月份');

      // 填写开始年份 - 2020
      this.log('📝 填写开始年份: 2020');
      await this.selectFormOption('#form_cf46b0f9-397e-472c-88ec-43b338e10792_y', '2020', '开始年份');

      this.log('✅ 开始日期填写完成');

    } catch (error) {
      this.log(`❌ 填写开始日期失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 填写结束日期
   */
  async fillEndDate() {
    this.log('📅 填写结束日期: July 2024');

    try {
      // 填写结束月份 - July
      this.log('📝 填写结束月份: July');
      await this.selectFormOption('#form_6f1f23d2-b19c-475d-93a7-92c1ee45487a_m', 'July', '结束月份');

      // 填写结束年份 - 2024
      this.log('📝 填写结束年份: 2024');
      await this.selectFormOption('#form_6f1f23d2-b19c-475d-93a7-92c1ee45487a_y', '2024', '结束年份');

      this.log('✅ 结束日期填写完成');

    } catch (error) {
      this.log(`❌ 填写结束日期失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 步骤11: 处理项目选择页面
   */
  async step11_HandleProgramSelection() {
    this.log('\n📋 步骤11: 处理项目选择页面');
    this.log('🔍 开始执行Program Selection页面处理...');

    try {
      // 截图：步骤11开始前
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step11-start');
      }

      // 导航到页面
      this.log('🧭 导航到Program Selection页面...');
      await this.navigateToMenuPage(9, 'Program Selection');

      // 截图：导航后
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step11-after-navigation');
      }

      // 检查是否已有项目选择
      this.log('🔍 检查现有项目选择...');
      const hasExistingSelection = await this.checkExistingProgramSelection();

      if (hasExistingSelection) {
        this.log('✅ 项目选择已完成，跳过选择步骤');
      } else {
        this.log('📝 未发现项目选择，开始选择项目...');
        await this.selectProgram();
      }

      // 截图：处理完成后
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step11-after-processing');
      }

      // 点击完成按钮
      this.log('🔘 点击Program Selection页面完成按钮...');
      await this.clickPageCompletionButton('Program Selection');

      this.log('✅ 步骤11完成: 项目选择页面已处理');

    } catch (error) {
      this.log(`❌ 步骤11失败: ${error.message}`);
      this.log(`❌ 错误堆栈: ${error.stack}`);

      // 截图：错误时
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step11-error');
      }

      throw error;
    }
  }

  /**
   * 检查现有项目选择
   */
  async checkExistingProgramSelection() {
    this.log('🔍 检查项目选择状态...');

    try {
      // 检查Nondegree单选按钮是否已选中
      const radioSelector = '#form_a3f832c6-31fb-478c-bb05-29aa54f7089e_1';

      const radioElement = await this.formFiller.currentPage.$(radioSelector);
      if (!radioElement) {
        this.log('⚠️  未找到Nondegree单选按钮');
        return false;
      }

      const isChecked = await radioElement.evaluate(el => el.checked);
      const radioValue = await radioElement.evaluate(el => el.value);
      const labelText = await radioElement.evaluate(el => {
        const label = document.querySelector(`label[for="${el.id}"]`);
        return label ? label.textContent.trim() : '';
      });

      this.log(`📊 单选按钮状态: 已选中=${isChecked}, 值="${radioValue}", 标签="${labelText}"`);

      if (isChecked) {
        this.log(`✅ 已选择项目: ${labelText}`);
      } else {
        this.log('⚠️  未选择项目');
      }

      return isChecked;

    } catch (error) {
      this.log(`⚠️  检查项目选择失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 选择项目
   */
  async selectProgram() {
    this.log('📚 选择项目...');

    try {
      // 点击Nondegree单选按钮
      const radioSelector = '#form_a3f832c6-31fb-478c-bb05-29aa54f7089e_1';

      this.log('🔘 点击Nondegree单选按钮...');

      // 等待页面完全加载
      this.log('⏳ 等待页面完全加载...');
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 等待单选按钮出现
      await this.formFiller.currentPage.waitForSelector(radioSelector, {
        timeout: this.options.dialogTimeout
      });

      const radioElement = await this.formFiller.currentPage.$(radioSelector);
      if (!radioElement) {
        throw new Error('找不到Nondegree单选按钮');
      }

      // 检查按钮状态
      const isVisible = await radioElement.evaluate(el => el.offsetParent !== null);
      const isDisabled = await radioElement.evaluate(el => el.disabled);
      const isChecked = await radioElement.evaluate(el => el.checked);

      this.log(`📊 单选按钮状态: 可见=${isVisible}, 禁用=${isDisabled}, 已选中=${isChecked}`);

      if (!isVisible) {
        throw new Error('Nondegree单选按钮不可见');
      }

      if (isDisabled) {
        throw new Error('Nondegree单选按钮被禁用');
      }

      if (isChecked) {
        this.log('ℹ️  Nondegree单选按钮已经选中');
      } else {
        // 截图：点击前
        if (this.options.enableScreenshots) {
          await this.takeScreenshot('before-click-nondegree-radio');
        }

        await radioElement.click();
        this.log('✅ Nondegree单选按钮已点击');

        // 等待选择生效
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 验证选择结果
        const isNowChecked = await radioElement.evaluate(el => el.checked);
        if (isNowChecked) {
          this.log('✅ Nondegree选择确认成功');
        } else {
          this.log('⚠️  Nondegree选择可能未生效');
        }
      }

      this.log('✅ 项目选择完成');

    } catch (error) {
      this.log(`❌ 选择项目失败: ${error.message}`);
      throw error;
    }
  }



  /**
   * 步骤12: 处理签名页面
   */
  async step12_HandleSignature() {
    this.log('\n✍️  步骤12: 处理签名页面');
    this.log('🔍 开始执行Signature页面处理...');

    try {
      // 截图：步骤12开始前
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step12-start');
      }

      // 导航到页面
      this.log('🧭 导航到Signature页面...');
      await this.navigateToMenuPage(10, 'Signature');

      // 截图：导航后
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step12-after-navigation');
      }

      // 检查是否已有签名
      this.log('🔍 检查现有签名...');
      const hasExistingSignature = await this.checkExistingSignature();

      if (hasExistingSignature) {
        this.log('✅ 签名已完成，跳过签名步骤');
      } else {
        this.log('✍️  未发现签名，开始填写签名...');
        await this.fillSignature();
      }

      // 截图：处理完成后
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step12-after-processing');
      }

      // 点击确认按钮
      this.log('🔘 点击Signature页面确认按钮...');
      await this.clickSignatureConfirmButton();

      this.log('✅ 步骤12完成: 签名页面已处理');

    } catch (error) {
      this.log(`❌ 步骤12失败: ${error.message}`);
      this.log(`❌ 错误堆栈: ${error.stack}`);

      // 截图：错误时
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step12-error');
      }

      throw error;
    }
  }

  /**
   * 检查现有签名
   */
  async checkExistingSignature() {
    this.log('🔍 检查签名状态...');

    try {
      // 检查签名输入字段
      const signatureSelector = 'input[name="signature"]';

      const signatureElement = await this.formFiller.currentPage.$(signatureSelector);
      if (!signatureElement) {
        this.log('⚠️  未找到签名输入字段');
        return false;
      }

      const signatureValue = await signatureElement.evaluate(el => el.value);

      this.log(`📊 签名状态: 值="${signatureValue}"`);

      const hasSignature = signatureValue && signatureValue.trim() !== '';

      if (hasSignature) {
        this.log(`✅ 已填写签名: ${signatureValue}`);
      } else {
        this.log('⚠️  未填写签名');
      }

      return hasSignature;

    } catch (error) {
      this.log(`⚠️  检查签名失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 填写签名
   */
  async fillSignature() {
    this.log('✍️  填写签名...');

    try {
      // 获取学生全名
      const fullName = `${this.studentData.firstName} ${this.studentData.lastName}`;
      this.log(`📝 使用学生全名作为签名: ${fullName}`);

      // 填写签名字段
      const signatureSelector = 'input[name="signature"]';
      await this.fillFormField(signatureSelector, fullName, '签名');

      this.log('✅ 签名填写完成');

    } catch (error) {
      this.log(`❌ 填写签名失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 点击签名确认按钮
   */
  async clickSignatureConfirmButton() {
    this.log('🔘 点击签名确认按钮...');

    try {
      const confirmButtonSelector = '#main > form > div > button';

      await this.formFiller.currentPage.waitForSelector(confirmButtonSelector, {
        timeout: this.options.dialogTimeout
      });

      const confirmButton = await this.formFiller.currentPage.$(confirmButtonSelector);
      if (!confirmButton) {
        throw new Error('找不到签名确认按钮');
      }

      // 检查按钮状态
      const isVisible = await confirmButton.evaluate(el => el.offsetParent !== null);
      const isDisabled = await confirmButton.evaluate(el => el.disabled);
      const buttonText = await confirmButton.evaluate(el => el.textContent.trim());

      this.log(`📊 确认按钮状态: 可见=${isVisible}, 禁用=${isDisabled}, 文本="${buttonText}"`);

      if (!isVisible) {
        throw new Error('签名确认按钮不可见');
      }

      if (isDisabled) {
        throw new Error('签名确认按钮被禁用');
      }

      // 截图：点击前
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('before-click-signature-confirm');
      }

      await confirmButton.click();
      this.log('✅ 签名确认按钮已点击');

      // 等待页面响应
      await new Promise(resolve => setTimeout(resolve, 2000));

    } catch (error) {
      this.log(`❌ 点击签名确认按钮失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 步骤13: 处理审核页面
   */
  async step13_HandleReview() {
    this.log('\n📋 步骤13: 处理审核页面');
    this.log('🔍 开始执行Review页面处理...');

    try {
      // 截图：步骤13开始前
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step13-start');
      }

      // 导航到页面
      this.log('🧭 导航到Review页面...');
      await this.navigateToMenuPage(11, 'Review');

      // 截图：导航后
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step13-after-navigation');
      }

      // 检查页面状态
      this.log('🔍 检查Review页面状态...');
      const reviewStatus = await this.checkReviewPageStatus();

      if (reviewStatus.hasErrors) {
        this.log('⚠️  Review页面发现错误，需要修正');
        this.log(`📄 错误详情: ${reviewStatus.errorDetails}`);
        // 这里可以添加错误处理逻辑
      } else {
        this.log('✅ Review页面检查通过，准备提交');
      }

      // 截图：检查完成后
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step13-after-review-check');
      }

      // 提交申请
      this.log('📤 提交申请...');
      await this.submitApplication();

      this.log('✅ 步骤13完成: Review页面已处理并提交申请');

    } catch (error) {
      this.log(`❌ 步骤13失败: ${error.message}`);
      this.log(`❌ 错误堆栈: ${error.stack}`);

      // 截图：错误时
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step13-error');
      }

      throw error;
    }
  }

  /**
   * 检查Review页面状态
   */
  async checkReviewPageStatus() {
    this.log('🔍 检查Review页面状态...');

    try {
      // 等待页面完全加载
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 检查是否有错误信息
      const errorElements = await this.formFiller.currentPage.$$('.error, .alert-danger, .validation-error');

      if (errorElements.length > 0) {
        this.log(`⚠️  发现 ${errorElements.length} 个错误元素`);

        const errorDetails = [];
        for (let i = 0; i < errorElements.length; i++) {
          try {
            const errorText = await errorElements[i].evaluate(el => el.textContent.trim());
            if (errorText) {
              errorDetails.push(errorText);
              this.log(`   错误 ${i + 1}: ${errorText}`);
            }
          } catch (error) {
            this.log(`⚠️  获取错误 ${i + 1} 文本失败: ${error.message}`);
          }
        }

        return {
          hasErrors: true,
          errorCount: errorElements.length,
          errorDetails: errorDetails.join('; ')
        };
      }

      // 检查提交按钮状态
      const submitButtonSelector = '#main > form > div > button.default';
      const submitButton = await this.formFiller.currentPage.$(submitButtonSelector);

      if (!submitButton) {
        this.log('⚠️  未找到提交按钮');
        return {
          hasErrors: true,
          errorCount: 0,
          errorDetails: '未找到提交按钮'
        };
      }

      const isDisabled = await submitButton.evaluate(el => el.disabled);
      const buttonText = await submitButton.evaluate(el => el.textContent.trim());

      this.log(`📊 提交按钮状态: 禁用=${isDisabled}, 文本="${buttonText}"`);

      return {
        hasErrors: false,
        errorCount: 0,
        errorDetails: '',
        submitButtonDisabled: isDisabled,
        submitButtonText: buttonText
      };

    } catch (error) {
      this.log(`⚠️  检查Review页面状态失败: ${error.message}`);
      return {
        hasErrors: true,
        errorCount: 0,
        errorDetails: `检查失败: ${error.message}`
      };
    }
  }

  /**
   * 提交申请
   */
  async submitApplication() {
    this.log('📤 提交申请...');

    try {
      const submitButtonSelector = '#main > form > div > button.default';

      // 等待提交按钮出现
      await this.formFiller.currentPage.waitForSelector(submitButtonSelector, {
        timeout: this.options.dialogTimeout
      });

      const submitButton = await this.formFiller.currentPage.$(submitButtonSelector);
      if (!submitButton) {
        throw new Error('找不到提交按钮');
      }

      // 检查按钮状态
      const isVisible = await submitButton.evaluate(el => el.offsetParent !== null);
      const isDisabled = await submitButton.evaluate(el => el.disabled);
      const buttonText = await submitButton.evaluate(el => el.textContent.trim());

      this.log(`📊 提交按钮状态: 可见=${isVisible}, 禁用=${isDisabled}, 文本="${buttonText}"`);

      if (!isVisible) {
        throw new Error('提交按钮不可见');
      }

      if (isDisabled) {
        this.log('⚠️  提交按钮被禁用，可能存在未完成的必填项');
        throw new Error('提交按钮被禁用，无法提交申请');
      }

      // 截图：提交前
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('before-submit-application');
      }

      // 设置弹窗监听器（在点击按钮之前）
      this.log('👂 设置JavaScript弹窗监听器...');
      let dialogHandled = false;

      const dialogHandler = async (dialog) => {
        if (dialogHandled) return;
        dialogHandled = true;

        this.log(`🚨 检测到JavaScript弹窗: ${dialog.type()}`);
        this.log(`📄 弹窗消息: ${dialog.message()}`);

        try {
          // 截图：弹窗出现时
          if (this.options.enableScreenshots) {
            await this.takeScreenshot('submission-confirmation-alert');
          }

          // 检查是否是预期的确认弹窗
          const message = dialog.message();
          const isExpectedAlert =
            message.includes('I acknowledge that I will be unable to make changes') ||
            message.includes('unable to make changes') ||
            message.includes('accurate, complete, and ready for submission');

          if (isExpectedAlert) {
            this.log('✅ 确认是预期的提交确认弹窗');
          } else {
            this.log('⚠️  弹窗内容不完全匹配，但继续处理');
          }

          // 自动点击确认按钮
          this.log('🔘 自动点击弹窗确认按钮...');
          await dialog.accept();
          this.log('✅ 弹窗确认按钮已自动点击');

          // 等待页面响应
          await new Promise(resolve => setTimeout(resolve, 3000));

          // 截图：弹窗处理后
          if (this.options.enableScreenshots) {
            await this.takeScreenshot('after-submission-confirmation');
          }

          this.log('🎉 申请提交确认完成！');

        } catch (error) {
          this.log(`❌ 处理弹窗失败: ${error.message}`);
          this.log(`❌ 错误详情: ${error.stack}`);

          // 即使失败也要接受弹窗，避免阻塞
          try {
            this.log('🔄 尝试强制接受弹窗...');
            await dialog.accept();
            this.log('✅ 强制接受弹窗成功');
          } catch (acceptError) {
            this.log(`❌ 强制接受弹窗也失败: ${acceptError.message}`);
          }
        }
      };

      // 注册弹窗监听器
      this.formFiller.currentPage.on('dialog', dialogHandler);

      // 额外的弹窗处理：设置页面级别的confirm函数覆盖
      await this.formFiller.currentPage.evaluateOnNewDocument(() => {
        window.confirm = function(message) {
          console.log('页面confirm被调用:', message);
          // 自动返回true，相当于点击OK
          return true;
        };
      });

      // 在当前页面也设置confirm覆盖
      await this.formFiller.currentPage.evaluate(() => {
        window.confirm = function(message) {
          console.log('页面confirm被调用:', message);
          // 自动返回true，相当于点击OK
          return true;
        };
      });

      // 点击提交按钮
      this.log(`🔘 点击提交按钮: "${buttonText}"`);
      await submitButton.click();
      this.log('✅ 提交按钮已点击');

      // 等待弹窗处理（最多15秒）
      this.log('⏳ 等待弹窗出现和处理...');
      let waitTime = 0;
      const maxWaitTime = 15000;
      const checkInterval = 500;

      while (waitTime < maxWaitTime && !dialogHandled) {
        await new Promise(resolve => setTimeout(resolve, checkInterval));
        waitTime += checkInterval;
      }

      // 移除监听器
      this.formFiller.currentPage.off('dialog', dialogHandler);

      if (dialogHandled) {
        this.log('✅ 弹窗处理完成');
      } else {
        this.log('⚠️  未检测到弹窗或处理超时');
      }

      // 等待提交处理
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 截图：提交后
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('after-submit-application');
      }

      // 检查提交结果
      await this.checkSubmissionResult();

    } catch (error) {
      this.log(`❌ 提交申请失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 处理提交确认弹窗（已集成到submitApplication方法中）
   */
  async handleSubmissionConfirmationDialog() {
    this.log('ℹ️  弹窗处理已集成到submitApplication方法中');
    // 此方法保留用于向后兼容，实际处理在submitApplication中
  }

  /**
   * 检查提交结果
   */
  async checkSubmissionResult() {
    this.log('🔍 检查提交结果...');

    try {
      // 等待页面响应
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 获取当前页面URL和标题
      const currentUrl = this.formFiller.currentPage.url();
      const pageTitle = await this.formFiller.currentPage.title();

      this.log(`📊 提交后状态:`);
      this.log(`   URL: ${currentUrl}`);
      this.log(`   标题: ${pageTitle}`);

      // 检查是否有成功消息
      const successSelectors = [
        '.success',
        '.alert-success',
        '.confirmation',
        '[class*="success"]',
        '[class*="confirm"]'
      ];

      let successFound = false;
      for (const selector of successSelectors) {
        try {
          const successElement = await this.formFiller.currentPage.$(selector);
          if (successElement) {
            const successText = await successElement.evaluate(el => el.textContent.trim());
            if (successText) {
              this.log(`✅ 发现成功消息: ${successText}`);
              successFound = true;
              break;
            }
          }
        } catch (error) {
          // 忽略单个选择器的错误
        }
      }

      // 检查是否有错误消息
      const errorSelectors = [
        '.error',
        '.alert-danger',
        '.alert-error',
        '[class*="error"]'
      ];

      let errorFound = false;
      for (const selector of errorSelectors) {
        try {
          const errorElement = await this.formFiller.currentPage.$(selector);
          if (errorElement) {
            const errorText = await errorElement.evaluate(el => el.textContent.trim());
            if (errorText) {
              this.log(`❌ 发现错误消息: ${errorText}`);
              errorFound = true;
            }
          }
        } catch (error) {
          // 忽略单个选择器的错误
        }
      }

      if (successFound) {
        this.log('🎉 申请提交成功！');

        // 保存成功申请的数据到Excel
        await this.saveSuccessfulApplicationData();

      } else if (errorFound) {
        this.log('❌ 申请提交可能失败，发现错误消息');
      } else {
        this.log('ℹ️  申请已提交，等待确认结果');

        // 即使没有明确的成功消息，也保存数据（可能是成功的）
        this.log('💾 保存申请数据作为备份...');
        await this.saveSuccessfulApplicationData();
      }

    } catch (error) {
      this.log(`⚠️  检查提交结果失败: ${error.message}`);
    }
  }

  /**
   * 格式化生日数据
   */
  formatBirthDate() {
    try {
      const month = this.studentData.birthMonth || '';
      const day = this.studentData.birthDay || '';
      const year = this.studentData.birthYear || '';

      if (month && day && year) {
        return `${month}/${day}/${year}`;
      }

      return '';
    } catch (error) {
      this.log(`⚠️  格式化生日失败: ${error.message}`);
      return '';
    }
  }

  /**
   * 保存成功申请的数据到Excel
   */
  async saveSuccessfulApplicationData() {
    this.log('💾 保存成功申请数据到Excel...');

    try {
      const XLSX = require('xlsx');
      const fs = require('fs');
      const path = require('path');

      // 成功申请数据文件路径
      const successFilePath = path.join(process.cwd(), 'successful_applications.xlsx');

      // 准备要保存的数据（使用正确的字段映射）
      const applicationData = {
        '提交时间': new Date().toLocaleString('zh-CN'),
        '邮箱': this.studentData.email || '',
        '密码': this.studentData.password || '',
        '姓名': `${this.studentData.firstName || ''} ${this.studentData.lastName || ''}`.trim(),
        '生日': this.formatBirthDate(),
        '性别': this.studentData.gender || '',
        '电话': this.studentData.phone || '',
        '完整地址': this.studentData.completeAddress || '',
        'SSN': this.studentData.ssn || '',
        '申请状态': '提交成功',
        '申请类型': 'Nondegree',
        '备注': '自动化申请系统提交'
      };

      this.log('📋 准备保存的数据:');
      Object.entries(applicationData).forEach(([key, value]) => {
        this.log(`   ${key}: ${value}`);
      });

      let workbook;
      let worksheet;

      // 检查文件是否存在
      if (fs.existsSync(successFilePath)) {
        this.log('📂 读取现有的成功申请文件...');

        // 读取现有文件
        workbook = XLSX.readFile(successFilePath);
        const sheetName = workbook.SheetNames[0] || 'SuccessfulApplications';
        worksheet = workbook.Sheets[sheetName];

        // 转换为JSON数组
        const existingData = XLSX.utils.sheet_to_json(worksheet);
        this.log(`📊 现有记录数: ${existingData.length}`);

        // 添加新数据
        existingData.push(applicationData);

        // 重新创建工作表
        worksheet = XLSX.utils.json_to_sheet(existingData);
        workbook.Sheets[sheetName] = worksheet;

      } else {
        this.log('📄 创建新的成功申请文件...');

        // 创建新的工作簿和工作表
        workbook = XLSX.utils.book_new();
        worksheet = XLSX.utils.json_to_sheet([applicationData]);
        XLSX.utils.book_append_sheet(workbook, worksheet, 'SuccessfulApplications');
      }

      // 设置列宽
      const columnWidths = [
        { wch: 20 }, // 提交时间
        { wch: 30 }, // 邮箱
        { wch: 15 }, // 密码
        { wch: 20 }, // 姓名
        { wch: 12 }, // 生日
        { wch: 8 },  // 性别
        { wch: 15 }, // 电话
        { wch: 40 }, // 完整地址
        { wch: 12 }, // SSN
        { wch: 12 }, // 申请状态
        { wch: 12 }, // 申请类型
        { wch: 20 }  // 备注
      ];

      worksheet['!cols'] = columnWidths;

      // 保存文件
      XLSX.writeFile(workbook, successFilePath);

      this.log(`✅ 成功申请数据已保存到: ${successFilePath}`);

      // 获取文件统计信息
      const stats = fs.statSync(successFilePath);
      this.log(`📊 文件大小: ${(stats.size / 1024).toFixed(2)} KB`);

      // 读取并显示总记录数
      const finalWorkbook = XLSX.readFile(successFilePath);
      const finalWorksheet = finalWorkbook.Sheets[finalWorkbook.SheetNames[0]];
      const finalData = XLSX.utils.sheet_to_json(finalWorksheet);
      this.log(`📈 总成功申请记录数: ${finalData.length}`);

    } catch (error) {
      this.log(`❌ 保存成功申请数据失败: ${error.message}`);
      this.log(`❌ 错误详情: ${error.stack}`);

      // 即使保存失败，也不影响主流程
      this.log('⚠️  数据保存失败，但申请流程已完成');
    }
  }

  /**
   * 动态查找日期选择器
   */
  async findDateSelectors(dateType) {
    this.log(`🔍 查找${dateType}日期选择器...`);

    try {
      // 获取对话框中的所有select元素
      const selects = await this.formFiller.currentPage.$$('body > div.dialog_host.ui-draggable select');

      this.log(`📋 找到 ${selects.length} 个选择框`);

      const selectors = { month: null, year: null };

      for (let i = 0; i < selects.length; i++) {
        const select = selects[i];
        const id = await select.evaluate(el => el.id);
        const name = await select.evaluate(el => el.name);

        this.log(`   选择框 ${i + 1}: id="${id}", name="${name}"`);

        // 判断是否为月份选择器
        if (id.includes('_m') || name.includes('month')) {
          if (dateType === 'start' && (i === 0 || id.includes('start') || id.includes('from'))) {
            selectors.month = `#${id}`;
          } else if (dateType === 'end' && (i > 0 || id.includes('end') || id.includes('to'))) {
            selectors.month = `#${id}`;
          }
        }

        // 判断是否为年份选择器
        if (id.includes('_y') || name.includes('year')) {
          if (dateType === 'start' && (i === 0 || id.includes('start') || id.includes('from'))) {
            selectors.year = `#${id}`;
          } else if (dateType === 'end' && (i > 0 || id.includes('end') || id.includes('to'))) {
            selectors.year = `#${id}`;
          }
        }
      }

      this.log(`📊 ${dateType}日期选择器: 月份="${selectors.month}", 年份="${selectors.year}"`);

      return selectors;

    } catch (error) {
      this.log(`❌ 查找日期选择器失败: ${error.message}`);
      return { month: null, year: null };
    }
  }

  /**
   * 选择毕业状态
   */
  async selectGraduationStatus() {
    this.log('🎓 选择毕业状态...');

    try {
      const graduationSelector = '#form_d016e597-011e-4c21-be71-9258f5395b47_2';

      await this.formFiller.currentPage.waitForSelector(graduationSelector, {
        timeout: this.options.dialogTimeout
      });

      const graduationOption = await this.formFiller.currentPage.$(graduationSelector);
      if (!graduationOption) {
        throw new Error('找不到毕业状态选项');
      }

      // 检查选项状态
      const isVisible = await graduationOption.evaluate(el => el.offsetParent !== null);
      const isChecked = await graduationOption.evaluate(el => el.checked);

      this.log(`📊 毕业状态选项: 可见=${isVisible}, 已选中=${isChecked}`);

      if (!isVisible) {
        throw new Error('毕业状态选项不可见');
      }

      if (!isChecked) {
        await graduationOption.click();
        this.log('✅ 毕业状态已选择');
      } else {
        this.log('ℹ️  毕业状态已经选中');
      }

    } catch (error) {
      this.log(`❌ 选择毕业状态失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 保存学校信息
   */
  async saveSchoolInformation() {
    this.log('💾 保存学校信息...');

    try {
      const saveButtonSelector = 'body > div.dialog_host.ui-draggable > div > form > div.action > button.default';

      await this.formFiller.currentPage.waitForSelector(saveButtonSelector, {
        timeout: this.options.dialogTimeout
      });

      const saveButton = await this.formFiller.currentPage.$(saveButtonSelector);
      if (!saveButton) {
        throw new Error('找不到保存按钮');
      }

      // 检查按钮状态
      const isVisible = await saveButton.evaluate(el => el.offsetParent !== null);
      const isDisabled = await saveButton.evaluate(el => el.disabled);
      const buttonText = await saveButton.evaluate(el => el.textContent.trim());

      this.log(`📊 保存按钮状态: 可见=${isVisible}, 禁用=${isDisabled}, 文本="${buttonText}"`);

      if (!isVisible) {
        throw new Error('保存按钮不可见');
      }

      if (isDisabled) {
        throw new Error('保存按钮被禁用');
      }

      // 截图：保存前
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('before-save-school');
      }

      await saveButton.click();
      this.log('✅ 保存按钮已点击');

      // 等待保存完成和对话框关闭
      await this.waitForDialogClose();

    } catch (error) {
      this.log(`❌ 保存学校信息失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 等待对话框关闭
   */
  async waitForDialogClose() {
    this.log('⏳ 等待对话框关闭...');

    try {
      const dialogSelector = 'body > div.dialog_host.ui-draggable > div > form';

      // 等待对话框消失
      await this.formFiller.currentPage.waitForFunction(
        (selector) => {
          const element = document.querySelector(selector);
          return !element || element.offsetParent === null;
        },
        { timeout: this.options.dialogTimeout },
        dialogSelector
      );

      this.log('✅ 对话框已关闭');

      // 截图：对话框关闭后
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('after-dialog-close');
      }

    } catch (error) {
      this.log(`⚠️  等待对话框关闭超时: ${error.message}`);
      // 不抛出错误，继续处理
    }
  }

  /**
   * 验证表单容器
   */
  async verifyFormContainer() {
    this.log('🔍 验证申请信息表单容器...');

    try {
      const formContainerSelector = '#form_e25f6bd4-ed77-431f-bf2d-efc8615a675f > div.form_responses';

      this.log(`🔍 等待表单容器: ${formContainerSelector}`);
      await this.formFiller.currentPage.waitForSelector(formContainerSelector, {
        timeout: this.options.dialogTimeout
      });

      const formContainer = await this.formFiller.currentPage.$(formContainerSelector);
      if (!formContainer) {
        throw new Error('找不到申请信息表单容器');
      }

      this.log('✅ 申请信息表单容器验证通过');

      // 截图：表单容器
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('application-form-container');
      }

    } catch (error) {
      this.log(`❌ 表单容器验证失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 执行顺序表单选择
   */
  async executeSequentialFormSelection(completionStatus = null) {
    this.log('🔄 执行顺序表单选择流程...');

    // 定义选择序列
    const selectionSequence = [
      {
        step: 1,
        selector: '#form_e25f6bd4-ed77-431f-bf2d-efc8615a675f_2',
        description: '第一个选择项'
      },
      {
        step: 2,
        selector: '#form_edb6bd5a-1c5e-457d-b4b6-97314f7840c2_2',
        description: '第二个选择项'
      },
      {
        step: 3,
        selector: '#form_39e5db3d-2a77-4f3c-ba9c-54bebe9528bd_2',
        description: '第三个选择项'
      },
      {
        step: 4,
        selector: '#form_60070daf-0765-4d0f-bd2f-cc79ed50c4ce_2',
        description: '第四个选择项'
      },
      {
        step: 5,
        selector: '#form_83fc6010-88e2-40fd-a456-7b3b92877248_3',
        description: '第五个选择项 (Nondegree)',
        dataText: 'Nondegree'
      },
      {
        step: 6,
        selector: '#form_6ff0815b-1c44-4b9f-bf2c-cfcb7c030afb_4',
        description: '第六个选择项'
      }
    ];

    try {
      for (const selection of selectionSequence) {
        // 智能检查是否已选择
        const alreadySelected = await this.checkSelectionAlreadyMade(selection, completionStatus);

        if (alreadySelected) {
          this.log(`ℹ️  步骤${selection.step} ${selection.description} 已选择，跳过`);
          continue;
        }

        await this.executeFormSelection(selection);

        // 在每个选择之间等待
        await new Promise(resolve => setTimeout(resolve, 1500));
      }

      this.log('✅ 所有表单选择完成');

    } catch (error) {
      this.log(`❌ 顺序表单选择失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 检查选择是否已完成
   */
  async checkSelectionAlreadyMade(selection, completionStatus) {
    try {
      // 如果有完成状态信息，先检查
      if (completionStatus && completionStatus.filledFields) {
        const matchingField = completionStatus.filledFields.find(field =>
          field.selector === selection.selector
        );

        if (matchingField) {
          this.log(`📊 从状态检测发现 ${selection.description} 已选择: ${matchingField.value}`);
          return true;
        }
      }

      // 实时检查元素状态
      const isSelected = await this.checkFormElementSelected(selection.selector);

      if (isSelected) {
        this.log(`🔍 实时检测发现 ${selection.description} 已选择`);
        return true;
      }

      return false;

    } catch (error) {
      this.log(`⚠️  检查选择状态失败: ${error.message}`);
      return false; // 默认为未选择，继续执行
    }
  }

  /**
   * 执行单个表单选择
   */
  async executeFormSelection(selection) {
    this.log(`\n🔘 步骤${selection.step}: ${selection.description}`);
    this.log(`   选择器: ${selection.selector}`);

    try {
      // 等待元素出现
      this.log(`⏳ 等待元素出现...`);

      let elementFound = false;
      let attempts = 0;
      const maxAttempts = 3;

      while (!elementFound && attempts < maxAttempts) {
        attempts++;
        this.log(`🔄 第${attempts}次尝试查找元素...`);

        try {
          await this.formFiller.currentPage.waitForSelector(selection.selector, {
            timeout: this.options.dialogTimeout
          });
          elementFound = true;
          this.log('✅ 元素找到');
        } catch (waitError) {
          this.log(`⚠️  第${attempts}次查找失败: ${waitError.message}`);

          if (attempts < maxAttempts) {
            this.log('⏳ 等待2秒后重试...');
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        }
      }

      if (!elementFound) {
        throw new Error(`元素未找到: ${selection.selector}`);
      }

      // 验证元素属性（如果有特定要求）
      if (selection.dataText) {
        await this.verifyElementDataText(selection.selector, selection.dataText);
      }

      // 获取元素信息
      const elementInfo = await this.formFiller.currentPage.$eval(selection.selector, el => ({
        visible: el.offsetParent !== null,
        disabled: el.disabled,
        text: el.textContent.trim(),
        dataText: el.getAttribute('data-text'),
        tagName: el.tagName.toLowerCase()
      }));

      this.log('📊 元素信息:');
      this.log(`   可见: ${elementInfo.visible}`);
      this.log(`   禁用: ${elementInfo.disabled}`);
      this.log(`   文本: "${elementInfo.text}"`);
      this.log(`   数据文本: "${elementInfo.dataText}"`);
      this.log(`   标签: ${elementInfo.tagName}`);

      if (!elementInfo.visible) {
        throw new Error('元素不可见');
      }

      if (elementInfo.disabled) {
        throw new Error('元素被禁用');
      }

      // 截图：选择前
      if (this.options.enableScreenshots) {
        await this.takeScreenshot(`before-selection-step-${selection.step}`);
      }

      // 执行点击
      const element = await this.formFiller.currentPage.$(selection.selector);
      await element.click();
      this.log(`✅ 步骤${selection.step}选择完成`);

      // 等待选择生效
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 截图：选择后
      if (this.options.enableScreenshots) {
        await this.takeScreenshot(`after-selection-step-${selection.step}`);
      }

      // 验证选择结果
      await this.verifySelectionResult(selection);

    } catch (error) {
      this.log(`❌ 步骤${selection.step}选择失败: ${error.message}`);

      // 详细错误调试
      await this.debugSelectionError(selection, error);
      throw error;
    }
  }

  /**
   * 验证元素数据文本
   */
  async verifyElementDataText(selector, expectedDataText) {
    this.log(`🔍 验证元素数据文本: ${expectedDataText}`);

    try {
      const actualDataText = await this.formFiller.currentPage.$eval(selector, el =>
        el.getAttribute('data-text')
      );

      if (actualDataText === expectedDataText) {
        this.log(`✅ 数据文本验证通过: "${actualDataText}"`);
      } else {
        this.log(`⚠️  数据文本不匹配: 期望="${expectedDataText}", 实际="${actualDataText}"`);
        // 不抛出错误，只是警告
      }

    } catch (error) {
      this.log(`⚠️  数据文本验证失败: ${error.message}`);
    }
  }

  /**
   * 验证选择结果
   */
  async verifySelectionResult(selection) {
    this.log(`🔍 验证步骤${selection.step}选择结果...`);

    try {
      // 检查元素是否被选中或激活
      const selectionResult = await this.formFiller.currentPage.$eval(selection.selector, el => ({
        selected: el.selected || el.checked || el.classList.contains('selected') || el.classList.contains('active'),
        className: el.className,
        ariaSelected: el.getAttribute('aria-selected')
      }));

      this.log('📊 选择结果:');
      this.log(`   选中状态: ${selectionResult.selected}`);
      this.log(`   类名: "${selectionResult.className}"`);
      this.log(`   ARIA选中: ${selectionResult.ariaSelected}`);

      // 如果有明确的选中指标，验证它
      if (selectionResult.selected || selectionResult.ariaSelected === 'true' ||
          selectionResult.className.includes('selected') || selectionResult.className.includes('active')) {
        this.log(`✅ 步骤${selection.step}选择验证通过`);
      } else {
        this.log(`⚠️  步骤${selection.step}选择状态不明确，但继续处理`);
      }

    } catch (error) {
      this.log(`⚠️  选择结果验证失败: ${error.message}`);
      // 不抛出错误，继续处理
    }
  }

  /**
   * 调试选择错误
   */
  async debugSelectionError(selection, error) {
    this.log(`🔍 步骤${selection.step}选择错误调试:`);
    this.log(`   错误: ${error.message}`);

    try {
      // 截图当前状态
      if (this.options.enableScreenshots) {
        await this.takeScreenshot(`selection-error-step-${selection.step}`);
      }

      // 查找相似的元素
      const similarElements = await this.formFiller.currentPage.$$eval(
        `[id*="form_"][id*="_"]`,
        elements => elements.map((el, index) => ({
          index: index,
          id: el.id,
          visible: el.offsetParent !== null,
          text: el.textContent.trim().substring(0, 50)
        }))
      );

      if (similarElements.length > 0) {
        this.log('📊 页面上的相似表单元素:');
        similarElements.slice(0, 10).forEach(el => {
          this.log(`   ${el.index + 1}. ID: ${el.id} ${el.visible ? '[可见]' : '[隐藏]'}`);
          if (el.text) {
            this.log(`      文本: "${el.text}"`);
          }
        });
      }

    } catch (debugError) {
      this.log(`⚠️  选择错误调试失败: ${debugError.message}`);
    }
  }

  /**
   * 完成表单提交
   */
  async completeFormSubmission() {
    this.log('🔘 完成表单提交...');

    try {
      const submitButtonSelector = '#main > form > div.action > button';

      this.log(`🔍 等待提交按钮: ${submitButtonSelector}`);
      await this.formFiller.currentPage.waitForSelector(submitButtonSelector, {
        timeout: this.options.dialogTimeout
      });

      // 获取所有匹配的按钮
      const buttons = await this.formFiller.currentPage.$$(submitButtonSelector);

      if (buttons.length === 0) {
        throw new Error('找不到提交按钮');
      }

      this.log(`📊 找到 ${buttons.length} 个提交按钮`);

      // 使用第一个按钮
      const submitButton = buttons[0];

      // 获取按钮信息
      const buttonInfo = await submitButton.evaluate(el => ({
        text: el.textContent.trim(),
        disabled: el.disabled,
        visible: el.offsetParent !== null,
        type: el.type,
        className: el.className
      }));

      this.log('📊 提交按钮信息:');
      this.log(`   文本: "${buttonInfo.text}"`);
      this.log(`   禁用: ${buttonInfo.disabled}`);
      this.log(`   可见: ${buttonInfo.visible}`);
      this.log(`   类型: ${buttonInfo.type}`);
      this.log(`   类名: "${buttonInfo.className}"`);

      if (!buttonInfo.visible) {
        throw new Error('提交按钮不可见');
      }

      if (buttonInfo.disabled) {
        this.log('⚠️  提交按钮被禁用，等待启用...');

        // 等待按钮启用
        await this.formFiller.currentPage.waitForFunction(
          (selector) => {
            const buttons = document.querySelectorAll(selector);
            return buttons.length > 0 && !buttons[0].disabled;
          },
          { timeout: this.options.dialogTimeout },
          submitButtonSelector
        );

        this.log('✅ 提交按钮已启用');
      }

      // 截图：提交前
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('before-form-submission');
      }

      this.log('✅ 找到提交按钮，准备点击');
      await submitButton.click();
      this.log('✅ 表单提交按钮已点击');

      // 等待页面响应
      await new Promise(resolve => setTimeout(resolve, this.options.waitAfterClick));

      // 等待页面导航或更新
      try {
        await this.formFiller.currentPage.waitForNavigation({
          timeout: this.options.dialogTimeout,
          waitUntil: 'domcontentloaded'
        });
        this.log('✅ 表单提交后页面导航完成');
      } catch (error) {
        this.log('⚠️  表单提交后页面导航超时，但继续处理');
      }

      // 截图：提交后
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('after-form-submission');
      }

    } catch (error) {
      this.log(`❌ 表单提交失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 填写地址信息
   */
  async fillAddressInformation() {
    this.log('📝 填写地址信息...');

    try {
      // 从学生数据中获取完整地址
      const completeAddress = this.studentData.completeAddress || this.studentData['完整地址'];

      if (!completeAddress) {
        throw new Error('学生数据中缺少完整地址信息');
      }

      this.log(`📍 完整地址: ${completeAddress}`);

      // 解析地址
      const addressParts = this.parseCompleteAddress(completeAddress);

      this.log('📊 地址解析结果:');
      this.log(`   街道: "${addressParts.street}"`);
      this.log(`   城市: "${addressParts.city}"`);
      this.log(`   州: "${addressParts.state}" (全名: "${addressParts.stateFull}")`);
      this.log(`   邮编: "${addressParts.postal}"`);

      // 填写街道地址
      await this.fillFormField('#address_1_street', addressParts.street, '街道地址');

      // 填写城市
      await this.fillFormField('#address_1_city', addressParts.city, '城市');

      // 选择州
      await this.selectStateInDropdown('#address_1_region', addressParts.stateFull, '州');

      // 填写邮编
      await this.fillFormField('#address_1_postal', addressParts.postal, '邮编');

      // 验证地址填写
      await this.verifyAddressFields(addressParts);

      this.log('✅ 地址信息填写完成');

    } catch (error) {
      this.log(`❌ 填写地址信息失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 解析完整地址
   */
  parseCompleteAddress(completeAddress) {
    this.log('🔍 解析完整地址...');

    try {
      // 支持多种地址格式:
      // 格式1: "Camp Chase Trail, Columbus, OH 43328, United States" (5部分)
      // 格式2: "2224 Ashley St,Beaumont,TX,77701" (4部分)
      // 格式3: "Street, City, State Zip" (3部分)
      const parts = completeAddress.split(',').map(part => part.trim());

      this.log(`📊 地址分割结果: ${parts.length}个部分`);
      parts.forEach((part, index) => {
        this.log(`   部分${index + 1}: "${part}"`);
      });

      let street, city, stateAbbr, postal;

      // 通用函数：解析州和邮编组合
      const parseStateAndPostal = (stateAndPostal) => {
        this.log(`🔍 解析州和邮编组合: "${stateAndPostal}"`);

        // 尝试匹配 "NJ 07032" 格式
        let match = stateAndPostal.match(/^([A-Z]{2})\s+(\d{5}(?:-\d{4})?)$/);

        if (!match) {
          // 尝试匹配 "TX77701" 格式（无空格）
          match = stateAndPostal.match(/^([A-Z]{2})(\d{5}(?:-\d{4})?)$/);
        }

        if (match) {
          return {
            state: match[1],
            postal: match[2]
          };
        }

        return null;
      };

      if (parts.length >= 5) {
        // 格式1: 5部分或更多 "Street, City, State Zip, Country"
        // 例如: "241 Davis Avenue, Kearny, NJ 07032, United States"
        street = parts[0];
        city = parts[1];

        // 检查第三部分是否包含州和邮编组合
        const statePostalPart = parts[2].trim();
        const parsed = parseStateAndPostal(statePostalPart);

        if (parsed) {
          // 第三部分是"NJ 07032"格式
          stateAbbr = parsed.state;
          postal = parsed.postal;
        } else {
          // 第三部分只有州，第四部分是邮编
          stateAbbr = statePostalPart;
          postal = parts[3].trim();
        }
      } else if (parts.length === 4) {
        // 格式2: 4部分，需要判断是哪种子格式
        street = parts[0];
        city = parts[1];

        // 检查第三部分是否包含州和邮编组合
        const thirdPart = parts[2].trim();
        const parsed = parseStateAndPostal(thirdPart);

        if (parsed) {
          // 第三部分是"WA 98663"格式，第四部分是国家
          stateAbbr = parsed.state;
          postal = parsed.postal;
          this.log(`📍 4部分格式 - 第三部分包含州和邮编: "${thirdPart}"`);
        } else {
          // 第三部分只有州，第四部分是邮编
          // 例如: "2224 Ashley St,Beaumont,TX,77701"
          stateAbbr = thirdPart;
          postal = parts[3].trim();
          this.log(`📍 4部分格式 - 第三部分是州，第四部分是邮编`);
        }
      } else if (parts.length === 3) {
        // 格式3: 3部分 "Street, City, State Zip"
        street = parts[0];
        city = parts[1];
        const stateAndPostal = parts[2].trim();

        // 解析州和邮编组合
        const parsed = parseStateAndPostal(stateAndPostal);

        if (!parsed) {
          throw new Error(`州和邮编格式不正确: "${stateAndPostal}"`);
        }

        stateAbbr = parsed.state;
        postal = parsed.postal;
      } else {
        throw new Error('地址格式不正确，应包含至少街道、城市、州、邮编');
      }

      // 验证州缩写格式
      if (!/^[A-Z]{2}$/.test(stateAbbr)) {
        throw new Error(`州缩写格式不正确: "${stateAbbr}"`);
      }

      // 验证邮编格式
      if (!/^\d{5}(?:-\d{4})?$/.test(postal)) {
        throw new Error(`邮编格式不正确: "${postal}"`);
      }

      // 州缩写到全名的映射
      const stateMapping = {
        'AL': 'Alabama', 'AK': 'Alaska', 'AZ': 'Arizona', 'AR': 'Arkansas', 'CA': 'California',
        'CO': 'Colorado', 'CT': 'Connecticut', 'DE': 'Delaware', 'FL': 'Florida', 'GA': 'Georgia',
        'HI': 'Hawaii', 'ID': 'Idaho', 'IL': 'Illinois', 'IN': 'Indiana', 'IA': 'Iowa',
        'KS': 'Kansas', 'KY': 'Kentucky', 'LA': 'Louisiana', 'ME': 'Maine', 'MD': 'Maryland',
        'MA': 'Massachusetts', 'MI': 'Michigan', 'MN': 'Minnesota', 'MS': 'Mississippi', 'MO': 'Missouri',
        'MT': 'Montana', 'NE': 'Nebraska', 'NV': 'Nevada', 'NH': 'New Hampshire', 'NJ': 'New Jersey',
        'NM': 'New Mexico', 'NY': 'New York', 'NC': 'North Carolina', 'ND': 'North Dakota', 'OH': 'Ohio',
        'OK': 'Oklahoma', 'OR': 'Oregon', 'PA': 'Pennsylvania', 'RI': 'Rhode Island', 'SC': 'South Carolina',
        'SD': 'South Dakota', 'TN': 'Tennessee', 'TX': 'Texas', 'UT': 'Utah', 'VT': 'Vermont',
        'VA': 'Virginia', 'WA': 'Washington', 'WV': 'West Virginia', 'WI': 'Wisconsin', 'WY': 'Wyoming'
      };

      const stateFull = stateMapping[stateAbbr] || stateAbbr;

      this.log(`✅ 地址解析成功:`);
      this.log(`   街道: "${street}"`);
      this.log(`   城市: "${city}"`);
      this.log(`   州缩写: "${stateAbbr}"`);
      this.log(`   州全名: "${stateFull}"`);
      this.log(`   邮编: "${postal}"`);

      return {
        street: street,
        city: city,
        state: stateAbbr,
        stateFull: stateFull,
        postal: postal
      };

    } catch (error) {
      this.log(`❌ 地址解析失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 填写表单字段（带错误恢复机制）
   */
  async fillFormField(selector, value, description) {
    this.log(`📝 填写${description}: ${value}`);
    this.log(`🔍 使用选择器: ${selector}`);

    // 最多重试3次
    for (let attempt = 1; attempt <= 3; attempt++) {
      try {
        this.log(`⏳ 第${attempt}次尝试 - 等待字段出现: ${selector}`);
        await this.formFiller.currentPage.waitForSelector(selector, {
          timeout: this.options.dialogTimeout
        });

        // 重新获取元素（避免JavaScript世界不匹配问题）
        const field = await this.formFiller.currentPage.$(selector);
        if (!field) {
          throw new Error(`找不到${description}字段: ${selector}`);
        }

        this.log(`✅ 字段找到，开始填写`);

        // 检查字段状态（使用页面级别的evaluate避免元素引用问题）
        const fieldStatus = await this.formFiller.currentPage.evaluate((sel) => {
          const element = document.querySelector(sel);
          if (!element) return { exists: false };

          return {
            exists: true,
            visible: element.offsetParent !== null,
            disabled: element.disabled,
            value: element.value || ''
          };
        }, selector);

        if (!fieldStatus.exists) {
          throw new Error(`${description}字段不存在`);
        }

        this.log(`📊 字段状态: 可见=${fieldStatus.visible}, 禁用=${fieldStatus.disabled}`);

        if (!fieldStatus.visible) {
          throw new Error(`${description}字段不可见`);
        }

        if (fieldStatus.disabled) {
          throw new Error(`${description}字段被禁用`);
        }

        // 清空并填写 - 使用页面级别的操作避免元素引用问题
        this.log(`🖱️  点击字段并清空`);

        const useSpecialClear = selector.includes('postal') || description.includes('邮编') ||
                               selector.includes('form_6ec61e72-6946-4456-a1eb-67c692cbf515_city') ||
                               description.includes('紧急联系人城市') ||
                               description.includes('紧急联系人名字') ||
                               description.includes('紧急联系人姓氏');

        if (useSpecialClear) {
          // 特殊字段使用页面级别的强力清空方式
          this.log(`🧹 特殊字段使用强力清空方式`);
          await this.formFiller.currentPage.evaluate((sel) => {
            const element = document.querySelector(sel);
            if (element) {
              element.focus();
              element.value = '';
              element.dispatchEvent(new Event('input', { bubbles: true }));
              element.dispatchEvent(new Event('change', { bubbles: true }));
            }
          }, selector);
          await new Promise(resolve => setTimeout(resolve, 200));
        } else {
          // 普通字段使用三次点击选中
          await field.click({ clickCount: 3 });
        }

        this.log(`⌨️  输入值: ${value}`);
        await field.type(value, { delay: 50 });

        // 等待输入完成
        await new Promise(resolve => setTimeout(resolve, 500));

        // 验证填写结果（使用页面级别的evaluate）
        const finalValue = await this.formFiller.currentPage.evaluate((sel) => {
          const element = document.querySelector(sel);
          return element ? element.value : '';
        }, selector);

        this.log(`🔍 验证填写结果: 期望="${value}", 实际="${finalValue}"`);

        if (finalValue === value) {
          this.log(`✅ ${description}填写成功`);
          return; // 成功，退出重试循环
        } else {
          this.log(`⚠️  ${description}填写可能不完整: 期望="${value}", 实际="${finalValue}"`);
          return; // 虽然不完全匹配，但也算成功
        }

      } catch (error) {
        this.log(`❌ 第${attempt}次尝试填写${description}失败: ${error.message}`);

        if (error.message.includes('JavaScript world') || error.message.includes('Execution context')) {
          this.log(`🔄 检测到JavaScript上下文错误，等待后重试...`);
          await new Promise(resolve => setTimeout(resolve, 1000));

          if (attempt < 3) {
            continue; // 重试
          }
        }

        if (attempt === 3) {
          this.log(`❌ 所有重试都失败，抛出错误`);
          this.log(`❌ 错误详情: ${error.stack}`);
          throw error;
        }
      }
    }
  }

  /**
   * 填写文本区域字段
   */
  async fillTextareaField(selector, value, description) {
    this.log(`📝 填写${description}: ${value}`);
    this.log(`🔍 使用选择器: ${selector}`);

    try {
      this.log(`⏳ 等待文本区域出现: ${selector}`);
      await this.formFiller.currentPage.waitForSelector(selector, {
        timeout: this.options.dialogTimeout
      });

      const field = await this.formFiller.currentPage.$(selector);
      if (!field) {
        throw new Error(`找不到${description}字段: ${selector}`);
      }

      this.log(`✅ 文本区域找到，开始填写`);

      // 检查字段状态
      const isVisible = await field.evaluate(el => el.offsetParent !== null);
      const isDisabled = await field.evaluate(el => el.disabled);
      const tagName = await field.evaluate(el => el.tagName);

      this.log(`📊 文本区域状态: 可见=${isVisible}, 禁用=${isDisabled}, 标签=${tagName}`);

      if (!isVisible) {
        throw new Error(`${description}字段不可见`);
      }

      if (isDisabled) {
        throw new Error(`${description}字段被禁用`);
      }

      // 清空并填写textarea
      this.log(`🖱️  点击文本区域并清空`);
      await field.click({ clickCount: 3 });

      this.log(`⌨️  输入值: ${value}`);
      await field.type(value, { delay: 50 });

      // 等待输入完成
      await new Promise(resolve => setTimeout(resolve, 500));

      // 验证填写结果
      const fieldValue = await field.evaluate(el => el.value);
      this.log(`🔍 验证填写结果: 期望="${value}", 实际="${fieldValue}"`);

      if (fieldValue === value) {
        this.log(`✅ ${description}填写成功`);
      } else {
        this.log(`⚠️  ${description}填写可能不完整: 期望="${value}", 实际="${fieldValue}"`);
      }

    } catch (error) {
      this.log(`❌ 填写${description}失败: ${error.message}`);
      this.log(`❌ 错误详情: ${error.stack}`);
      throw error;
    }
  }

  /**
   * 选择表单选项
   */
  async selectFormOption(selector, targetValue, description) {
    this.log(`📋 选择${description}: ${targetValue}`);
    this.log(`🔍 使用选择器: ${selector}`);

    try {
      this.log(`⏳ 等待下拉框出现: ${selector}`);
      await this.formFiller.currentPage.waitForSelector(selector, {
        timeout: this.options.dialogTimeout
      });

      // 检查下拉框状态
      const selectElement = await this.formFiller.currentPage.$(selector);
      if (!selectElement) {
        throw new Error(`找不到${description}下拉框: ${selector}`);
      }

      const isVisible = await selectElement.evaluate(el => el.offsetParent !== null);
      const isDisabled = await selectElement.evaluate(el => el.disabled);

      this.log(`📊 下拉框状态: 可见=${isVisible}, 禁用=${isDisabled}`);

      if (!isVisible) {
        throw new Error(`${description}下拉框不可见`);
      }

      if (isDisabled) {
        throw new Error(`${description}下拉框被禁用`);
      }

      // 获取所有选项
      this.log(`🔍 获取所有选项...`);
      const options = await this.formFiller.currentPage.$$eval(`${selector} option`, options =>
        options.map(option => ({
          value: option.value,
          text: option.textContent.trim(),
          dataText: option.getAttribute('data-text')
        }))
      );

      this.log(`📋 找到 ${options.length} 个选项:`);
      options.forEach((option, index) => {
        this.log(`   ${index + 1}. "${option.text}" (值: ${option.value}, data-text: ${option.dataText})`);
      });

      // 查找匹配的选项
      this.log(`🔍 查找匹配选项: "${targetValue}"`);
      const matchingOption = options.find(option =>
        option.text === targetValue ||
        option.dataText === targetValue ||
        option.value === targetValue ||
        option.text.toLowerCase().includes(targetValue.toLowerCase())
      );

      if (matchingOption) {
        this.log(`✅ 找到匹配选项: "${matchingOption.text}" (值: ${matchingOption.value})`);
        await this.formFiller.currentPage.select(selector, matchingOption.value);
        this.log(`✅ ${description}选择成功: ${matchingOption.text}`);
      } else {
        this.log(`⚠️  未找到匹配的"${targetValue}"选项`);
        // 如果没找到匹配项，使用第一个非空选项
        const availableOptions = options.filter(option => option.value && option.value.trim() !== '');
        if (availableOptions.length > 0) {
          this.log(`🔄 使用第一个可用选项: "${availableOptions[0].text}"`);
          await this.formFiller.currentPage.select(selector, availableOptions[0].value);
          this.log(`⚠️  未找到"${targetValue}"选项，使用第一个可用选项: ${availableOptions[0].text}`);
        } else {
          throw new Error(`没有可用的选项用于${description}`);
        }
      }

      // 等待选择生效
      await new Promise(resolve => setTimeout(resolve, 500));

    } catch (error) {
      this.log(`❌ 选择${description}失败: ${error.message}`);
      this.log(`❌ 错误详情: ${error.stack}`);
      throw error;
    }
  }

  /**
   * 在下拉框中选择州
   */
  async selectStateInDropdown(selector, stateName, description) {
    this.log(`📋 选择${description}: ${stateName}`);

    try {
      await this.formFiller.currentPage.waitForSelector(selector, {
        timeout: this.options.dialogTimeout
      });

      // 获取所有选项
      const options = await this.formFiller.currentPage.$$eval(`${selector} option`, options =>
        options.map(option => ({
          value: option.value,
          text: option.textContent.trim()
        }))
      );

      this.log(`📊 ${description}选项数量: ${options.length}`);

      // 查找匹配的选项
      const matchingOption = options.find(option =>
        option.text.toLowerCase() === stateName.toLowerCase() ||
        option.value.toLowerCase() === stateName.toLowerCase()
      );

      if (!matchingOption) {
        this.log(`⚠️  未找到匹配的${description}选项，使用第一个可用选项`);
        const availableOptions = options.filter(option => option.value && option.value.trim() !== '');
        if (availableOptions.length > 0) {
          await this.formFiller.currentPage.select(selector, availableOptions[0].value);
          this.log(`✅ 选择了备用${description}: ${availableOptions[0].text}`);
        } else {
          throw new Error(`没有可用的${description}选项`);
        }
      } else {
        await this.formFiller.currentPage.select(selector, matchingOption.value);
        this.log(`✅ ${description}选择成功: ${matchingOption.text}`);
      }

      // 等待选择生效
      await new Promise(resolve => setTimeout(resolve, 500));

    } catch (error) {
      this.log(`❌ 选择${description}失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 处理地址验证对话框
   */
  async handleAddressValidationDialog() {
    this.log('🔍 检查地址验证对话框...');

    try {
      const dialogSelector = 'body > div.dialog_host.ui-draggable > div';

      // 等待一下让对话框有时间出现
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 检查对话框是否出现
      const dialogExists = await this.formFiller.currentPage.$(dialogSelector);

      if (!dialogExists) {
        this.log('✅ 未检测到地址验证对话框，地址验证通过');
        return;
      }

      // 检查对话框是否可见
      const isVisible = await this.formFiller.currentPage.$eval(dialogSelector, el =>
        el.offsetParent !== null
      );

      if (!isVisible) {
        this.log('✅ 地址验证对话框存在但不可见，地址验证通过');
        return;
      }

      this.log('⚠️  检测到地址验证对话框，需要处理');

      // 截图：地址验证对话框
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('address-validation-dialog');
      }

      // 获取对话框内容
      const dialogContent = await this.formFiller.currentPage.$eval(dialogSelector, el =>
        el.textContent.trim()
      );

      this.log('📄 对话框内容预览:');
      this.log(`   ${dialogContent.substring(0, 200)}...`);

      // 点击第二个按钮（通常是"继续"或"确认"按钮）
      await this.clickAddressDialogButton();

      // 等待对话框消失
      await this.waitForAddressDialogDismissal();

      this.log('✅ 地址验证对话框已处理');

    } catch (error) {
      this.log(`⚠️  地址验证对话框处理失败: ${error.message}`);
      // 不抛出错误，继续处理
    }
  }

  /**
   * 点击地址对话框按钮
   */
  async clickAddressDialogButton() {
    this.log('🔘 点击地址对话框确认按钮...');

    try {
      const buttonSelector = 'body > div.dialog_host.ui-draggable > div > div.action > button:nth-child(2)';

      // 等待按钮出现
      await this.formFiller.currentPage.waitForSelector(buttonSelector, {
        timeout: this.options.dialogTimeout
      });

      const button = await this.formFiller.currentPage.$(buttonSelector);
      if (!button) {
        throw new Error('找不到地址对话框确认按钮');
      }

      // 获取按钮信息
      const buttonInfo = await this.formFiller.currentPage.$eval(buttonSelector, el => ({
        text: el.textContent.trim(),
        disabled: el.disabled,
        visible: el.offsetParent !== null,
        className: el.className
      }));

      this.log('📊 地址对话框按钮信息:');
      this.log(`   文本: "${buttonInfo.text}"`);
      this.log(`   禁用: ${buttonInfo.disabled}`);
      this.log(`   可见: ${buttonInfo.visible}`);
      this.log(`   类名: "${buttonInfo.className}"`);

      if (!buttonInfo.visible) {
        throw new Error('地址对话框按钮不可见');
      }

      if (buttonInfo.disabled) {
        this.log('⚠️  地址对话框按钮被禁用，等待启用...');

        // 等待按钮启用
        await this.formFiller.currentPage.waitForFunction(
          (selector) => {
            const button = document.querySelector(selector);
            return button && !button.disabled;
          },
          { timeout: this.options.dialogTimeout },
          buttonSelector
        );

        this.log('✅ 地址对话框按钮已启用');
      }

      // 截图：点击按钮前
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('before-address-dialog-click');
      }

      // 点击按钮
      await button.click();
      this.log('✅ 地址对话框确认按钮已点击');

      // 等待响应
      await new Promise(resolve => setTimeout(resolve, 1000));

    } catch (error) {
      this.log(`❌ 点击地址对话框按钮失败: ${error.message}`);

      // 尝试备用方法
      await this.tryAlternativeAddressDialogClick();
    }
  }

  /**
   * 尝试备用地址对话框点击方法
   */
  async tryAlternativeAddressDialogClick() {
    this.log('🔄 尝试备用地址对话框点击方法...');

    try {
      // 查找所有对话框中的按钮
      const allButtons = await this.formFiller.currentPage.$$('body > div.dialog_host.ui-draggable > div button');

      if (allButtons.length === 0) {
        throw new Error('对话框中没有找到任何按钮');
      }

      this.log(`📊 对话框中找到 ${allButtons.length} 个按钮`);

      // 获取所有按钮的信息
      const buttonInfos = [];
      for (let i = 0; i < allButtons.length; i++) {
        const info = await allButtons[i].evaluate(el => ({
          text: el.textContent.trim(),
          className: el.className,
          visible: el.offsetParent !== null,
          disabled: el.disabled
        }));
        buttonInfos.push({ index: i, ...info });
      }

      // 显示按钮信息
      this.log('📋 对话框按钮列表:');
      buttonInfos.forEach(info => {
        this.log(`   ${info.index + 1}. "${info.text}" ${info.disabled ? '[禁用]' : ''} ${info.visible ? '[可见]' : '[隐藏]'}`);
      });

      // 选择合适的按钮（优先选择第二个可见的按钮）
      let targetButton = null;
      const visibleButtons = buttonInfos.filter(info => info.visible && !info.disabled);

      if (visibleButtons.length >= 2) {
        targetButton = allButtons[visibleButtons[1].index]; // 第二个可见按钮
        this.log(`✅ 选择第二个可见按钮: "${visibleButtons[1].text}"`);
      } else if (visibleButtons.length >= 1) {
        targetButton = allButtons[visibleButtons[0].index]; // 第一个可见按钮
        this.log(`✅ 选择第一个可见按钮: "${visibleButtons[0].text}"`);
      } else {
        throw new Error('没有找到可用的按钮');
      }

      // 点击选中的按钮
      await targetButton.click();
      this.log('✅ 备用方法点击成功');

    } catch (error) {
      this.log(`❌ 备用地址对话框点击方法失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 等待地址对话框消失
   */
  async waitForAddressDialogDismissal() {
    this.log('⏳ 等待地址对话框消失...');

    try {
      const dialogSelector = 'body > div.dialog_host.ui-draggable > div';

      // 等待对话框消失或变为不可见
      await this.formFiller.currentPage.waitForFunction(
        (selector) => {
          const dialog = document.querySelector(selector);
          return !dialog || dialog.offsetParent === null;
        },
        { timeout: this.options.dialogTimeout },
        dialogSelector
      );

      this.log('✅ 地址对话框已消失');

      // 截图：对话框消失后
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('after-address-dialog-dismissed');
      }

    } catch (error) {
      this.log(`⚠️  等待地址对话框消失超时: ${error.message}`);
      // 不抛出错误，继续处理
    }
  }



  /**
   * 等待对话框消失
   */
  async waitForDialogDismissal(dialogSelector) {
    this.log('⏳ 等待提示框消失...');

    try {
      // 等待对话框消失
      await this.formFiller.currentPage.waitForFunction(
        (selector) => {
          const element = document.querySelector(selector);
          return !element || element.offsetParent === null;
        },
        { timeout: this.options.dialogTimeout },
        dialogSelector
      );

      this.log('✅ 提示框已消失');

    } catch (error) {
      this.log(`⚠️  等待提示框消失超时: ${error.message}`);
      // 不抛出错误，继续处理
    }
  }

  /**
   * 验证地址字段
   */
  async verifyAddressFields(expectedParts) {
    this.log('🔍 验证地址字段填写...');

    try {
      const fields = [
        { selector: '#address_1_street', expected: expectedParts.street, name: '街道' },
        { selector: '#address_1_city', expected: expectedParts.city, name: '城市' },
        { selector: '#address_1_postal', expected: expectedParts.postal, name: '邮编' }
      ];

      for (const field of fields) {
        const actualValue = await this.formFiller.currentPage.$eval(field.selector, el => el.value);
        const isMatch = actualValue === field.expected;
        this.log(`   ${field.name}: ${isMatch ? '✅' : '❌'} (期望: "${field.expected}", 实际: "${actualValue}")`);
      }

      // 验证州选择
      const selectedState = await this.formFiller.currentPage.$eval('#address_1_region', el => {
        const selectedOption = el.options[el.selectedIndex];
        return selectedOption ? selectedOption.textContent.trim() : '';
      });

      const stateMatch = selectedState.toLowerCase().includes(expectedParts.stateFull.toLowerCase());
      this.log(`   州: ${stateMatch ? '✅' : '❌'} (期望: "${expectedParts.stateFull}", 实际: "${selectedState}")`);

    } catch (error) {
      this.log(`⚠️  地址字段验证失败: ${error.message}`);
    }
  }

  /**
   * 点击邮寄地址链接
   */
  async clickMailingAddressLink() {
    this.log('🔘 检查邮寄地址链接...');

    try {
      const linkSelector = '#addresses > tbody:nth-child(5) > tr.address_mailing > td > a';

      // 检查邮寄地址链接是否存在（使用较短的超时时间）
      const linkExists = await this.formFiller.currentPage.waitForSelector(linkSelector, {
        timeout: 5000 // 5秒超时
      }).then(() => true).catch(() => false);

      if (!linkExists) {
        this.log('ℹ️  邮寄地址链接不存在，地址可能已经配置完成，跳过此步骤');
        return;
      }

      const link = await this.formFiller.currentPage.$(linkSelector);
      if (!link) {
        this.log('ℹ️  邮寄地址链接元素不可用，地址可能已经配置完成，跳过此步骤');
        return;
      }

      // 检查链接是否可见
      const isVisible = await link.evaluate(el => el.offsetParent !== null);
      if (!isVisible) {
        this.log('ℹ️  邮寄地址链接不可见，地址可能已经配置完成，跳过此步骤');
        return;
      }

      this.log('✅ 找到邮寄地址链接，准备点击');
      await link.click();
      this.log('✅ 邮寄地址链接已点击');

      // 等待操作生效
      await new Promise(resolve => setTimeout(resolve, 1000));

    } catch (error) {
      this.log(`⚠️  邮寄地址链接处理失败: ${error.message}`);
      this.log('ℹ️  这通常表示地址已经配置完成，继续处理后续步骤');
      // 不抛出错误，继续处理
    }
  }

  /**
   * 填写个人信息
   */
  async fillPersonalInformation() {
    this.log('📝 填写个人信息...');

    try {
      // 填写电话号码到多个字段
      const phone = this.studentData.phone || this.studentData['电话'];
      if (phone) {
        // 填写主要电话号码字段
        await this.fillFormField('#phone', phone, '电话号码');

        // 填写商务电话号码字段
        try {
          await this.fillFormField('#business', phone, '商务电话号码');
        } catch (error) {
          this.log(`⚠️  填写商务电话号码失败: ${error.message}`);
        }

        // 填写手机号码字段
        try {
          await this.fillFormField('#mobile', phone, '手机号码');
        } catch (error) {
          this.log(`⚠️  填写手机号码失败: ${error.message}`);
        }
      }

      // 选择性别
      const gender = this.studentData.gender || this.studentData['性别'];
      if (gender) {
        await this.selectGender(gender);
      }

      // 填写出生地（使用地址中的城市）
      const completeAddress = this.studentData.completeAddress || this.studentData['完整地址'];
      if (completeAddress) {
        const addressParts = this.parseCompleteAddress(completeAddress);
        await this.fillFormField('#birthplace', addressParts.city, '出生地');

        // 选择出生州
        await this.selectStateInDropdown('#birthregion', addressParts.stateFull, '出生州');
      }

      // 选择国籍
      await this.selectCitizenship();

      // 填写社会安全号
      const ssn = this.studentData.ssn || this.studentData['社会安全号'];
      if (ssn) {
        const cleanSSN = ssn.replace(/[-\s]/g, ''); // 移除连字符和空格
        await this.fillFormField('#ssn', cleanSSN, '社会安全号');
      }

      this.log('✅ 个人信息填写完成');

    } catch (error) {
      this.log(`❌ 填写个人信息失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 选择性别
   */
  async selectGender(gender) {
    this.log(`📋 选择性别: ${gender}`);

    try {
      // 性别映射
      const genderMapping = {
        '男': 'Male',
        '女': 'Female',
        'M': 'Male',
        'F': 'Female',
        'Male': 'Male',
        'Female': 'Female'
      };

      const mappedGender = genderMapping[gender] || gender;
      await this.selectInDropdown('#sex', mappedGender, '性别');

    } catch (error) {
      this.log(`❌ 选择性别失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 选择国籍
   */
  async selectCitizenship() {
    this.log('📋 选择国籍: United States');

    try {
      await this.selectInDropdown('#citizenship1', 'United States', '国籍');

    } catch (error) {
      this.log(`❌ 选择国籍失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 在下拉框中选择选项
   */
  async selectInDropdown(selector, targetValue, description) {
    this.log(`📋 在${description}下拉框中选择: ${targetValue}`);

    try {
      await this.formFiller.currentPage.waitForSelector(selector, {
        timeout: this.options.dialogTimeout
      });

      // 获取所有选项
      const options = await this.formFiller.currentPage.$$eval(`${selector} option`, options =>
        options.map(option => ({
          value: option.value,
          text: option.textContent.trim()
        }))
      );

      // 查找匹配的选项
      const matchingOption = options.find(option =>
        option.text.toLowerCase().includes(targetValue.toLowerCase()) ||
        option.value.toLowerCase().includes(targetValue.toLowerCase())
      );

      if (matchingOption) {
        await this.formFiller.currentPage.select(selector, matchingOption.value);
        this.log(`✅ ${description}选择成功: ${matchingOption.text}`);
      } else {
        this.log(`⚠️  未找到匹配的${description}选项: ${targetValue}`);
        throw new Error(`未找到匹配的${description}选项`);
      }

    } catch (error) {
      this.log(`❌ ${description}选择失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 选择种族和民族信息
   */
  async selectEthnicityAndRace() {
    this.log('📋 选择种族和民族信息...');

    try {
      // 点击民族选项
      await this.clickFormElement('#hispanic_0', '民族选项');

      // 点击种族选项
      await this.clickFormElement('#race_cd6b1bac-e441-4649-a7d3-a61ccf8c860c', '种族选项');

      this.log('✅ 种族和民族信息选择完成');

    } catch (error) {
      this.log(`❌ 选择种族和民族信息失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证完整工作流程
   */
  async verifyCompleteWorkflow() {
    this.log('🔍 验证完整工作流程...');

    try {
      const currentUrl = this.formFiller.currentPage.url();
      const pageTitle = await this.formFiller.currentPage.title();

      // 获取页面内容
      const pageText = await this.formFiller.currentPage.evaluate(() => {
        return document.body.innerText.substring(0, 1000);
      });

      const result = {
        url: currentUrl,
        title: pageTitle,
        textPreview: pageText.substring(0, 300),
        workflowComplete: true,
        timestamp: new Date().toISOString()
      };

      this.log('📊 完整工作流程结果:');
      this.log(`   URL: ${result.url}`);
      this.log(`   标题: ${result.title}`);
      this.log(`   工作流程完成: ${result.workflowComplete}`);

      return result;

    } catch (error) {
      this.log(`❌ 验证完整工作流程失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 检查申请启动是否成功
   */
  checkApplicationStartSuccess(url, title, pageText) {
    // 成功指标
    const successIndicators = [
      url.includes('/apply/') && !url.includes('/login'),
      url.includes('/application/'),
      title.toLowerCase().includes('application'),
      pageText.includes('Application'),
      pageText.includes('Personal Information'),
      pageText.includes('Continue'),
      pageText.includes('Save'),
      !pageText.includes('error'),
      !pageText.includes('Error')
    ];

    // 失败指标
    const failureIndicators = [
      pageText.toLowerCase().includes('error'),
      pageText.toLowerCase().includes('failed'),
      pageText.toLowerCase().includes('invalid'),
      title.toLowerCase().includes('error'),
      url.includes('/login') && pageText.includes('error')
    ];

    const successCount = successIndicators.filter(Boolean).length;
    const failureCount = failureIndicators.filter(Boolean).length;

    if (failureCount > 0) {
      return {
        success: false,
        message: '检测到错误信息'
      };
    }

    if (successCount >= 3) {
      return {
        success: true,
        message: `检测到${successCount}个成功指标`
      };
    }

    if (url.includes('/application/') || pageText.includes('Personal Information')) {
      return {
        success: true,
        message: '已进入申请表单页面'
      };
    }

    return {
      success: false,
      message: '无法确认申请启动状态'
    };
  }

  /**
   * 截图
   */
  async takeScreenshot(name) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `unr-start-app-${this.studentData.firstName}-${this.studentData.lastName}-${name}-${timestamp}`;
      
      await this.formFiller.currentPage.screenshot({
        path: `${this.options.screenshotPath}/${filename}.png`,
        fullPage: true
      });
      
      this.log(`📸 截图保存: ${filename}.png`);
      
    } catch (error) {
      this.log(`⚠️  截图失败: ${error.message}`);
    }
  }

  /**
   * 日志输出
   */
  log(message) {
    if (this.options.enableLogging) {
      console.log(message);
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    if (this.formFiller) {
      await this.formFiller.close();
    }
  }
}

/**
 * 从Excel处理UNR申请启动
 */
async function processUNRApplicationStartFromExcel(excelFilePath = 'test.xlsx') {
  console.log('🎓 UNR申请启动自动化 - 从Excel处理');
  console.log('==================================');

  try {
    // 读取学生数据
    const fullPath = path.join(process.cwd(), excelFilePath);
    console.log(`📁 读取文件: ${fullPath}`);
    
    const rawData = await ExcelReader.readExcelFile(fullPath);
    const studentsData = ExcelReader.convertToUNRFormat(rawData);

    if (studentsData.length === 0) {
      console.log('❌ 没有找到有效的学生数据');
      return;
    }

    console.log(`📝 准备处理 ${studentsData.length} 个学生的申请启动流程\n`);

    // 获取要处理的学生索引（从命令行参数或环境变量）
    const studentIndex = process.env.STUDENT_INDEX ? parseInt(process.env.STUDENT_INDEX) : 1; // 默认处理第二个学生（索引1）

    if (studentIndex >= studentsData.length) {
      console.log(`❌ 学生索引 ${studentIndex} 超出范围，总共只有 ${studentsData.length} 个学生`);
      return;
    }

    // 处理指定的学生
    const student = studentsData[studentIndex];
    console.log(`🎯 处理学生 #${studentIndex + 1}: ${student.firstName} ${student.lastName} (${student.email})`);

    const appStarter = new UNRApplicationStarter({
      enableLogging: true,
      enableScreenshots: true,
      waitAfterClick: 2000,
      dialogTimeout: 10000
    });

    try {
      await appStarter.initialize();
      const result = await appStarter.startApplication(student);
      
      if (result.success) {
        console.log('\n🎉 申请启动流程处理完成！');
        console.log('========================');
        console.log(`✅ 学生: ${result.studentData.firstName} ${result.studentData.lastName}`);
        console.log(`📧 邮箱: ${result.studentData.email}`);
        console.log(`📍 当前页面: ${result.result.url}`);
        console.log(`📄 页面标题: ${result.result.title}`);
      } else {
        console.log('\n❌ 申请启动流程处理失败');
        console.log(`错误: ${result.error}`);
      }

      return result;

    } finally {
      await appStarter.cleanup();
    }

  } catch (error) {
    console.error('❌ 处理失败:', error.message);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  processUNRApplicationStartFromExcel().catch(error => {
    console.error('❌ 程序执行失败:', error.message);
    process.exit(1);
  });
}

module.exports = {
  UNRApplicationStarter,
  processUNRApplicationStartFromExcel
};
