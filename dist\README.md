# UNR自动化工具 - Windows版

## 简介
UNR自动化流程管理工具的Windows可执行版本，无需安装Node.js即可运行。

## 系统要求
- Windows 10/11 (64位)
- 至少2GB可用内存
- 网络连接

## 安装和使用

### 快速启动
1. 双击 `UNR自动化工具.bat` 启动程序
2. 或者双击 `browser-automation.exe` 直接运行

### 首次使用
1. 启动程序后，选择"8. 📁 路径配置"
2. 配置Chrome浏览器路径和缓存目录
3. 配置Excel数据文件路径
4. 保存配置

### 目录结构
```
UNR自动化工具/
├── browser-automation.exe     # 主程序
├── UNR自动化工具.bat          # 启动脚本(推荐)
├── UNR自动化工具.ps1          # PowerShell启动脚本
├── assets/                    # 资源文件
│   ├── config/               # 配置文件
│   ├── docs/                 # 文档
│   ├── test.xlsx            # 示例数据文件
│   ├── screenshots/         # 截图目录
│   ├── logs/                # 日志目录
│   └── cache/               # 缓存目录
└── README.md                # 本文档
```

## 配置说明

### Chrome路径配置
程序会自动检测Chrome安装路径，如果检测失败，请手动配置：
- 标准路径: `C:\Program Files\Google\Chrome\Application\chrome.exe`
- 用户路径: `%USERPROFILE%\AppData\Local\Google\Chrome\Application\chrome.exe`

### 缓存目录配置
建议设置独立的缓存目录，避免与系统Chrome冲突：
- 推荐路径: `%USERPROFILE%\AppData\Local\UNR-Automation\Cache`

### 数据文件配置
- 支持Excel文件(.xlsx格式)
- 默认使用assets目录下的test.xlsx
- 可配置自定义路径

## 功能特性
- ✅ 交互式菜单界面
- ✅ 浏览器指纹随机化
- ✅ 自定义路径配置
- ✅ 批量处理支持
- ✅ 实时进度监控
- ✅ 详细日志记录

## 故障排除

### 常见问题
1. **程序无法启动**
   - 检查Windows版本是否支持
   - 以管理员身份运行
   - 检查防病毒软件是否阻止

2. **Chrome连接失败**
   - 确认Chrome路径配置正确
   - 检查端口9222是否被占用
   - 重启程序重试

3. **数据文件读取失败**
   - 检查Excel文件格式
   - 确认文件路径正确
   - 检查文件权限

### 日志文件
- 程序日志: `assets/logs/`
- 截图文件: `assets/screenshots/`
- 配置文件: `assets/config/`

## 技术支持
如遇问题，请检查日志文件或联系技术支持。

---
版本: 1.0.0
构建日期: 2025-07-17
