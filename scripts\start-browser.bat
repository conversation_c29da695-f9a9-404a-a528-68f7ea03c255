@echo off
REM 浏览器自动化启动脚本 (Windows批处理版本)
REM 基于用户原始命令，添加远程调试端口支持

echo 正在启动浏览器，支持Puppeteer连接...
echo.

REM 设置浏览器路径
set BROWSER_PATH=C:\Users\<USER>\AppData\Local\Chromium\Application
set BROWSER_EXE=chrome.exe

REM 检查浏览器是否存在
if not exist "%BROWSER_PATH%\%BROWSER_EXE%" (
    echo 错误: 找不到浏览器可执行文件
    echo 路径: %BROWSER_PATH%\%BROWSER_EXE%
    echo.
    echo 请检查浏览器安装路径是否正确
    pause
    exit /b 1
)

REM 切换到浏览器目录并启动
cd /d "%BROWSER_PATH%"

REM 启动浏览器（保持所有原始参数 + 调试端口）
"%BROWSER_EXE%" ^
--fingerprint=2024 ^
--fingerprint-platform=macos ^
--fingerprint-platform-version="15.2.0" ^
--fingerprint-brand="Edge" ^
--fingerprint-hardware-concurrency=4 ^
--timezone="America/Los_Angeles" ^
--lang="en-US" ^
--accept-lang="en-US,zh-CN" ^
--disable-non-proxied-udp ^
--proxy-server="socks5://127.0.0.1:7897" ^
--disable-webgl ^
--disable-webgl2 ^
--disable-accelerated-2d-canvas ^
--user-data-dir="D:\chrome\bycache" ^
--remote-debugging-port=9222 ^
--remote-allow-origins=*

if %ERRORLEVEL% neq 0 (
    echo.
    echo 浏览器启动失败，错误代码: %ERRORLEVEL%
    echo 请检查:
    echo 1. 浏览器路径是否正确
    echo 2. 端口9222是否被占用
    echo 3. 代理设置是否正确
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo 浏览器已启动，远程调试端口: 9222
echo Puppeteer连接地址: ws://localhost:9222
echo.
echo 按任意键关闭此窗口...
pause >nul
