/**
 * 浏览器自动化启动脚本 (Node.js跨平台版本)
 * 基于用户原始命令，添加远程调试端口支持
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const net = require('net');

// 导入浏览器配置
const browserConfig = require('../config/browser-config');

/**
 * 检查端口是否被占用
 */
function checkPort(port) {
  return new Promise((resolve) => {
    const server = net.createServer();
    server.listen(port, () => {
      server.once('close', () => resolve(false));
      server.close();
    });
    server.on('error', () => resolve(true));
  });
}

/**
 * 检查浏览器可执行文件是否存在
 */
function checkBrowserExists(browserPath) {
  const fullPath = path.join(browserPath, browserConfig.paths.executable);
  return fs.existsSync(fullPath);
}

/**
 * 启动浏览器
 */
async function startBrowser() {
  console.log('🚀 浏览器自动化启动器');
  console.log('=====================================');
  
  // 检查浏览器是否存在
  const browserPath = browserConfig.paths.chromium;
  if (!checkBrowserExists(browserPath)) {
    console.error('❌ 错误: 找不到浏览器可执行文件');
    console.error(`📁 路径: ${path.join(browserPath, browserConfig.paths.executable)}`);
    console.error('');
    console.error('💡 请检查浏览器安装路径是否正确');
    process.exit(1);
  }

  // 检查调试端口是否被占用
  const port = browserConfig.debugging.port;
  const isPortInUse = await checkPort(port);
  
  if (isPortInUse) {
    console.warn(`⚠️  警告: 端口${port}已被占用，可能有其他浏览器实例正在运行`);
    console.log('');
  }

  // 构建启动命令
  const executable = path.join(browserPath, browserConfig.paths.executable);
  const args = browserConfig.getAllArgs();

  console.log('🔧 启动配置:');
  console.log(`📂 浏览器路径: ${executable}`);
  console.log(`🔌 调试端口: ${port}`);
  console.log(`🌐 连接地址: ${browserConfig.debugging.wsEndpoint()}`);
  console.log('');
  
  console.log('⚙️  启动参数:');
  args.forEach(arg => console.log(`   ${arg}`));
  console.log('');

  try {
    console.log('🚀 正在启动浏览器...');
    
    // 启动浏览器进程
    const browserProcess = spawn(executable, args, {
      cwd: browserPath,
      detached: true,
      stdio: ['ignore', 'pipe', 'pipe']
    });

    // 处理启动成功
    browserProcess.on('spawn', () => {
      console.log('✅ 浏览器启动成功!');
      console.log(`🆔 进程ID: ${browserProcess.pid}`);
      console.log(`🔌 远程调试端口: ${port}`);
      console.log(`🌐 Puppeteer连接地址: ${browserConfig.debugging.wsEndpoint()}`);
      console.log('');
      console.log('🎯 浏览器已在后台运行，可以开始使用Puppeteer连接');
      console.log('');
      console.log('📝 使用示例:');
      console.log('   const puppeteer = require("puppeteer-core");');
      console.log(`   const browser = await puppeteer.connect({`);
      console.log(`     browserWSEndpoint: "${browserConfig.debugging.wsEndpoint()}"`);
      console.log('   });');
      
      // 分离进程，让浏览器独立运行
      browserProcess.unref();
    });

    // 处理启动错误
    browserProcess.on('error', (error) => {
      console.error('❌ 启动浏览器时发生错误:');
      console.error(error.message);
      console.error('');
      console.error('💡 请检查:');
      console.error('1. 浏览器路径是否正确');
      console.error('2. 端口9222是否被占用');
      console.error('3. 代理设置是否正确');
      process.exit(1);
    });

    // 处理进程退出
    browserProcess.on('exit', (code, signal) => {
      if (code !== 0) {
        console.error(`❌ 浏览器进程异常退出，代码: ${code}, 信号: ${signal}`);
        process.exit(code || 1);
      }
    });

    // 输出浏览器的错误信息
    browserProcess.stderr.on('data', (data) => {
      const message = data.toString().trim();
      if (message && !message.includes('DevTools')) {
        console.warn(`⚠️  浏览器警告: ${message}`);
      }
    });

  } catch (error) {
    console.error('❌ 启动失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  startBrowser().catch(error => {
    console.error('❌ 启动失败:', error.message);
    process.exit(1);
  });
}

module.exports = { startBrowser, checkPort, checkBrowserExists };
