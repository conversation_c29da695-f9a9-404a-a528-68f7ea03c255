# 🚀 快速开始指南

本指南将帮助您在5分钟内开始使用Browser Automation表单填写引擎。

## 📋 前置要求

- Node.js 14+ 
- Chrome/Chromium 浏览器
- Windows/macOS/Linux 操作系统

## 🛠️ 安装步骤

### 1. 克隆项目

```bash
git clone <repository-url>
cd browser-automation
```

### 2. 安装依赖

```bash
npm install
```

### 3. 启动浏览器

选择适合您系统的命令：

```bash
# Windows (推荐)
npm run browser:ps1

# 或使用批处理文件
npm run browser:bat

# 跨平台Node.js脚本
npm run browser
```

浏览器启动后，您应该看到类似以下的输出：
```
DevTools listening on ws://127.0.0.1:9222/devtools/browser/...
```

## 🎯 第一个示例

### 创建简单的表单填写脚本

创建文件 `my-first-form.js`：

```javascript
const { FormFiller } = require('./src/form-filler');

async function fillMyForm() {
  // 1. 创建表单填写引擎
  const formFiller = new FormFiller({
    enableLogging: true  // 启用日志查看执行过程
  });

  try {
    // 2. 初始化引擎
    await formFiller.initialize();
    console.log('✅ 引擎初始化成功');

    // 3. 配置要填写的表单
    const config = {
      url: 'https://example.com/contact',  // 表单页面URL
      fields: {
        '#name': '张三',                    // 姓名字段
        '#email': '<EMAIL>',   // 邮箱字段
        '#message': '这是一条测试消息',       // 消息字段
        '#agree': true                      // 同意条款复选框
      },
      submit: {
        selector: '#submit-btn'             // 提交按钮
      }
    };

    // 4. 执行表单填写
    const result = await formFiller.fillForm(config);
    
    // 5. 查看结果
    console.log('填写结果:', result.getSummary());

  } catch (error) {
    console.error('填写失败:', error.message);
  } finally {
    // 6. 清理资源
    await formFiller.close();
  }
}

// 运行脚本
fillMyForm();
```

### 运行脚本

```bash
node my-first-form.js
```

## 📖 基础配置说明

### 字段配置格式

```javascript
fields: {
  // 简单值配置
  '#username': 'testuser',
  
  // 复杂配置
  '#email': {
    value: '<EMAIL>',
    locatorType: 'css'  // 定位方式
  },
  
  // 下拉框选择
  '#country': {
    value: '中国',
    selectBy: 'text'    // 按文本选择
  },
  
  // 复选框
  '#newsletter': true,  // 勾选
  '#spam': false,       // 不勾选
  
  // 单选框
  '#gender': 'male'
}
```

### 支持的元素类型

| 元素 | 配置示例 | 说明 |
|------|----------|------|
| 文本框 | `'#name': 'John'` | 自动填写文本 |
| 邮箱框 | `'#email': '<EMAIL>'` | 邮箱格式验证 |
| 密码框 | `'#password': 'secret123'` | 安全输入 |
| 数字框 | `'#age': '25'` | 数字输入 |
| 下拉框 | `'#country': 'China'` | 选择选项 |
| 复选框 | `'#agree': true` | 勾选/取消 |
| 单选框 | `'#gender': 'male'` | 选择选项 |
| 文本域 | `'#bio': 'Long text...'` | 多行文本 |

## 🧪 运行内置示例

### 基础示例

```bash
# 运行基础表单示例
npm run example:basic

# 查看详细的表单处理器示例
npm run example:forms

# 查看核心引擎高级功能示例
npm run example:engine
```

### 测试功能

```bash
# 测试浏览器连接
npm run test:connector

# 测试表单处理器
npm run test:forms

# 测试核心引擎
npm run test:engine

# 运行所有测试
npm test
```

## 🔧 常见配置

### 等待表单加载

```javascript
const config = {
  url: 'https://example.com/form',
  waitForForm: '#myForm',  // 等待表单出现
  fields: {
    // ... 字段配置
  }
};
```

### 条件填写

```javascript
const config = {
  fields: {
    '#email': {
      value: '<EMAIL>',
      condition: {
        selector: '#premium-section',
        visible: true  // 只有当高级区域可见时才填写
      }
    }
  }
};
```

### 错误处理

```javascript
const formFiller = new FormFiller({
  stopOnError: false,      // 遇到错误继续执行
  enableLogging: true,     // 启用详细日志
  enableScreenshots: true  // 启用截图
});
```

## 🎯 实际应用场景

### 1. 用户注册表单

```javascript
const registrationConfig = {
  url: 'https://site.com/register',
  fields: {
    '#username': 'newuser2024',
    '#email': '<EMAIL>',
    '#password': 'SecurePass123!',
    '#confirmPassword': 'SecurePass123!',
    '#terms': true
  },
  submit: { selector: '#register-btn' }
};
```

### 2. 联系表单

```javascript
const contactConfig = {
  url: 'https://site.com/contact',
  fields: {
    '#name': '张三',
    '#email': '<EMAIL>',
    '#subject': 'technical-support',
    '#message': '我需要技术支持...'
  },
  submit: { selector: '#send-btn' }
};
```

### 3. 搜索表单

```javascript
const searchConfig = {
  url: 'https://site.com/search',
  fields: {
    '#keyword': 'JavaScript教程',
    '#category': 'programming',
    '#sort': 'relevance'
  },
  submit: { selector: '#search-btn' }
};
```

## 🛠️ 故障排除

### 常见问题

1. **浏览器连接失败**
   ```bash
   # 检查浏览器是否启动
   npm run browser
   
   # 检查端口占用
   netstat -an | findstr 9222
   ```

2. **元素找不到**
   - 检查CSS选择器是否正确
   - 使用浏览器开发者工具验证选择器
   - 添加等待时间：`waitForForm: '#form'`

3. **填写失败**
   - 启用详细日志：`enableLogging: true`
   - 检查元素是否可见和可用
   - 尝试不同的定位方式

### 调试技巧

```javascript
// 启用详细日志和截图
const formFiller = new FormFiller({
  enableLogging: true,
  enableScreenshots: true,
  screenshotPath: './debug-screenshots'
});

// 查看详细结果
const result = await formFiller.fillForm(config);
console.log('详细结果:', result.fieldResults);
```

## 📚 下一步

- 📖 阅读完整的 [API文档](API.md)
- 🔍 查看更多 [使用示例](../src/examples/)
- 🧪 运行 [测试套件](../src/test-*.js)
- 🎯 了解 [高级功能](../README.md#高级功能)

## 💡 提示

- 始终在 `try-catch` 块中使用FormFiller
- 记得调用 `formFiller.close()` 释放资源
- 使用 `enableLogging: true` 查看详细执行过程
- 复杂表单建议分步骤填写

---

🎉 **恭喜！您已经掌握了基础用法，现在可以开始自动化您的表单填写任务了！**
