/**
 * 浏览器连接工具类
 * 提供稳定可靠的Puppeteer浏览器连接服务
 */

const puppeteer = require('puppeteer-core');
const http = require('http');
const EventEmitter = require('events');
const browserConfig = require('../../config/browser-config');

/**
 * 浏览器连接状态枚举
 */
const ConnectionState = {
  DISCONNECTED: 'disconnected',
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  RECONNECTING: 'reconnecting',
  ERROR: 'error'
};

/**
 * 浏览器连接管理器
 */
class BrowserConnector extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      port: options.port || browserConfig.debugging.port,
      host: options.host || browserConfig.debugging.host,
      maxRetries: options.maxRetries || 3,
      retryDelay: options.retryDelay || 2000,
      connectionTimeout: options.connectionTimeout || 10000,
      healthCheckInterval: options.healthCheckInterval || 30000,
      autoReconnect: options.autoReconnect !== false
    };
    
    this.browser = null;
    this.state = ConnectionState.DISCONNECTED;
    this.retryCount = 0;
    this.healthCheckTimer = null;
    this.wsEndpoint = null;
  }

  /**
   * 获取当前连接状态
   */
  getState() {
    return this.state;
  }

  /**
   * 检查浏览器是否已连接
   */
  isConnected() {
    return this.state === ConnectionState.CONNECTED && this.browser && this.browser.isConnected();
  }

  /**
   * 获取浏览器WebSocket端点
   */
  async getBrowserEndpoint() {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('获取浏览器端点超时'));
      }, this.options.connectionTimeout);

      // 强制使用IPv4地址
      const host = this.options.host === 'localhost' ? '127.0.0.1' : this.options.host;
      const url = `http://${host}:${this.options.port}/json/version`;

      console.log(`🔍 尝试连接调试端口: ${url}`);

      const req = http.get(url, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          clearTimeout(timeout);
          try {
            const versionInfo = JSON.parse(data);
            console.log(`✅ 获取到WebSocket端点: ${versionInfo.webSocketDebuggerUrl}`);
            resolve(versionInfo.webSocketDebuggerUrl);
          } catch (e) {
            reject(new Error(`解析浏览器端点失败: ${e.message}`));
          }
        });
      });

      req.on('error', (error) => {
        clearTimeout(timeout);
        console.log(`❌ 连接调试端口失败: ${error.code} - ${error.message}`);
        reject(new Error(`连接浏览器调试端口失败: ${error.code || error.message || '未知错误'}`));
      });
    });
  }

  /**
   * 连接到浏览器
   */
  async connect() {
    if (this.isConnected()) {
      return this.browser;
    }

    this.setState(ConnectionState.CONNECTING);
    this.emit('connecting');

    try {
      // 尝试获取WebSocket端点，如果失败则启动浏览器
      try {
        this.wsEndpoint = await this.getBrowserEndpoint();
        console.log(`🔗 连接到现有浏览器: ${this.wsEndpoint}`);
      } catch (error) {
        console.log('🚀 未检测到运行中的浏览器，启动新浏览器...');
        await this.launchBrowser();

        // 等待浏览器完全启动
        console.log('⏳ 等待浏览器启动...');
        await new Promise(resolve => setTimeout(resolve, 3000));

        // 重试获取端点，最多尝试5次
        let retries = 5;
        while (retries > 0) {
          try {
            this.wsEndpoint = await this.getBrowserEndpoint();
            console.log(`🔗 连接到新启动的浏览器: ${this.wsEndpoint}`);
            break;
          } catch (retryError) {
            retries--;
            if (retries > 0) {
              console.log(`⏳ 浏览器尚未就绪，等待2秒后重试... (剩余${retries}次)`);
              await new Promise(resolve => setTimeout(resolve, 2000));
            } else {
              throw retryError;
            }
          }
        }
      }

      // 连接到浏览器
      this.browser = await puppeteer.connect({
        browserWSEndpoint: this.wsEndpoint,
        defaultViewport: null
      });

      // 设置事件监听
      this.setupBrowserEvents();

      this.setState(ConnectionState.CONNECTED);
      this.retryCount = 0;
      this.emit('connected', this.browser);

      // 启动健康检查
      this.startHealthCheck();

      console.log('✅ 浏览器连接成功');
      return this.browser;

    } catch (error) {
      this.setState(ConnectionState.ERROR);
      this.emit('error', error);

      if (this.options.autoReconnect && this.retryCount < this.options.maxRetries) {
        console.warn(`⚠️  连接失败，${this.options.retryDelay}ms后重试 (${this.retryCount + 1}/${this.options.maxRetries})`);
        await this.scheduleReconnect();
      } else {
        console.error('❌ 浏览器连接失败:', error.message);
        throw error;
      }
    }
  }

  /**
   * 启动浏览器
   */
  async launchBrowser() {
    const { spawn } = require('child_process');
    const fs = require('fs');
    const browserConfig = require('../../config/browser-config');

    console.log('🚀 启动Chrome浏览器...');

    // 获取正确的Chrome路径
    const chromePath = browserConfig.paths.chromePath;

    // 验证Chrome路径
    if (!chromePath || !fs.existsSync(chromePath)) {
      const error = new Error(`找不到浏览器可执行文件\n📁 路径: ${chromePath}\n\n💡 请检查浏览器安装路径是否正确`);
      console.error('❌ 错误:', error.message);
      throw error;
    }

    console.log(`✅ Chrome路径: ${chromePath}`);

    const args = browserConfig.getAllArgs();

    // 使用配置的用户数据目录
    const userDataDir = browserConfig.paths.userDataDir;

    // 确保用户数据目录存在
    if (!fs.existsSync(userDataDir)) {
      fs.mkdirSync(userDataDir, { recursive: true });
    }

    // 替换用户数据目录参数，确保使用正确的路径
    const updatedArgs = args.map(arg => {
      if (arg.startsWith('--user-data-dir=')) {
        return `--user-data-dir=${userDataDir}`;
      }
      return arg;
    });

    // 如果没有用户数据目录参数，添加一个
    if (!updatedArgs.some(arg => arg.startsWith('--user-data-dir='))) {
      updatedArgs.push(`--user-data-dir=${userDataDir}`);
    }

    console.log(`📁 使用用户数据目录: ${userDataDir}`);
    console.log(`🔧 启动参数: ${updatedArgs.slice(0, 3).join(' ')}...`);

    const browserProcess = spawn(chromePath, updatedArgs, {
      detached: true,
      stdio: 'ignore'
    });

    browserProcess.unref();

    // 等待浏览器启动
    console.log('⏳ 等待浏览器启动...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('✅ 浏览器启动完成');
  }

  /**
   * 设置浏览器事件监听
   */
  setupBrowserEvents() {
    if (!this.browser) return;

    this.browser.on('disconnected', () => {
      console.warn('⚠️  浏览器连接断开');
      this.setState(ConnectionState.DISCONNECTED);
      this.stopHealthCheck();
      this.emit('disconnected');

      if (this.options.autoReconnect) {
        this.scheduleReconnect();
      }
    });

    this.browser.on('targetcreated', (target) => {
      this.emit('targetcreated', target);
    });

    this.browser.on('targetdestroyed', (target) => {
      this.emit('targetdestroyed', target);
    });
  }

  /**
   * 计划重连
   */
  async scheduleReconnect() {
    if (this.retryCount >= this.options.maxRetries) {
      console.error('❌ 达到最大重试次数，停止重连');
      this.setState(ConnectionState.ERROR);
      return;
    }

    this.setState(ConnectionState.RECONNECTING);
    this.retryCount++;
    this.emit('reconnecting', this.retryCount);

    await new Promise(resolve => setTimeout(resolve, this.options.retryDelay));
    
    try {
      await this.connect();
    } catch (error) {
      console.error(`❌ 重连失败 (${this.retryCount}/${this.options.maxRetries}):`, error.message);
    }
  }

  /**
   * 启动健康检查
   */
  startHealthCheck() {
    this.stopHealthCheck();
    
    this.healthCheckTimer = setInterval(async () => {
      try {
        if (this.browser && this.browser.isConnected()) {
          // 简单的健康检查：获取浏览器版本
          await this.browser.version();
        } else {
          throw new Error('浏览器连接已断开');
        }
      } catch (error) {
        console.warn('⚠️  健康检查失败:', error.message);
        if (this.options.autoReconnect) {
          this.scheduleReconnect();
        }
      }
    }, this.options.healthCheckInterval);
  }

  /**
   * 停止健康检查
   */
  stopHealthCheck() {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }
  }

  /**
   * 设置连接状态
   */
  setState(newState) {
    const oldState = this.state;
    this.state = newState;
    this.emit('stateChanged', newState, oldState);
  }

  /**
   * 断开连接
   */
  async disconnect() {
    this.stopHealthCheck();
    
    if (this.browser) {
      try {
        await this.browser.disconnect();
        console.log('🔌 浏览器连接已断开');
      } catch (error) {
        console.warn('⚠️  断开连接时出错:', error.message);
      }
      this.browser = null;
    }
    
    this.setState(ConnectionState.DISCONNECTED);
    this.emit('disconnected');
  }

  /**
   * 获取浏览器实例
   */
  getBrowser() {
    return this.browser;
  }

  /**
   * 创建新页面
   */
  async newPage() {
    if (!this.isConnected()) {
      await this.connect();
    }
    return await this.browser.newPage();
  }

  /**
   * 获取所有页面
   */
  async pages() {
    if (!this.isConnected()) {
      await this.connect();
    }
    return await this.browser.pages();
  }

  /**
   * 获取浏览器信息
   */
  async getBrowserInfo() {
    if (!this.isConnected()) {
      await this.connect();
    }
    
    const version = await this.browser.version();
    const userAgent = await this.browser.userAgent();
    const pages = await this.browser.pages();
    
    return {
      version,
      userAgent,
      pageCount: pages.length,
      wsEndpoint: this.wsEndpoint,
      state: this.state,
      isConnected: this.isConnected()
    };
  }
}

module.exports = { BrowserConnector, ConnectionState };
