/**
 * 核心表单填写引擎
 * 整合所有工具模块，提供高级API和完整的表单自动化解决方案
 */

const { BrowserConnector } = require('./utils/browser-connector');
const { FormHandlerManager, LocatorType, ElementType } = require('./utils/form-handlers');
const { retry, ErrorHandler } = require('./utils/retry-helper');

/**
 * 表单填写配置验证器
 */
class FormConfigValidator {
  /**
   * 验证表单配置
   */
  static validate(config) {
    const errors = [];

    if (!config || typeof config !== 'object') {
      errors.push('配置必须是一个对象');
      return errors;
    }

    // 验证必需字段
    if (!config.url && !config.skipNavigation) {
      errors.push('必须提供url或设置skipNavigation为true');
    }

    if (!config.fields || typeof config.fields !== 'object') {
      errors.push('必须提供fields配置');
    }

    // 验证字段配置
    if (config.fields) {
      for (const [selector, fieldConfig] of Object.entries(config.fields)) {
        if (!selector) {
          errors.push('字段选择器不能为空');
          continue;
        }

        if (typeof fieldConfig === 'object' && fieldConfig !== null) {
          if (fieldConfig.value === undefined && !fieldConfig.skip) {
            errors.push(`字段 ${selector} 必须提供value或设置skip为true`);
          }
        }
      }
    }

    return errors;
  }
}

/**
 * 表单填写结果类
 */
class FormFillResult {
  constructor() {
    this.success = false;
    this.totalFields = 0;
    this.successFields = 0;
    this.failedFields = 0;
    this.skippedFields = 0;
    this.fieldResults = [];
    this.errors = [];
    this.startTime = Date.now();
    this.endTime = null;
    this.duration = 0;
  }

  /**
   * 添加字段结果
   */
  addFieldResult(selector, success, value = null, error = null, skipped = false) {
    const result = {
      selector,
      success,
      value,
      error: error ? error.message : null,
      skipped,
      timestamp: new Date().toISOString()
    };

    this.fieldResults.push(result);
    this.totalFields++;

    if (skipped) {
      this.skippedFields++;
    } else if (success) {
      this.successFields++;
    } else {
      this.failedFields++;
      if (error) {
        this.errors.push(`${selector}: ${error.message}`);
      }
    }
  }

  /**
   * 完成填写
   */
  finish() {
    this.endTime = Date.now();
    this.duration = this.endTime - this.startTime;
    this.success = this.failedFields === 0 && this.successFields > 0;
  }

  /**
   * 获取摘要
   */
  getSummary() {
    return {
      success: this.success,
      totalFields: this.totalFields,
      successFields: this.successFields,
      failedFields: this.failedFields,
      skippedFields: this.skippedFields,
      duration: this.duration,
      successRate: this.totalFields > 0 ? (this.successFields / this.totalFields * 100).toFixed(2) + '%' : '0%'
    };
  }
}

/**
 * 核心表单填写引擎
 */
class FormFiller {
  constructor(options = {}) {
    this.options = {
      // 浏览器连接选项
      browserOptions: options.browserOptions || {},
      
      // 表单处理选项
      formOptions: options.formOptions || {
        timeout: 30000,
        waitForVisible: true,
        waitForEnabled: true,
        retryOptions: {
          maxRetries: 3,
          baseDelay: 500
        }
      },

      // 全局选项
      stopOnError: options.stopOnError || false,
      enableLogging: options.enableLogging !== false,
      enableScreenshots: options.enableScreenshots || false,
      screenshotPath: options.screenshotPath || './screenshots'
    };

    this.connector = null;
    this.formManager = null;
    this.currentPage = null;
  }

  /**
   * 初始化引擎
   */
  async initialize() {
    try {
      this.log('🚀 初始化表单填写引擎...');

      // 初始化浏览器连接
      this.connector = new BrowserConnector(this.options.browserOptions);
      await this.connector.connect();

      // 创建默认页面
      this.currentPage = await this.connector.newPage();

      this.log('✅ 浏览器连接成功');
      return true;

    } catch (error) {
      this.log('❌ 初始化失败:', error.message);
      throw error;
    }
  }

  /**
   * 填写表单
   */
  async fillForm(config) {
    const result = new FormFillResult();
    
    try {
      this.log('📋 开始表单填写任务...');
      
      // 验证配置
      const validationErrors = FormConfigValidator.validate(config);
      if (validationErrors.length > 0) {
        throw new Error(`配置验证失败: ${validationErrors.join(', ')}`);
      }

      // 创建或获取页面
      await this.preparePage(config);
      
      // 初始化表单管理器
      this.formManager = new FormHandlerManager(this.currentPage, this.options.formOptions);

      // 等待表单加载
      if (config.waitForForm) {
        await this.waitForFormReady(config.waitForForm);
      }

      // 截图（如果启用）
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('before-fill');
      }

      // 填写表单字段
      await this.fillFormFields(config.fields, result);

      // 处理表单提交
      if (config.submit) {
        await this.handleFormSubmit(config.submit, result);
      }

      // 处理表单验证
      if (config.validation) {
        await this.handleFormValidation(config.validation, result);
      }

      // 截图（如果启用）
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('after-fill');
      }

      result.finish();
      this.log('✅ 表单填写完成:', result.getSummary());
      
      return result;

    } catch (error) {
      result.errors.push(error.message);
      result.finish();
      this.log('❌ 表单填写失败:', error.message);
      throw error;
    }
  }

  /**
   * 准备页面
   */
  async preparePage(config) {
    if (config.skipNavigation) {
      // 使用现有页面或创建新页面
      if (!this.currentPage) {
        this.currentPage = await this.connector.newPage();
      }
    } else {
      // 导航到指定URL
      this.currentPage = await this.connector.newPage();
      
      this.log(`🌐 导航到: ${config.url}`);
      await retry(async () => {
        await this.currentPage.goto(config.url, {
          waitUntil: config.waitUntil || 'domcontentloaded',
          timeout: config.navigationTimeout || 30000
        });
      }, this.options.formOptions.retryOptions);
    }
  }

  /**
   * 等待表单准备就绪
   */
  async waitForFormReady(waitConfig) {
    this.log('⏳ 等待表单加载...');
    
    if (typeof waitConfig === 'string') {
      // 简单选择器等待
      await this.currentPage.waitForSelector(waitConfig);
    } else if (typeof waitConfig === 'object') {
      // 复杂等待配置
      if (waitConfig.selector) {
        await this.currentPage.waitForSelector(waitConfig.selector, {
          timeout: waitConfig.timeout || 30000
        });
      }
      
      if (waitConfig.requiredElements) {
        for (const selector of waitConfig.requiredElements) {
          await this.currentPage.waitForSelector(selector);
        }
      }
      
      if (waitConfig.delay) {
        await new Promise(resolve => setTimeout(resolve, waitConfig.delay));
      }
    }
    
    this.log('✅ 表单加载完成');
  }

  /**
   * 填写表单字段
   */
  async fillFormFields(fields, result) {
    this.log('📝 开始填写表单字段...');
    
    for (const [selector, fieldConfig] of Object.entries(fields)) {
      try {
        // 解析字段配置
        let value, options;
        if (typeof fieldConfig === 'object' && fieldConfig !== null) {
          if (fieldConfig.skip) {
            result.addFieldResult(selector, true, null, null, true);
            this.log(`⏭️  跳过字段: ${selector}`);
            continue;
          }
          
          value = fieldConfig.value;
          options = { ...fieldConfig };
          delete options.value;
        } else {
          value = fieldConfig;
          options = {};
        }

        // 条件填写
        if (options.condition) {
          const shouldFill = await this.evaluateCondition(options.condition);
          if (!shouldFill) {
            result.addFieldResult(selector, true, null, null, true);
            this.log(`🔀 条件不满足，跳过字段: ${selector}`);
            continue;
          }
        }

        // 填写字段
        this.log(`📝 填写字段: ${selector} = ${value}`);
        await this.formManager.handleElement(selector, value, options);
        
        result.addFieldResult(selector, true, value);
        this.log(`✅ 字段填写成功: ${selector}`);

      } catch (error) {
        result.addFieldResult(selector, false, null, error);
        this.log(`❌ 字段填写失败: ${selector} - ${error.message}`);
        
        if (this.options.stopOnError) {
          throw error;
        }
      }
    }
  }

  /**
   * 处理表单提交
   */
  async handleFormSubmit(submitConfig, result) {
    try {
      this.log('📤 处理表单提交...');
      
      if (typeof submitConfig === 'string') {
        // 简单提交按钮选择器
        await this.formManager.button.click(submitConfig);
      } else if (typeof submitConfig === 'object') {
        // 复杂提交配置
        const selector = submitConfig.selector || 'button[type="submit"]';
        const options = { ...submitConfig };
        delete options.selector;
        
        await this.formManager.button.click(selector, options);
        
        // 等待提交结果
        if (submitConfig.waitForResponse) {
          await this.currentPage.waitForResponse(response => 
            response.status() === 200, { timeout: 30000 }
          );
        }
        
        if (submitConfig.waitForNavigation) {
          await this.currentPage.waitForNavigation({ timeout: 30000 });
        }
      }
      
      this.log('✅ 表单提交成功');
      
    } catch (error) {
      this.log('❌ 表单提交失败:', error.message);
      result.errors.push(`表单提交失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 处理表单验证
   */
  async handleFormValidation(validationConfig, result) {
    try {
      this.log('🔍 处理表单验证...');
      
      for (const [name, validation] of Object.entries(validationConfig)) {
        if (validation.selector) {
          const element = await this.currentPage.$(validation.selector);
          if (element) {
            const isVisible = await element.evaluate(el => {
              const style = window.getComputedStyle(el);
              return style.display !== 'none' && style.visibility !== 'hidden';
            });
            
            if (isVisible) {
              const text = await element.evaluate(el => el.textContent);
              this.log(`⚠️  验证错误: ${name} - ${text}`);
              result.errors.push(`验证失败: ${name} - ${text}`);
            }
          }
        }
      }
      
    } catch (error) {
      this.log('❌ 表单验证处理失败:', error.message);
    }
  }

  /**
   * 评估条件
   */
  async evaluateCondition(condition) {
    try {
      if (typeof condition === 'function') {
        return await condition(this.currentPage);
      } else if (typeof condition === 'object') {
        if (condition.selector) {
          const element = await this.currentPage.$(condition.selector);
          if (condition.exists !== undefined) {
            return !!element === condition.exists;
          }
          if (condition.visible !== undefined && element) {
            const isVisible = await element.evaluate(el => {
              const style = window.getComputedStyle(el);
              return style.display !== 'none' && style.visibility !== 'hidden';
            });
            return isVisible === condition.visible;
          }
        }
      }
      return true;
    } catch (error) {
      this.log('⚠️  条件评估失败:', error.message);
      return false;
    }
  }

  /**
   * 截图
   */
  async takeScreenshot(name) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${name}-${timestamp}.png`;
      const path = `${this.options.screenshotPath}/${filename}`;
      
      await this.currentPage.screenshot({ path, fullPage: true });
      this.log(`📸 截图保存: ${path}`);
      
    } catch (error) {
      this.log('⚠️  截图失败:', error.message);
    }
  }

  /**
   * 日志输出
   */
  log(...args) {
    if (this.options.enableLogging) {
      console.log(...args);
    }
  }

  /**
   * 关闭连接
   */
  async close() {
    if (this.currentPage) {
      await this.currentPage.close();
      this.currentPage = null;
    }
    
    if (this.connector) {
      await this.connector.disconnect();
      this.connector = null;
    }
  }
}

module.exports = {
  FormFiller,
  FormFillResult,
  FormConfigValidator
};
