/**
 * 重试机制和错误处理工具
 * 提供通用的重试装饰器、错误分类和处理策略
 */

/**
 * 错误类型枚举
 */
const ErrorType = {
  NETWORK_ERROR: 'network_error',
  TIMEOUT_ERROR: 'timeout_error',
  ELEMENT_NOT_FOUND: 'element_not_found',
  ELEMENT_NOT_VISIBLE: 'element_not_visible',
  ELEMENT_NOT_INTERACTABLE: 'element_not_interactable',
  NAVIGATION_ERROR: 'navigation_error',
  SCRIPT_ERROR: 'script_error',
  UNKNOWN_ERROR: 'unknown_error'
};

/**
 * 重试策略枚举
 */
const RetryStrategy = {
  FIXED_DELAY: 'fixed_delay',
  EXPONENTIAL_BACKOFF: 'exponential_backoff',
  LINEAR_BACKOFF: 'linear_backoff'
};

/**
 * 错误分类器
 */
class ErrorClassifier {
  /**
   * 根据错误信息分类错误类型
   */
  static classify(error) {
    const message = error.message || error.toString();
    const lowerMessage = message.toLowerCase();

    // 网络相关错误
    if (lowerMessage.includes('net::') || 
        lowerMessage.includes('network') ||
        lowerMessage.includes('connection') ||
        lowerMessage.includes('econnrefused') ||
        lowerMessage.includes('enotfound')) {
      return ErrorType.NETWORK_ERROR;
    }

    // 元素未找到（需要在超时错误之前检查，因为selector timeout也包含timeout关键字）
    if (lowerMessage.includes('no node found') ||
        lowerMessage.includes('element not found') ||
        lowerMessage.includes('waiting for selector') ||
        lowerMessage.includes('failed to find element')) {
      return ErrorType.ELEMENT_NOT_FOUND;
    }

    // 超时错误
    if (lowerMessage.includes('timeout') ||
        lowerMessage.includes('timed out') ||
        error.name === 'TimeoutError') {
      return ErrorType.TIMEOUT_ERROR;
    }

    // 元素不可见
    if (lowerMessage.includes('not visible') ||
        lowerMessage.includes('element is not visible') ||
        lowerMessage.includes('node is not visible')) {
      return ErrorType.ELEMENT_NOT_VISIBLE;
    }

    // 元素不可交互
    if (lowerMessage.includes('not interactable') ||
        lowerMessage.includes('element not interactable') ||
        lowerMessage.includes('element is not clickable') ||
        lowerMessage.includes('not clickable') ||
        lowerMessage.includes('node is either not clickable')) {
      return ErrorType.ELEMENT_NOT_INTERACTABLE;
    }

    // 导航错误
    if (lowerMessage.includes('navigation') ||
        lowerMessage.includes('page.goto') ||
        lowerMessage.includes('net::err_')) {
      return ErrorType.NAVIGATION_ERROR;
    }

    // 脚本执行错误
    if (lowerMessage.includes('evaluation failed') ||
        lowerMessage.includes('script error') ||
        error.name === 'EvaluationError') {
      return ErrorType.SCRIPT_ERROR;
    }

    return ErrorType.UNKNOWN_ERROR;
  }

  /**
   * 判断错误是否可重试
   */
  static isRetryable(errorType) {
    const retryableErrors = [
      ErrorType.NETWORK_ERROR,
      ErrorType.TIMEOUT_ERROR,
      ErrorType.ELEMENT_NOT_FOUND,
      ErrorType.ELEMENT_NOT_VISIBLE,
      ErrorType.ELEMENT_NOT_INTERACTABLE
    ];
    return retryableErrors.includes(errorType);
  }
}

/**
 * 重试配置类
 */
class RetryConfig {
  constructor(options = {}) {
    this.maxRetries = options.maxRetries || 3;
    this.baseDelay = options.baseDelay || 1000;
    this.maxDelay = options.maxDelay || 30000;
    this.strategy = options.strategy || RetryStrategy.EXPONENTIAL_BACKOFF;
    this.retryableErrors = options.retryableErrors || null; // null表示使用默认分类
    this.onRetry = options.onRetry || null; // 重试回调函数
    this.timeout = options.timeout || 60000; // 总超时时间
    this.jitter = options.jitter !== false; // 是否添加随机抖动
  }

  /**
   * 计算延迟时间
   */
  calculateDelay(attempt) {
    let delay;

    switch (this.strategy) {
      case RetryStrategy.FIXED_DELAY:
        delay = this.baseDelay;
        break;
      
      case RetryStrategy.LINEAR_BACKOFF:
        delay = this.baseDelay * (attempt + 1);
        break;
      
      case RetryStrategy.EXPONENTIAL_BACKOFF:
      default:
        delay = this.baseDelay * Math.pow(2, attempt);
        break;
    }

    // 限制最大延迟
    delay = Math.min(delay, this.maxDelay);

    // 添加随机抖动（±25%）
    if (this.jitter) {
      const jitterRange = delay * 0.25;
      delay += (Math.random() - 0.5) * 2 * jitterRange;
    }

    return Math.max(delay, 0);
  }

  /**
   * 判断是否应该重试
   */
  shouldRetry(error, attempt) {
    if (attempt >= this.maxRetries) {
      return false;
    }

    const errorType = ErrorClassifier.classify(error);
    
    if (this.retryableErrors) {
      return this.retryableErrors.includes(errorType);
    }
    
    return ErrorClassifier.isRetryable(errorType);
  }
}

/**
 * 重试执行器
 */
class RetryExecutor {
  constructor(config = {}) {
    this.config = new RetryConfig(config);
    this.startTime = null;
  }

  /**
   * 执行带重试的异步函数
   */
  async execute(fn, context = null) {
    this.startTime = Date.now();
    let lastError = null;
    let attempt = 0;

    while (attempt <= this.config.maxRetries) {
      try {
        // 检查总超时
        if (Date.now() - this.startTime > this.config.timeout) {
          throw new Error(`操作总超时 (${this.config.timeout}ms)`);
        }

        // 执行函数
        const result = await fn.call(context);
        
        // 成功则返回结果
        if (attempt > 0) {
          console.log(`✅ 重试成功 (第${attempt}次重试)`);
        }
        return result;

      } catch (error) {
        lastError = error;
        const errorType = ErrorClassifier.classify(error);
        
        console.warn(`❌ 执行失败 (第${attempt + 1}次尝试): ${error.message}`);
        console.warn(`🏷️  错误类型: ${errorType}`);

        // 判断是否应该重试
        if (!this.config.shouldRetry(error, attempt)) {
          console.error(`🚫 不可重试的错误或达到最大重试次数`);
          break;
        }

        // 计算延迟时间
        const delay = this.config.calculateDelay(attempt);
        console.log(`⏳ ${delay}ms后进行第${attempt + 1}次重试...`);

        // 调用重试回调
        if (this.config.onRetry) {
          try {
            await this.config.onRetry(error, attempt, delay);
          } catch (callbackError) {
            console.warn('⚠️  重试回调执行失败:', callbackError.message);
          }
        }

        // 等待延迟
        await this.delay(delay);
        attempt++;
      }
    }

    // 所有重试都失败了
    const totalTime = Date.now() - this.startTime;
    const finalError = new Error(
      `操作失败，已重试${attempt}次，总耗时${totalTime}ms。最后错误: ${lastError.message}`
    );
    finalError.originalError = lastError;
    finalError.attempts = attempt;
    finalError.totalTime = totalTime;
    
    throw finalError;
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 重试装饰器函数
 */
function withRetry(fn, config = {}) {
  return async function(...args) {
    const executor = new RetryExecutor(config);
    return await executor.execute(() => fn.apply(this, args), this);
  };
}

/**
 * 重试装饰器（类方法）
 */
function retryMethod(config = {}) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function(...args) {
      const executor = new RetryExecutor(config);
      return await executor.execute(() => originalMethod.apply(this, args), this);
    };
    
    return descriptor;
  };
}

/**
 * 简化的重试函数
 */
async function retry(fn, options = {}) {
  const executor = new RetryExecutor(options);
  return await executor.execute(fn);
}

/**
 * 错误处理工具类
 */
class ErrorHandler {
  /**
   * 创建详细的错误信息
   */
  static createDetailedError(error, context = {}) {
    const errorType = ErrorClassifier.classify(error);
    
    return {
      message: error.message,
      type: errorType,
      isRetryable: ErrorClassifier.isRetryable(errorType),
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      originalError: error
    };
  }

  /**
   * 记录错误日志
   */
  static logError(error, context = {}) {
    const detailedError = this.createDetailedError(error, context);
    
    console.error('🚨 错误详情:');
    console.error(`   消息: ${detailedError.message}`);
    console.error(`   类型: ${detailedError.type}`);
    console.error(`   可重试: ${detailedError.isRetryable ? '是' : '否'}`);
    console.error(`   时间: ${detailedError.timestamp}`);
    
    if (Object.keys(context).length > 0) {
      console.error(`   上下文:`, context);
    }
    
    return detailedError;
  }
}

module.exports = {
  ErrorType,
  RetryStrategy,
  ErrorClassifier,
  RetryConfig,
  RetryExecutor,
  ErrorHandler,
  withRetry,
  retryMethod,
  retry
};
