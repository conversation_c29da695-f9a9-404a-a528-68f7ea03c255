/**
 * Windows打包脚本
 * 将UNR自动化工具打包成Windows可执行文件
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class WindowsBuilder {
  constructor() {
    this.projectRoot = path.join(__dirname, '..');
    this.distDir = path.join(this.projectRoot, 'dist');
    this.assetsDir = path.join(this.distDir, 'assets');
  }

  /**
   * 执行打包流程
   */
  async build() {
    console.log('🚀 开始Windows打包流程...');
    
    try {
      // 1. 清理输出目录
      await this.cleanDist();
      
      // 2. 安装依赖
      await this.installDependencies();
      
      // 3. 执行pkg打包
      await this.runPkg();
      
      // 4. 复制资源文件
      await this.copyAssets();
      
      // 5. 创建启动脚本
      await this.createLauncher();
      
      // 6. 生成说明文档
      await this.generateReadme();
      
      console.log('✅ Windows打包完成！');
      console.log(`📁 输出目录: ${this.distDir}`);
      
    } catch (error) {
      console.error('❌ 打包失败:', error.message);
      process.exit(1);
    }
  }

  /**
   * 清理输出目录
   */
  async cleanDist() {
    console.log('🧹 清理输出目录...');
    
    if (fs.existsSync(this.distDir)) {
      fs.rmSync(this.distDir, { recursive: true, force: true });
    }
    
    fs.mkdirSync(this.distDir, { recursive: true });
    fs.mkdirSync(this.assetsDir, { recursive: true });
  }

  /**
   * 安装依赖
   */
  async installDependencies() {
    console.log('📦 检查依赖...');
    
    try {
      // 检查pkg是否已安装
      execSync('pkg --version', { stdio: 'ignore' });
      console.log('✅ pkg已安装');
    } catch (error) {
      console.log('📦 安装pkg...');
      execSync('npm install -g pkg', { stdio: 'inherit' });
    }
  }

  /**
   * 执行pkg打包
   */
  async runPkg() {
    console.log('📦 执行pkg打包...');
    
    const pkgCommand = [
      'pkg',
      '.',
      '--targets', 'node18-win-x64',
      '--out-path', 'dist',
      '--compress', 'GZip'
    ].join(' ');
    
    execSync(pkgCommand, { 
      stdio: 'inherit',
      cwd: this.projectRoot
    });
  }

  /**
   * 复制资源文件
   */
  async copyAssets() {
    console.log('📁 复制资源文件...');
    
    const assetsToCopy = [
      { src: 'test.xlsx', dest: 'test.xlsx' },
      { src: 'config', dest: 'config' },
      { src: 'docs', dest: 'docs' },
      { src: 'package.json', dest: 'package.json' }
    ];

    for (const asset of assetsToCopy) {
      const srcPath = path.join(this.projectRoot, asset.src);
      const destPath = path.join(this.assetsDir, asset.dest);
      
      if (fs.existsSync(srcPath)) {
        if (fs.statSync(srcPath).isDirectory()) {
          this.copyDirectory(srcPath, destPath);
        } else {
          fs.copyFileSync(srcPath, destPath);
        }
        console.log(`✅ 复制: ${asset.src} -> ${asset.dest}`);
      }
    }

    // 创建必要的目录
    const dirsToCreate = ['screenshots', 'logs', 'cache'];
    for (const dir of dirsToCreate) {
      const dirPath = path.join(this.assetsDir, dir);
      fs.mkdirSync(dirPath, { recursive: true });
      
      // 创建.gitkeep文件
      fs.writeFileSync(path.join(dirPath, '.gitkeep'), '');
    }
  }

  /**
   * 复制目录
   */
  copyDirectory(src, dest) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }

    const items = fs.readdirSync(src);
    
    for (const item of items) {
      const srcPath = path.join(src, item);
      const destPath = path.join(dest, item);
      
      if (fs.statSync(srcPath).isDirectory()) {
        this.copyDirectory(srcPath, destPath);
      } else {
        fs.copyFileSync(srcPath, destPath);
      }
    }
  }

  /**
   * 创建启动脚本
   */
  async createLauncher() {
    console.log('🚀 创建启动脚本...');
    
    // Windows批处理文件
    const batContent = `@echo off
title UNR自动化工具
echo.
echo ========================================
echo    UNR自动化流程管理工具
echo ========================================
echo.

REM 设置工作目录
cd /d "%~dp0"

REM 启动程序
"browser-automation.exe"

REM 如果程序异常退出，暂停以查看错误信息
if errorlevel 1 (
    echo.
    echo 程序异常退出，请检查错误信息
    pause
)
`;

    fs.writeFileSync(path.join(this.distDir, 'UNR自动化工具.bat'), batContent);

    // PowerShell脚本
    const ps1Content = `# UNR自动化工具启动脚本
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    UNR自动化流程管理工具" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 设置工作目录
Set-Location $PSScriptRoot

# 启动程序
try {
    & ".\\browser-automation.exe"
} catch {
    Write-Host "程序启动失败: $_" -ForegroundColor Red
    Read-Host "按回车键退出"
}
`;

    fs.writeFileSync(path.join(this.distDir, 'UNR自动化工具.ps1'), ps1Content);
  }

  /**
   * 生成说明文档
   */
  async generateReadme() {
    console.log('📝 生成说明文档...');
    
    const readmeContent = `# UNR自动化工具 - Windows版

## 简介
UNR自动化流程管理工具的Windows可执行版本，无需安装Node.js即可运行。

## 系统要求
- Windows 10/11 (64位)
- 至少2GB可用内存
- 网络连接

## 安装和使用

### 快速启动
1. 双击 \`UNR自动化工具.bat\` 启动程序
2. 或者双击 \`browser-automation.exe\` 直接运行

### 首次使用
1. 启动程序后，选择"8. 📁 路径配置"
2. 配置Chrome浏览器路径和缓存目录
3. 配置Excel数据文件路径
4. 保存配置

### 目录结构
\`\`\`
UNR自动化工具/
├── browser-automation.exe     # 主程序
├── UNR自动化工具.bat          # 启动脚本(推荐)
├── UNR自动化工具.ps1          # PowerShell启动脚本
├── assets/                    # 资源文件
│   ├── config/               # 配置文件
│   ├── docs/                 # 文档
│   ├── test.xlsx            # 示例数据文件
│   ├── screenshots/         # 截图目录
│   ├── logs/                # 日志目录
│   └── cache/               # 缓存目录
└── README.md                # 本文档
\`\`\`

## 配置说明

### Chrome路径配置
程序会自动检测Chrome安装路径，如果检测失败，请手动配置：
- 标准路径: \`C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe\`
- 用户路径: \`%USERPROFILE%\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe\`

### 缓存目录配置
建议设置独立的缓存目录，避免与系统Chrome冲突：
- 推荐路径: \`%USERPROFILE%\\AppData\\Local\\UNR-Automation\\Cache\`

### 数据文件配置
- 支持Excel文件(.xlsx格式)
- 默认使用assets目录下的test.xlsx
- 可配置自定义路径

## 功能特性
- ✅ 交互式菜单界面
- ✅ 浏览器指纹随机化
- ✅ 自定义路径配置
- ✅ 批量处理支持
- ✅ 实时进度监控
- ✅ 详细日志记录

## 故障排除

### 常见问题
1. **程序无法启动**
   - 检查Windows版本是否支持
   - 以管理员身份运行
   - 检查防病毒软件是否阻止

2. **Chrome连接失败**
   - 确认Chrome路径配置正确
   - 检查端口9222是否被占用
   - 重启程序重试

3. **数据文件读取失败**
   - 检查Excel文件格式
   - 确认文件路径正确
   - 检查文件权限

### 日志文件
- 程序日志: \`assets/logs/\`
- 截图文件: \`assets/screenshots/\`
- 配置文件: \`assets/config/\`

## 技术支持
如遇问题，请检查日志文件或联系技术支持。

---
版本: 1.0.0
构建日期: ${new Date().toISOString().split('T')[0]}
`;

    fs.writeFileSync(path.join(this.distDir, 'README.md'), readmeContent);
  }
}

// 如果直接运行此脚本，执行打包
if (require.main === module) {
  const builder = new WindowsBuilder();
  builder.build().catch(console.error);
}

module.exports = { WindowsBuilder };
