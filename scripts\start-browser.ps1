# 浏览器自动化启动脚本 (PowerShell版本)
# 基于用户原始命令，添加远程调试端口支持

Write-Host "正在启动浏览器，支持Puppeteer连接..." -ForegroundColor Green
Write-Host ""

# 设置浏览器路径
$BrowserPath = "C:\Users\<USER>\AppData\Local\Chromium\Application"
$BrowserExe = "chrome.exe"
$FullPath = Join-Path $BrowserPath $BrowserExe

# 检查浏览器是否存在
if (-not (Test-Path $FullPath)) {
    Write-Host "错误: 找不到浏览器可执行文件" -ForegroundColor Red
    Write-Host "路径: $FullPath" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "请检查浏览器安装路径是否正确" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

# 检查端口9222是否被占用
try {
    $connection = Test-NetConnection -ComputerName localhost -Port 9222 -InformationLevel Quiet -WarningAction SilentlyContinue
    if ($connection) {
        Write-Host "警告: 端口9222已被占用，可能有其他浏览器实例正在运行" -ForegroundColor Yellow
        $choice = Read-Host "是否继续启动? (y/N)"
        if ($choice -ne "y" -and $choice -ne "Y") {
            Write-Host "启动已取消" -ForegroundColor Yellow
            exit 0
        }
    }
} catch {
    # 端口检查失败，继续启动
}

# 切换到浏览器目录
Set-Location $BrowserPath

# 构建启动参数（保持所有原始参数 + 调试端口）
$Arguments = @(
    "--fingerprint=2024",
    "--fingerprint-platform=macos",
    "--fingerprint-platform-version=15.2.0",
    "--fingerprint-brand=Edge",
    "--fingerprint-hardware-concurrency=4",
    "--timezone=America/Los_Angeles",
    "--lang=en-US",
    "--accept-lang=en-US,zh-CN",
    "--disable-non-proxied-udp",
    "--proxy-server=socks5://127.0.0.1:7897",
    "--disable-webgl",
    "--disable-webgl2",
    "--disable-accelerated-2d-canvas",
    "--user-data-dir=D:\chrome\bycache",
    "--remote-debugging-port=9222",
    "--remote-allow-origins=*"
)

Write-Host "启动参数:" -ForegroundColor Cyan
$Arguments | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
Write-Host ""

try {
    # 启动浏览器
    Write-Host "正在启动浏览器..." -ForegroundColor Green
    $Process = Start-Process -FilePath $BrowserExe -ArgumentList $Arguments -PassThru
    
    # 等待一下确保启动
    Start-Sleep -Seconds 2
    
    if ($Process.HasExited) {
        Write-Host "浏览器启动失败，进程已退出" -ForegroundColor Red
        Write-Host "退出代码: $($Process.ExitCode)" -ForegroundColor Red
        exit $Process.ExitCode
    }
    
    Write-Host "✓ 浏览器启动成功!" -ForegroundColor Green
    Write-Host "✓ 进程ID: $($Process.Id)" -ForegroundColor Green
    Write-Host "✓ 远程调试端口: 9222" -ForegroundColor Green
    Write-Host "✓ Puppeteer连接地址: ws://localhost:9222" -ForegroundColor Green
    Write-Host ""
    Write-Host "浏览器已在后台运行，可以开始使用Puppeteer连接" -ForegroundColor Yellow
    
} catch {
    Write-Host "启动浏览器时发生错误:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host ""
    Write-Host "请检查:" -ForegroundColor Yellow
    Write-Host "1. 浏览器路径是否正确" -ForegroundColor Yellow
    Write-Host "2. 端口9222是否被占用" -ForegroundColor Yellow
    Write-Host "3. 代理设置是否正确" -ForegroundColor Yellow
    exit 1
}

Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
