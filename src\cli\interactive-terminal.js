/**
 * UNR自动化流程交互式终端界面
 * 提供完整的菜单系统、参数配置和实时监控功能
 */

const readline = require('readline');
const fs = require('fs');
const path = require('path');
const chalk = require('chalk');
const figlet = require('figlet');
const { ExcelReader } = require('../utils/excel-reader');
const { BrowserRandomizer } = require('./browser-randomizer');
const { ConfigManager } = require('./config-manager');
const { UNRFullAutomation } = require('../examples/unr-full-automation');

class InteractiveTerminal {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    this.config = new ConfigManager();
    this.browserRandomizer = new BrowserRandomizer();
    this.currentSession = null;
    this.isRunning = false;
    
    // 默认配置
    this.settings = {
      dataFile: 'test.xlsx',
      startIndex: 0,
      endIndex: null,
      enableScreenshots: true,
      enableLogging: true,
      delayBetweenStudents: 3000,
      maxRetries: 2,
      randomizeBrowser: true,
      autoSaveConfig: true
    };
  }

  /**
   * 启动交互式终端
   */
  async start() {
    console.clear();
    await this.showWelcome();
    await this.loadConfig();
    await this.showMainMenu();
  }

  /**
   * 显示欢迎界面
   */
  async showWelcome() {
    return new Promise((resolve) => {
      figlet('UNR Automation', { font: 'Small' }, (err, data) => {
        if (err) {
          console.log(chalk.blue.bold('🎓 UNR自动化流程管理工具'));
        } else {
          console.log(chalk.blue.bold(data));
        }
        
        console.log(chalk.gray('====================================='));
        console.log(chalk.green('✨ 欢迎使用UNR自动化流程交互式终端'));
        console.log(chalk.gray('版本: 1.0.0 | 作者: AI Assistant'));
        console.log(chalk.gray('=====================================\n'));
        
        setTimeout(resolve, 1000);
      });
    });
  }

  /**
   * 加载配置
   */
  async loadConfig() {
    try {
      const savedConfig = await this.config.load();
      if (savedConfig) {
        this.settings = { ...this.settings, ...savedConfig };
        console.log(chalk.green('✅ 已加载保存的配置'));
      }
    } catch (error) {
      console.log(chalk.yellow('⚠️  使用默认配置'));
    }
  }

  /**
   * 显示主菜单
   */
  async showMainMenu() {
    while (true) {
      console.log(chalk.cyan.bold('\n📋 主菜单'));
      console.log(chalk.gray('─'.repeat(40)));
      console.log('1. 🚀 启动浏览器');
      console.log('2. 📝 运行注册流程');
      console.log('3. 📋 运行申请流程');
      console.log('4. 🔄 完整自动化流程');
      console.log('5. ⚙️  配置设置');
      console.log('6. 📊 查看数据文件');
      console.log('7. 🎲 浏览器随机化设置');
      console.log('8. 📈 查看运行报告');
      console.log('9. ❓ 帮助');
      console.log('0. 🚪 退出');
      console.log(chalk.gray('─'.repeat(40)));

      const choice = await this.prompt(chalk.yellow('请选择操作 (0-9): '));
      
      try {
        switch (choice.trim()) {
          case '1':
            await this.startBrowser();
            break;
          case '2':
            await this.runRegistrationFlow();
            break;
          case '3':
            await this.runApplicationFlow();
            break;
          case '4':
            await this.runFullAutomation();
            break;
          case '5':
            await this.configureSettings();
            break;
          case '6':
            await this.viewDataFile();
            break;
          case '7':
            await this.configureBrowserRandomization();
            break;
          case '8':
            await this.viewReports();
            break;
          case '9':
            await this.showHelp();
            break;
          case '0':
            await this.exit();
            return;
          default:
            console.log(chalk.red('❌ 无效选择，请重试'));
        }
      } catch (error) {
        console.log(chalk.red(`❌ 操作失败: ${error.message}`));
        await this.prompt(chalk.gray('按回车键继续...'));
      }
    }
  }

  /**
   * 启动浏览器
   */
  async startBrowser() {
    console.log(chalk.cyan('\n🚀 启动浏览器'));
    console.log(chalk.gray('─'.repeat(30)));
    
    const useRandomization = await this.confirm('是否使用浏览器随机化? (y/n): ');
    
    if (useRandomization) {
      const randomConfig = this.browserRandomizer.generateRandomConfig();
      console.log(chalk.blue('🎲 生成随机浏览器配置:'));
      console.log(`   User-Agent: ${randomConfig.userAgent.substring(0, 60)}...`);
      console.log(`   分辨率: ${randomConfig.viewport.width}x${randomConfig.viewport.height}`);
      console.log(`   时区: ${randomConfig.timezone}`);
      console.log(`   语言: ${randomConfig.language}`);
    }
    
    try {
      const { spawn } = require('child_process');
      const browserProcess = spawn('npm', ['run', 'browser'], {
        stdio: 'inherit',
        shell: true
      });
      
      console.log(chalk.green('✅ 浏览器启动命令已执行'));
      await this.prompt(chalk.gray('按回车键返回主菜单...'));
      
    } catch (error) {
      console.log(chalk.red(`❌ 启动浏览器失败: ${error.message}`));
    }
  }

  /**
   * 运行注册流程
   */
  async runRegistrationFlow() {
    console.log(chalk.cyan('\n📝 运行注册流程'));
    console.log(chalk.gray('─'.repeat(30)));
    
    const studentIndex = await this.getStudentIndex('注册');
    if (studentIndex === null) return;
    
    try {
      const { spawn } = require('child_process');
      const env = { 
        ...process.env, 
        STUDENT_INDEX: studentIndex.toString()
      };
      
      console.log(chalk.blue(`🔄 开始为第${studentIndex + 1}个学生执行注册流程...`));
      
      const registrationProcess = spawn('node', ['src/examples/unr-complete-flow.js'], {
        env: env,
        stdio: 'inherit',
        shell: true
      });
      
      registrationProcess.on('close', (code) => {
        if (code === 0) {
          console.log(chalk.green('✅ 注册流程完成'));
        } else {
          console.log(chalk.red(`❌ 注册流程失败，退出码: ${code}`));
        }
      });
      
      await this.prompt(chalk.gray('按回车键返回主菜单...'));
      
    } catch (error) {
      console.log(chalk.red(`❌ 运行注册流程失败: ${error.message}`));
    }
  }

  /**
   * 运行申请流程
   */
  async runApplicationFlow() {
    console.log(chalk.cyan('\n📋 运行申请流程'));
    console.log(chalk.gray('─'.repeat(30)));
    
    const studentIndex = await this.getStudentIndex('申请');
    if (studentIndex === null) return;
    
    try {
      const { spawn } = require('child_process');
      const env = { 
        ...process.env, 
        STUDENT_INDEX: studentIndex.toString()
      };
      
      console.log(chalk.blue(`🔄 开始为第${studentIndex + 1}个学生执行申请流程...`));
      
      const applicationProcess = spawn('node', ['src/examples/unr-start-application.js'], {
        env: env,
        stdio: 'inherit',
        shell: true
      });
      
      applicationProcess.on('close', (code) => {
        if (code === 0) {
          console.log(chalk.green('✅ 申请流程完成'));
        } else {
          console.log(chalk.red(`❌ 申请流程失败，退出码: ${code}`));
        }
      });
      
      await this.prompt(chalk.gray('按回车键返回主菜单...'));
      
    } catch (error) {
      console.log(chalk.red(`❌ 运行申请流程失败: ${error.message}`));
    }
  }

  /**
   * 运行完整自动化流程
   */
  async runFullAutomation() {
    console.log(chalk.cyan('\n🔄 完整自动化流程'));
    console.log(chalk.gray('─'.repeat(30)));
    
    const range = await this.getStudentRange();
    if (!range) return;
    
    console.log(chalk.blue(`📊 将处理第${range.start + 1}到第${range.end}个学生`));
    const confirm = await this.confirm('确认开始执行? (y/n): ');
    
    if (!confirm) {
      console.log(chalk.yellow('❌ 操作已取消'));
      return;
    }
    
    try {
      const automation = new UNRFullAutomation({
        startIndex: range.start,
        endIndex: range.end,
        dataFile: this.settings.dataFile,
        enableScreenshots: this.settings.enableScreenshots,
        enableLogging: this.settings.enableLogging,
        delayBetweenStudents: this.settings.delayBetweenStudents,
        maxRetries: this.settings.maxRetries
      });
      
      console.log(chalk.green('🚀 开始执行完整自动化流程...'));
      this.isRunning = true;
      
      await automation.start();
      
      console.log(chalk.green('✅ 完整自动化流程执行完成'));
      
    } catch (error) {
      console.log(chalk.red(`❌ 完整自动化流程失败: ${error.message}`));
    } finally {
      this.isRunning = false;
      await this.prompt(chalk.gray('按回车键返回主菜单...'));
    }
  }

  /**
   * 获取学生索引
   */
  async getStudentIndex(flowType) {
    const totalStudents = await this.getTotalStudents();
    if (totalStudents === 0) {
      console.log(chalk.red('❌ 数据文件中没有学生数据'));
      return null;
    }
    
    console.log(chalk.blue(`📊 数据文件中共有 ${totalStudents} 个学生`));
    
    const input = await this.prompt(`请输入要执行${flowType}的学生编号 (1-${totalStudents}): `);
    const studentNumber = parseInt(input.trim());
    
    if (isNaN(studentNumber) || studentNumber < 1 || studentNumber > totalStudents) {
      console.log(chalk.red('❌ 无效的学生编号'));
      return null;
    }
    
    return studentNumber - 1; // 转换为索引
  }

  /**
   * 获取学生范围
   */
  async getStudentRange() {
    const totalStudents = await this.getTotalStudents();
    if (totalStudents === 0) {
      console.log(chalk.red('❌ 数据文件中没有学生数据'));
      return null;
    }
    
    console.log(chalk.blue(`📊 数据文件中共有 ${totalStudents} 个学生`));
    
    const startInput = await this.prompt(`请输入起始学生编号 (1-${totalStudents}, 默认1): `);
    const start = startInput.trim() ? parseInt(startInput.trim()) - 1 : 0;
    
    const endInput = await this.prompt(`请输入结束学生编号 (${start + 2}-${totalStudents}, 默认${totalStudents}): `);
    const end = endInput.trim() ? parseInt(endInput.trim()) : totalStudents;
    
    if (isNaN(start) || isNaN(end) || start < 0 || end > totalStudents || start >= end) {
      console.log(chalk.red('❌ 无效的范围'));
      return null;
    }
    
    return { start, end };
  }

  /**
   * 获取学生总数
   */
  async getTotalStudents() {
    try {
      const filePath = path.resolve(this.settings.dataFile);
      if (!fs.existsSync(filePath)) {
        return 0;
      }
      
      const data = await ExcelReader.readExcelFile(filePath);
      return data.length;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 提示用户输入
   */
  prompt(question) {
    return new Promise((resolve) => {
      this.rl.question(question, resolve);
    });
  }

  /**
   * 确认对话框
   */
  async confirm(question) {
    const answer = await this.prompt(question);
    return ['y', 'yes', '是', '1', 'true'].includes(answer.toLowerCase().trim());
  }

  /**
   * 配置设置
   */
  async configureSettings() {
    console.log(chalk.cyan('\n⚙️  配置设置'));
    console.log(chalk.gray('─'.repeat(30)));

    while (true) {
      console.log('\n📋 当前配置:');
      console.log(`1. 数据文件: ${chalk.yellow(this.settings.dataFile)}`);
      console.log(`2. 截图功能: ${this.settings.enableScreenshots ? chalk.green('启用') : chalk.red('禁用')}`);
      console.log(`3. 日志功能: ${this.settings.enableLogging ? chalk.green('启用') : chalk.red('禁用')}`);
      console.log(`4. 学生间延迟: ${chalk.yellow(this.settings.delayBetweenStudents)}ms`);
      console.log(`5. 最大重试次数: ${chalk.yellow(this.settings.maxRetries)}`);
      console.log(`6. 浏览器随机化: ${this.settings.randomizeBrowser ? chalk.green('启用') : chalk.red('禁用')}`);
      console.log(`7. 自动保存配置: ${this.settings.autoSaveConfig ? chalk.green('启用') : chalk.red('禁用')}`);
      console.log('8. 💾 保存配置');
      console.log('9. 🔄 重置为默认');
      console.log('0. 🔙 返回主菜单');

      const choice = await this.prompt(chalk.yellow('\n请选择要修改的配置 (0-9): '));

      switch (choice.trim()) {
        case '1':
          await this.configureDataFile();
          break;
        case '2':
          this.settings.enableScreenshots = !this.settings.enableScreenshots;
          console.log(chalk.green(`✅ 截图功能已${this.settings.enableScreenshots ? '启用' : '禁用'}`));
          break;
        case '3':
          this.settings.enableLogging = !this.settings.enableLogging;
          console.log(chalk.green(`✅ 日志功能已${this.settings.enableLogging ? '启用' : '禁用'}`));
          break;
        case '4':
          await this.configureDelay();
          break;
        case '5':
          await this.configureRetries();
          break;
        case '6':
          this.settings.randomizeBrowser = !this.settings.randomizeBrowser;
          console.log(chalk.green(`✅ 浏览器随机化已${this.settings.randomizeBrowser ? '启用' : '禁用'}`));
          break;
        case '7':
          this.settings.autoSaveConfig = !this.settings.autoSaveConfig;
          console.log(chalk.green(`✅ 自动保存配置已${this.settings.autoSaveConfig ? '启用' : '禁用'}`));
          break;
        case '8':
          await this.saveConfig();
          break;
        case '9':
          await this.resetConfig();
          break;
        case '0':
          return;
        default:
          console.log(chalk.red('❌ 无效选择'));
      }
    }
  }

  /**
   * 配置数据文件
   */
  async configureDataFile() {
    console.log(chalk.blue('\n📁 配置数据文件'));

    const recentFiles = await this.config.getRecentFiles();
    if (recentFiles.length > 0) {
      console.log('\n📋 最近使用的文件:');
      recentFiles.forEach((file, index) => {
        console.log(`${index + 1}. ${file}`);
      });
      console.log(`${recentFiles.length + 1}. 输入新路径`);

      const choice = await this.prompt('请选择文件或输入新路径: ');
      const choiceNum = parseInt(choice.trim());

      if (choiceNum > 0 && choiceNum <= recentFiles.length) {
        this.settings.dataFile = recentFiles[choiceNum - 1];
        console.log(chalk.green(`✅ 已选择文件: ${this.settings.dataFile}`));
        return;
      }
    }

    const filePath = await this.prompt('请输入Excel文件路径: ');
    if (filePath.trim()) {
      const fs = require('fs');
      const path = require('path');
      const fullPath = path.resolve(filePath.trim());

      if (fs.existsSync(fullPath)) {
        this.settings.dataFile = filePath.trim();
        await this.config.addRecentFile(fullPath);
        console.log(chalk.green(`✅ 数据文件已设置: ${this.settings.dataFile}`));
      } else {
        console.log(chalk.red('❌ 文件不存在'));
      }
    }
  }

  /**
   * 配置延迟时间
   */
  async configureDelay() {
    const input = await this.prompt(`请输入学生间延迟时间(ms, 当前: ${this.settings.delayBetweenStudents}): `);
    const delay = parseInt(input.trim());

    if (!isNaN(delay) && delay >= 0) {
      this.settings.delayBetweenStudents = delay;
      console.log(chalk.green(`✅ 延迟时间已设置为: ${delay}ms`));
    } else {
      console.log(chalk.red('❌ 无效的延迟时间'));
    }
  }

  /**
   * 配置重试次数
   */
  async configureRetries() {
    const input = await this.prompt(`请输入最大重试次数(当前: ${this.settings.maxRetries}): `);
    const retries = parseInt(input.trim());

    if (!isNaN(retries) && retries >= 0) {
      this.settings.maxRetries = retries;
      console.log(chalk.green(`✅ 最大重试次数已设置为: ${retries}`));
    } else {
      console.log(chalk.red('❌ 无效的重试次数'));
    }
  }

  /**
   * 保存配置
   */
  async saveConfig() {
    const success = await this.config.save(this.settings);
    if (success) {
      console.log(chalk.green('✅ 配置保存成功'));
    } else {
      console.log(chalk.red('❌ 配置保存失败'));
    }
  }

  /**
   * 重置配置
   */
  async resetConfig() {
    const confirm = await this.confirm('确认重置为默认配置? (y/n): ');
    if (confirm) {
      const success = await this.config.reset();
      if (success) {
        this.settings = await this.config.load();
        console.log(chalk.green('✅ 配置已重置为默认值'));
      } else {
        console.log(chalk.red('❌ 重置配置失败'));
      }
    }
  }

  /**
   * 查看数据文件
   */
  async viewDataFile() {
    console.log(chalk.cyan('\n📊 查看数据文件'));
    console.log(chalk.gray('─'.repeat(30)));

    try {
      const fs = require('fs');
      const filePath = path.resolve(this.settings.dataFile);

      if (!fs.existsSync(filePath)) {
        console.log(chalk.red(`❌ 文件不存在: ${this.settings.dataFile}`));
        return;
      }

      console.log(chalk.blue(`📁 文件路径: ${filePath}`));

      const data = await ExcelReader.readExcelFile(filePath);
      const students = ExcelReader.convertToUNRFormat(data);

      console.log(chalk.green(`📊 学生总数: ${students.length}`));

      if (students.length > 0) {
        console.log(chalk.blue('\n📋 前5个学生预览:'));
        students.slice(0, 5).forEach((student, index) => {
          console.log(`${index + 1}. ${student.firstName} ${student.lastName} (${student.email})`);
        });

        if (students.length > 5) {
          console.log(chalk.gray(`... 还有 ${students.length - 5} 个学生`));
        }
      }

    } catch (error) {
      console.log(chalk.red(`❌ 读取文件失败: ${error.message}`));
    }

    await this.prompt(chalk.gray('按回车键返回主菜单...'));
  }

  /**
   * 配置浏览器随机化
   */
  async configureBrowserRandomization() {
    console.log(chalk.cyan('\n🎲 浏览器随机化设置'));
    console.log(chalk.gray('─'.repeat(30)));

    while (true) {
      console.log('\n📋 随机化选项:');
      console.log('1. 🎯 生成随机配置');
      console.log('2. 👀 查看当前配置');
      console.log('3. 💾 保存配置');
      console.log('4. 📁 加载配置');
      console.log('5. 🧪 测试配置');
      console.log('0. 🔙 返回主菜单');

      const choice = await this.prompt(chalk.yellow('请选择操作 (0-5): '));

      switch (choice.trim()) {
        case '1':
          await this.generateRandomBrowserConfig();
          break;
        case '2':
          await this.viewBrowserConfig();
          break;
        case '3':
          await this.saveBrowserConfig();
          break;
        case '4':
          await this.loadBrowserConfig();
          break;
        case '5':
          await this.testBrowserConfig();
          break;
        case '0':
          return;
        default:
          console.log(chalk.red('❌ 无效选择'));
      }
    }
  }

  /**
   * 生成随机浏览器配置
   */
  async generateRandomBrowserConfig() {
    const config = this.browserRandomizer.generateRandomConfig();

    console.log(chalk.blue('\n🎲 生成的随机配置:'));
    console.log(`User-Agent: ${config.userAgent.substring(0, 80)}...`);
    console.log(`分辨率: ${config.viewport.width}x${config.viewport.height}`);
    console.log(`时区: ${config.timezone}`);
    console.log(`语言: ${config.language}`);
    console.log(`平台: ${config.platform}`);
    console.log(`硬件并发: ${config.hardwareConcurrency}`);
    console.log(`设备内存: ${config.deviceMemory}GB`);

    const save = await this.confirm('\n保存此配置? (y/n): ');
    if (save) {
      this.browserRandomizer.saveConfig(config);
      console.log(chalk.green('✅ 配置已保存'));
    }
  }

  /**
   * 查看浏览器配置
   */
  async viewBrowserConfig() {
    const config = this.browserRandomizer.loadConfig();

    if (!config) {
      console.log(chalk.yellow('⚠️  没有找到保存的配置'));
      return;
    }

    console.log(chalk.blue('\n👀 当前浏览器配置:'));
    console.log(JSON.stringify(config, null, 2));
  }

  /**
   * 保存浏览器配置
   */
  async saveBrowserConfig() {
    const filename = await this.prompt('请输入配置文件名 (默认: browser-config.json): ');
    const configName = filename.trim() || 'browser-config.json';

    try {
      const config = this.browserRandomizer.generateRandomConfig();
      this.browserRandomizer.saveConfig(config, configName);
      console.log(chalk.green(`✅ 配置已保存为: ${configName}`));
    } catch (error) {
      console.log(chalk.red(`❌ 保存失败: ${error.message}`));
    }
  }

  /**
   * 加载浏览器配置
   */
  async loadBrowserConfig() {
    const filename = await this.prompt('请输入配置文件名 (默认: browser-config.json): ');
    const configName = filename.trim() || 'browser-config.json';

    try {
      const config = this.browserRandomizer.loadConfig(configName);
      if (config) {
        console.log(chalk.green(`✅ 配置已加载: ${configName}`));
        console.log(chalk.blue('配置预览:'));
        console.log(`User-Agent: ${config.userAgent.substring(0, 60)}...`);
        console.log(`分辨率: ${config.viewport.width}x${config.viewport.height}`);
      } else {
        console.log(chalk.yellow(`⚠️  配置文件不存在: ${configName}`));
      }
    } catch (error) {
      console.log(chalk.red(`❌ 加载失败: ${error.message}`));
    }
  }

  /**
   * 测试浏览器配置
   */
  async testBrowserConfig() {
    console.log(chalk.blue('\n🧪 测试浏览器配置'));

    try {
      const config = this.browserRandomizer.generateRandomConfig();
      const validation = this.browserRandomizer.validateConfig(config);

      if (validation) {
        console.log(chalk.green('✅ 配置验证通过'));
        console.log(chalk.blue('生成的启动参数:'));
        const args = this.browserRandomizer.generateBrowserArgs(config);
        args.slice(0, 5).forEach(arg => console.log(`  ${arg}`));
        if (args.length > 5) {
          console.log(chalk.gray(`  ... 还有 ${args.length - 5} 个参数`));
        }
      } else {
        console.log(chalk.red('❌ 配置验证失败'));
      }
    } catch (error) {
      console.log(chalk.red(`❌ 测试失败: ${error.message}`));
    }
  }

  /**
   * 查看运行报告
   */
  async viewReports() {
    console.log(chalk.cyan('\n📈 查看运行报告'));
    console.log(chalk.gray('─'.repeat(30)));
    console.log(chalk.yellow('⚠️  报告功能开发中...'));
    await this.prompt(chalk.gray('按回车键返回主菜单...'));
  }

  /**
   * 显示帮助
   */
  async showHelp() {
    console.log(chalk.cyan('\n❓ 帮助信息'));
    console.log(chalk.gray('─'.repeat(30)));

    console.log(chalk.blue('🎓 UNR自动化流程管理工具使用指南\n'));

    console.log(chalk.yellow('主要功能:'));
    console.log('• 🚀 启动浏览器 - 启动Chrome浏览器用于自动化');
    console.log('• 📝 运行注册流程 - 为指定学生执行UNR账户注册');
    console.log('• 📋 运行申请流程 - 为指定学生执行UNR申请提交');
    console.log('• 🔄 完整自动化流程 - 批量执行注册+申请的完整流程');
    console.log('• ⚙️  配置设置 - 修改各种参数和选项');
    console.log('• 📊 查看数据文件 - 预览Excel文件中的学生数据');
    console.log('• 🎲 浏览器随机化 - 配置浏览器指纹随机化');

    console.log(chalk.yellow('\n数据文件格式:'));
    console.log('• Excel文件(.xlsx)包含学生信息');
    console.log('• 必需列: 邮箱, 名, 姓, 生日, 密码等');
    console.log('• 默认文件: test.xlsx');

    console.log(chalk.yellow('\n注意事项:'));
    console.log('• 确保浏览器已启动再运行流程');
    console.log('• 建议启用浏览器随机化避免检测');
    console.log('• 合理设置延迟时间避免过快操作');
    console.log('• 定期保存配置以便下次使用');

    await this.prompt(chalk.gray('\n按回车键返回主菜单...'));
  }

  /**
   * 退出程序
   */
  async exit() {
    if (this.settings.autoSaveConfig) {
      await this.config.save(this.settings);
      console.log(chalk.green('✅ 配置已保存'));
    }

    console.log(chalk.blue('\n👋 感谢使用UNR自动化流程管理工具！'));
    this.rl.close();
    process.exit(0);
  }
}

// 导出类
module.exports = { InteractiveTerminal };

// 如果直接运行此文件，启动交互式终端
if (require.main === module) {
  const terminal = new InteractiveTerminal();
  terminal.start().catch(console.error);
}
