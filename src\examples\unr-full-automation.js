/**
 * UNR完整自动化流程
 * 整合注册和申请流程，实现端到端自动化
 */

const path = require('path');
const { spawn } = require('child_process');
const { ExcelReader } = require('../utils/excel-reader');

class UNRFullAutomation {
  constructor(options = {}) {
    this.options = {
      dataFile: options.dataFile || path.join(__dirname, '../../test.xlsx'),
      startIndex: options.startIndex || 0,
      endIndex: options.endIndex || null,
      enableScreenshots: options.enableScreenshots !== false,
      delayBetweenStudents: options.delayBetweenStudents || 5000,
      maxRetries: options.maxRetries || 2,
      ...options
    };
    
    this.students = [];
    this.processedCount = 0;
    this.successCount = 0;
    this.failureCount = 0;
    this.results = [];
  }

  /**
   * 启动完整自动化流程
   */
  async start() {
    console.log('🎓 UNR完整自动化流程启动');
    console.log('==================================');
    
    try {
      // 读取学生数据
      await this.loadStudentData();
      
      // 处理每个学生
      await this.processAllStudents();
      
      // 生成最终报告
      this.generateFinalReport();
      
    } catch (error) {
      console.error('❌ 完整自动化流程失败:', error.message);
      throw error;
    }
  }

  /**
   * 读取学生数据
   */
  async loadStudentData() {
    console.log(`📖 读取学生数据: ${this.options.dataFile}`);
    
    try {
      this.students = await ExcelReader.readStudentData(this.options.dataFile);
      
      // 应用索引范围
      const startIndex = this.options.startIndex;
      const endIndex = this.options.endIndex || this.students.length;
      this.students = this.students.slice(startIndex, endIndex);
      
      console.log(`✅ 成功读取 ${this.students.length} 个学生数据`);
      console.log(`📊 处理范围: 第${startIndex + 1}到第${Math.min(endIndex, this.students.length + startIndex)}个学生`);
      
    } catch (error) {
      console.error('❌ 读取学生数据失败:', error.message);
      throw error;
    }
  }

  /**
   * 处理所有学生
   */
  async processAllStudents() {
    console.log('\n🚀 开始处理学生流程');
    console.log('====================');
    
    for (let i = 0; i < this.students.length; i++) {
      const student = this.students[i];
      const studentIndex = this.options.startIndex + i;
      
      console.log(`\n🎯 处理学生 #${studentIndex + 1}: ${student.firstName} ${student.lastName}`);
      console.log(`📧 邮箱: ${student.email}`);
      
      const result = await this.processStudent(student, studentIndex);
      this.results.push(result);
      
      this.processedCount++;
      if (result.success) {
        this.successCount++;
      } else {
        this.failureCount++;
      }
      
      // 学生之间的延迟
      if (i < this.students.length - 1) {
        console.log(`⏳ 等待 ${this.options.delayBetweenStudents}ms 后处理下一个学生...`);
        await new Promise(resolve => setTimeout(resolve, this.options.delayBetweenStudents));
      }
    }
  }

  /**
   * 处理单个学生的完整流程
   */
  async processStudent(student, studentIndex) {
    const result = {
      studentIndex: studentIndex,
      student: student,
      registrationSuccess: false,
      applicationSuccess: false,
      success: false,
      errors: [],
      startTime: new Date(),
      endTime: null,
      duration: null
    };

    try {
      console.log(`\n📋 步骤1: 注册流程 (${student.firstName} ${student.lastName})`);
      
      // 执行注册流程
      const registrationResult = await this.runRegistrationFlow(studentIndex);
      result.registrationSuccess = registrationResult.success;
      
      if (!registrationResult.success) {
        result.errors.push(`注册失败: ${registrationResult.error}`);
        console.log(`❌ 学生 ${student.firstName} ${student.lastName} 注册失败，跳过申请流程`);
        return result;
      }
      
      console.log(`✅ 学生 ${student.firstName} ${student.lastName} 注册成功`);
      
      // 注册成功后等待一段时间
      console.log('⏳ 注册完成，等待5秒后开始申请流程...');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      console.log(`\n📋 步骤2: 申请流程 (${student.firstName} ${student.lastName})`);
      
      // 执行申请流程
      const applicationResult = await this.runApplicationFlow(studentIndex);
      result.applicationSuccess = applicationResult.success;
      
      if (!applicationResult.success) {
        result.errors.push(`申请失败: ${applicationResult.error}`);
        console.log(`❌ 学生 ${student.firstName} ${student.lastName} 申请失败`);
      } else {
        console.log(`✅ 学生 ${student.firstName} ${student.lastName} 申请成功`);
        result.success = true;
      }
      
    } catch (error) {
      result.errors.push(`处理异常: ${error.message}`);
      console.error(`❌ 处理学生 ${student.firstName} ${student.lastName} 时发生异常:`, error.message);
    } finally {
      result.endTime = new Date();
      result.duration = result.endTime - result.startTime;
    }

    return result;
  }

  /**
   * 运行注册流程
   */
  async runRegistrationFlow(studentIndex) {
    return new Promise((resolve) => {
      console.log(`🔄 启动注册流程脚本...`);
      
      const env = { ...process.env, STUDENT_INDEX: studentIndex.toString() };
      const registrationProcess = spawn('node', ['src/examples/unr-complete-flow.js'], {
        cwd: process.cwd(),
        env: env,
        stdio: 'pipe'
      });

      let output = '';
      let errorOutput = '';

      registrationProcess.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        // 实时输出（可选）
        if (this.options.showRealTimeOutput) {
          process.stdout.write(`[注册] ${text}`);
        }
      });

      registrationProcess.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;
        if (this.options.showRealTimeOutput) {
          process.stderr.write(`[注册错误] ${text}`);
        }
      });

      registrationProcess.on('close', (code) => {
        const success = code === 0;
        console.log(`📊 注册流程结束，退出码: ${code}`);
        
        resolve({
          success: success,
          code: code,
          output: output,
          error: success ? null : (errorOutput || `进程退出码: ${code}`)
        });
      });

      registrationProcess.on('error', (error) => {
        console.error(`❌ 注册流程启动失败:`, error.message);
        resolve({
          success: false,
          code: -1,
          output: output,
          error: error.message
        });
      });
    });
  }

  /**
   * 运行申请流程
   */
  async runApplicationFlow(studentIndex) {
    return new Promise((resolve) => {
      console.log(`🔄 启动申请流程脚本...`);
      
      const env = { ...process.env, STUDENT_INDEX: studentIndex.toString() };
      const applicationProcess = spawn('node', ['src/examples/unr-start-application.js'], {
        cwd: process.cwd(),
        env: env,
        stdio: 'pipe'
      });

      let output = '';
      let errorOutput = '';

      applicationProcess.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        // 实时输出（可选）
        if (this.options.showRealTimeOutput) {
          process.stdout.write(`[申请] ${text}`);
        }
      });

      applicationProcess.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;
        if (this.options.showRealTimeOutput) {
          process.stderr.write(`[申请错误] ${text}`);
        }
      });

      applicationProcess.on('close', (code) => {
        const success = code === 0;
        console.log(`📊 申请流程结束，退出码: ${code}`);
        
        resolve({
          success: success,
          code: code,
          output: output,
          error: success ? null : (errorOutput || `进程退出码: ${code}`)
        });
      });

      applicationProcess.on('error', (error) => {
        console.error(`❌ 申请流程启动失败:`, error.message);
        resolve({
          success: false,
          code: -1,
          output: output,
          error: error.message
        });
      });
    });
  }

  /**
   * 生成最终报告
   */
  generateFinalReport() {
    console.log('\n🎉 完整自动化流程报告');
    console.log('========================');
    console.log(`📊 总处理学生数: ${this.processedCount}`);
    console.log(`✅ 成功完成数: ${this.successCount}`);
    console.log(`❌ 失败数: ${this.failureCount}`);
    console.log(`📈 成功率: ${((this.successCount / this.processedCount) * 100).toFixed(1)}%`);
    
    console.log('\n📋 详细结果:');
    this.results.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      const duration = result.duration ? `${Math.round(result.duration / 1000)}秒` : 'N/A';
      
      console.log(`${status} 学生 #${result.studentIndex + 1}: ${result.student.firstName} ${result.student.lastName}`);
      console.log(`   📧 邮箱: ${result.student.email}`);
      console.log(`   📋 注册: ${result.registrationSuccess ? '✅' : '❌'}`);
      console.log(`   📋 申请: ${result.applicationSuccess ? '✅' : '❌'}`);
      console.log(`   ⏱️  耗时: ${duration}`);
      
      if (result.errors.length > 0) {
        console.log(`   ❌ 错误: ${result.errors.join(', ')}`);
      }
      console.log('');
    });
  }
}

// 主函数
async function main() {
  try {
    // 从环境变量或命令行参数获取配置
    const startIndex = parseInt(process.env.START_INDEX || process.argv[2] || '0');
    const endIndex = process.env.END_INDEX ? parseInt(process.env.END_INDEX) : (process.argv[3] ? parseInt(process.argv[3]) : null);
    
    const automation = new UNRFullAutomation({
      startIndex: startIndex,
      endIndex: endIndex,
      showRealTimeOutput: process.env.SHOW_REAL_TIME === 'true',
      delayBetweenStudents: parseInt(process.env.DELAY_BETWEEN_STUDENTS || '5000')
    });
    
    await automation.start();
    
    console.log('\n🎉 完整自动化流程执行完成！');
    process.exit(0);
    
  } catch (error) {
    console.error('\n❌ 完整自动化流程执行失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main();
}

module.exports = { UNRFullAutomation };
