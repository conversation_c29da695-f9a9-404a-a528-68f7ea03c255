/**
 * UNR完整自动化流程
 * 整合注册和申请流程，实现端到端自动化
 * 在同一浏览器会话中完成注册和申请的无缝衔接
 */

const path = require('path');
const { ExcelReader } = require('../utils/excel-reader');
const { UNRCompleteFlow } = require('./unr-complete-flow');
const { UNRApplicationStarter } = require('./unr-start-application');

class UNRFullAutomation {
  constructor(options = {}) {
    this.options = {
      dataFile: options.dataFile || path.join(__dirname, '../../test.xlsx'),
      startIndex: options.startIndex || 0,
      endIndex: options.endIndex || null,
      enableScreenshots: options.enableScreenshots !== false,
      delayBetweenStudents: options.delayBetweenStudents || 3000, // 减少延迟时间
      maxRetries: options.maxRetries || 2,
      enableLogging: options.enableLogging !== false,
      ...options
    };

    this.students = [];
    this.processedCount = 0;
    this.successCount = 0;
    this.failureCount = 0;
    this.results = [];

    // 浏览器会话管理
    this.registrationFlow = null;
    this.applicationFlow = null;
    this.sharedFormFiller = null;
  }

  /**
   * 启动完整自动化流程
   */
  async start() {
    console.log('🎓 UNR完整自动化流程启动');
    console.log('==================================');

    try {
      // 初始化浏览器会话
      await this.initializeBrowserSession();

      // 读取学生数据
      await this.loadStudentData();

      // 处理每个学生
      await this.processAllStudents();

      // 生成最终报告
      this.generateFinalReport();

    } catch (error) {
      console.error('❌ 完整自动化流程失败:', error.message);
      throw error;
    } finally {
      // 清理资源
      await this.cleanup();
    }
  }

  /**
   * 初始化浏览器会话
   */
  async initializeBrowserSession() {
    console.log('🌐 初始化浏览器会话...');

    try {
      const { FormFiller } = require('../form-filler');

      // 创建共享的FormFiller实例
      this.sharedFormFiller = new FormFiller({
        enableLogging: this.options.enableLogging,
        enableScreenshots: this.options.enableScreenshots
      });

      // 初始化浏览器连接
      await this.sharedFormFiller.initialize();

      console.log('✅ 浏览器会话初始化成功');

    } catch (error) {
      console.error('❌ 浏览器会话初始化失败:', error.message);
      throw error;
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    console.log('🧹 清理资源...');

    try {
      if (this.sharedFormFiller) {
        console.log('🔄 关闭浏览器...');

        try {
          // 关闭浏览器连接
          await this.sharedFormFiller.close();
          console.log('✅ 浏览器连接已关闭');
        } catch (browserError) {
          console.error('⚠️  关闭浏览器连接失败:', browserError.message);

          // 尝试强制关闭浏览器进程
          try {
            if (this.sharedFormFiller.connector && this.sharedFormFiller.connector.browser) {
              await this.sharedFormFiller.connector.browser.close();
              console.log('✅ 浏览器进程已强制关闭');
            }
          } catch (forceCloseError) {
            console.error('⚠️  强制关闭浏览器失败:', forceCloseError.message);
          }
        }
      }

      this.registrationFlow = null;
      this.applicationFlow = null;
      this.sharedFormFiller = null;

      console.log('✅ 资源清理完成');

    } catch (error) {
      console.error('⚠️  资源清理失败:', error.message);
    }
  }

  /**
   * 读取学生数据
   */
  async loadStudentData() {
    console.log(`📖 读取学生数据: ${this.options.dataFile}`);

    try {
      const rawData = await ExcelReader.readExcelFile(this.options.dataFile);
      this.students = ExcelReader.convertToUNRFormat(rawData);
      
      // 应用索引范围
      const startIndex = this.options.startIndex;
      const endIndex = this.options.endIndex || this.students.length;
      this.students = this.students.slice(startIndex, endIndex);
      
      console.log(`✅ 成功读取 ${this.students.length} 个学生数据`);
      console.log(`📊 处理范围: 第${startIndex + 1}到第${Math.min(endIndex, this.students.length + startIndex)}个学生`);
      
    } catch (error) {
      console.error('❌ 读取学生数据失败:', error.message);
      throw error;
    }
  }

  /**
   * 处理所有学生
   */
  async processAllStudents() {
    console.log('\n🚀 开始处理学生流程');
    console.log('====================');
    
    for (let i = 0; i < this.students.length; i++) {
      const student = this.students[i];
      const studentIndex = this.options.startIndex + i;
      
      console.log(`\n🎯 处理学生 #${studentIndex + 1}: ${student.firstName} ${student.lastName}`);
      console.log(`📧 邮箱: ${student.email}`);
      
      const result = await this.processStudent(student, studentIndex);
      this.results.push(result);
      
      this.processedCount++;
      if (result.success) {
        this.successCount++;
      } else {
        this.failureCount++;
      }
      
      // 学生之间的延迟
      if (i < this.students.length - 1) {
        console.log(`⏳ 等待 ${this.options.delayBetweenStudents}ms 后处理下一个学生...`);
        await new Promise(resolve => setTimeout(resolve, this.options.delayBetweenStudents));
      }
    }
  }

  /**
   * 处理单个学生的完整流程（无缝衔接）
   */
  async processStudent(student, studentIndex) {
    const result = {
      studentIndex: studentIndex,
      student: student,
      registrationSuccess: false,
      applicationSuccess: false,
      success: false,
      errors: [],
      startTime: new Date(),
      endTime: null,
      duration: null
    };

    try {
      console.log(`\n📋 步骤1: 注册流程 (${student.firstName} ${student.lastName})`);

      // 执行注册流程（在同一浏览器会话中）
      const registrationResult = await this.runRegistrationFlowSeamless(student, studentIndex);
      result.registrationSuccess = registrationResult.success;

      if (!registrationResult.success) {
        result.errors.push(`注册失败: ${registrationResult.error}`);
        console.log(`❌ 学生 ${student.firstName} ${student.lastName} 注册失败，跳过申请流程`);
        return result;
      }

      console.log(`✅ 学生 ${student.firstName} ${student.lastName} 注册成功`);

      // 无缝衔接：注册完成后直接进入申请流程
      console.log('🔄 无缝衔接：注册完成，直接进入申请流程...');
      console.log('💡 优化：保持浏览器会话，无需重新登录');

      // 短暂等待确保页面稳定
      await new Promise(resolve => setTimeout(resolve, 1000));

      console.log(`\n📋 步骤2: 申请流程 (${student.firstName} ${student.lastName})`);

      // 执行申请流程（在同一浏览器会话中）
      const applicationResult = await this.runApplicationFlowSeamless(student, studentIndex);
      result.applicationSuccess = applicationResult.success;

      if (!applicationResult.success) {
        result.errors.push(`申请失败: ${applicationResult.error}`);
        console.log(`❌ 学生 ${student.firstName} ${student.lastName} 申请失败`);
      } else {
        console.log(`✅ 学生 ${student.firstName} ${student.lastName} 申请成功`);
        result.success = true;
      }

    } catch (error) {
      result.errors.push(`处理异常: ${error.message}`);
      console.error(`❌ 处理学生 ${student.firstName} ${student.lastName} 时发生异常:`, error.message);
      console.error(`❌ 错误堆栈:`, error.stack);
    } finally {
      result.endTime = new Date();
      result.duration = result.endTime - result.startTime;
    }

    return result;
  }

  /**
   * 运行注册流程
   */
  async runRegistrationFlow(studentIndex) {
    return new Promise((resolve) => {
      console.log(`🔄 启动注册流程脚本...`);
      
      const env = { ...process.env, STUDENT_INDEX: studentIndex.toString() };
      const registrationProcess = spawn('node', ['src/examples/unr-complete-flow.js'], {
        cwd: process.cwd(),
        env: env,
        stdio: 'pipe'
      });

      let output = '';
      let errorOutput = '';

      registrationProcess.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        // 实时输出（可选）
        if (this.options.showRealTimeOutput) {
          process.stdout.write(`[注册] ${text}`);
        }
      });

      registrationProcess.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;
        if (this.options.showRealTimeOutput) {
          process.stderr.write(`[注册错误] ${text}`);
        }
      });

      registrationProcess.on('close', (code) => {
        const success = code === 0;
        console.log(`📊 注册流程结束，退出码: ${code}`);
        
        resolve({
          success: success,
          code: code,
          output: output,
          error: success ? null : (errorOutput || `进程退出码: ${code}`)
        });
      });

      registrationProcess.on('error', (error) => {
        console.error(`❌ 注册流程启动失败:`, error.message);
        resolve({
          success: false,
          code: -1,
          output: output,
          error: error.message
        });
      });
    });
  }

  /**
   * 运行注册流程（无缝模式）
   */
  async runRegistrationFlowSeamless(student, studentIndex) {
    console.log(`🔄 启动注册流程（无缝模式）...`);

    try {
      // 创建注册流程实例，使用共享的FormFiller
      this.registrationFlow = new (require('./unr-complete-flow').UNRCompleteFlow)({
        enableLogging: this.options.enableLogging,
        enableScreenshots: this.options.enableScreenshots,
        formFiller: this.sharedFormFiller // 使用共享的浏览器会话
      });

      // 执行注册流程
      const result = await this.registrationFlow.executeCompleteFlow(student);

      console.log(`📊 注册流程（无缝模式）完成，成功: ${result.success}`);

      return {
        success: result.success,
        error: result.success ? null : result.error,
        data: result
      };

    } catch (error) {
      console.error(`❌ 注册流程（无缝模式）失败:`, error.message);
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * 运行申请流程（无缝模式）
   */
  async runApplicationFlowSeamless(student, studentIndex) {
    console.log(`🔄 启动申请流程（无缝模式）...`);

    try {
      // 创建申请流程实例，使用共享的FormFiller
      this.applicationFlow = new (require('./unr-start-application').UNRApplicationStarter)({
        enableLogging: this.options.enableLogging,
        enableScreenshots: this.options.enableScreenshots,
        formFiller: this.sharedFormFiller, // 使用共享的浏览器会话
        continuousMode: true, // 标记为连续模式
        skipInitialNavigation: true // 跳过初始导航
      });

      // 执行申请流程
      const result = await this.applicationFlow.startApplication(student);

      console.log(`📊 申请流程（无缝模式）完成，成功: ${result.success}`);

      return {
        success: result.success,
        error: result.success ? null : result.error,
        data: result
      };

    } catch (error) {
      console.error(`❌ 申请流程（无缝模式）失败:`, error.message);
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * 运行申请流程（连续模式 - 不重新启动浏览器）
   */
  async runApplicationFlowContinuous(studentIndex) {
    return new Promise((resolve) => {
      console.log(`🔄 启动申请流程脚本（连续模式）...`);

      const env = {
        ...process.env,
        STUDENT_INDEX: studentIndex.toString(),
        CONTINUOUS_MODE: 'true', // 标记为连续模式
        SKIP_NAVIGATION: 'true'  // 跳过初始导航
      };

      const applicationProcess = spawn('node', ['src/examples/unr-start-application.js'], {
        cwd: process.cwd(),
        env: env,
        stdio: 'pipe'
      });

      let output = '';
      let errorOutput = '';

      applicationProcess.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        // 实时输出（可选）
        if (this.options.showRealTimeOutput) {
          process.stdout.write(`[申请-连续] ${text}`);
        }
      });

      applicationProcess.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;
        if (this.options.showRealTimeOutput) {
          process.stderr.write(`[申请错误-连续] ${text}`);
        }
      });

      applicationProcess.on('close', (code) => {
        const success = code === 0;
        console.log(`📊 申请流程（连续模式）结束，退出码: ${code}`);

        resolve({
          success: success,
          code: code,
          output: output,
          error: success ? null : (errorOutput || `进程退出码: ${code}`)
        });
      });

      applicationProcess.on('error', (error) => {
        console.error(`❌ 申请流程（连续模式）启动失败:`, error.message);
        resolve({
          success: false,
          code: -1,
          output: output,
          error: error.message
        });
      });
    });
  }

  /**
   * 运行申请流程（独立模式 - 重新启动浏览器）
   */
  async runApplicationFlow(studentIndex) {
    return new Promise((resolve) => {
      console.log(`🔄 启动申请流程脚本...`);
      
      const env = { ...process.env, STUDENT_INDEX: studentIndex.toString() };
      const applicationProcess = spawn('node', ['src/examples/unr-start-application.js'], {
        cwd: process.cwd(),
        env: env,
        stdio: 'pipe'
      });

      let output = '';
      let errorOutput = '';

      applicationProcess.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        // 实时输出（可选）
        if (this.options.showRealTimeOutput) {
          process.stdout.write(`[申请] ${text}`);
        }
      });

      applicationProcess.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;
        if (this.options.showRealTimeOutput) {
          process.stderr.write(`[申请错误] ${text}`);
        }
      });

      applicationProcess.on('close', (code) => {
        const success = code === 0;
        console.log(`📊 申请流程结束，退出码: ${code}`);
        
        resolve({
          success: success,
          code: code,
          output: output,
          error: success ? null : (errorOutput || `进程退出码: ${code}`)
        });
      });

      applicationProcess.on('error', (error) => {
        console.error(`❌ 申请流程启动失败:`, error.message);
        resolve({
          success: false,
          code: -1,
          output: output,
          error: error.message
        });
      });
    });
  }

  /**
   * 生成最终报告
   */
  generateFinalReport() {
    console.log('\n🎉 完整自动化流程报告');
    console.log('========================');
    console.log(`📊 总处理学生数: ${this.processedCount}`);
    console.log(`✅ 成功完成数: ${this.successCount}`);
    console.log(`❌ 失败数: ${this.failureCount}`);
    console.log(`📈 成功率: ${((this.successCount / this.processedCount) * 100).toFixed(1)}%`);
    
    console.log('\n📋 详细结果:');
    this.results.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      const duration = result.duration ? `${Math.round(result.duration / 1000)}秒` : 'N/A';
      
      console.log(`${status} 学生 #${result.studentIndex + 1}: ${result.student.firstName} ${result.student.lastName}`);
      console.log(`   📧 邮箱: ${result.student.email}`);
      console.log(`   📋 注册: ${result.registrationSuccess ? '✅' : '❌'}`);
      console.log(`   📋 申请: ${result.applicationSuccess ? '✅' : '❌'}`);
      console.log(`   ⏱️  耗时: ${duration}`);
      
      if (result.errors.length > 0) {
        console.log(`   ❌ 错误: ${result.errors.join(', ')}`);
      }
      console.log('');
    });
  }
}

// 主函数
async function main() {
  try {
    // 从环境变量或命令行参数获取配置
    const startIndex = parseInt(process.env.START_INDEX || process.argv[2] || '0');
    const endIndex = process.env.END_INDEX ? parseInt(process.env.END_INDEX) : (process.argv[3] ? parseInt(process.argv[3]) : null);
    
    const automation = new UNRFullAutomation({
      startIndex: startIndex,
      endIndex: endIndex,
      showRealTimeOutput: process.env.SHOW_REAL_TIME === 'true',
      delayBetweenStudents: parseInt(process.env.DELAY_BETWEEN_STUDENTS || '5000')
    });
    
    await automation.start();
    
    console.log('\n🎉 完整自动化流程执行完成！');
    process.exit(0);
    
  } catch (error) {
    console.error('\n❌ 完整自动化流程执行失败:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main();
}

module.exports = { UNRFullAutomation };
