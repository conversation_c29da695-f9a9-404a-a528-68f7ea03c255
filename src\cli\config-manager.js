/**
 * 配置管理模块
 * 处理配置文件的保存、加载和验证
 */

const fs = require('fs').promises;
const path = require('path');

class ConfigManager {
  constructor() {
    this.configDir = path.join(__dirname, '../../config');
    this.configFile = path.join(this.configDir, 'terminal-config.json');
    this.defaultConfig = {
      dataFile: 'test.xlsx',
      startIndex: 0,
      endIndex: null,
      enableScreenshots: true,
      enableLogging: true,
      delayBetweenStudents: 3000,
      maxRetries: 2,
      randomizeBrowser: true,
      autoSaveConfig: true,
      browserConfig: {
        randomizeUserAgent: true,
        randomizeViewport: true,
        randomizeTimezone: true,
        randomizeLanguage: true
      },
      recentFiles: [],
      lastUsedRange: { start: 0, end: null }
    };
  }

  /**
   * 确保配置目录存在
   */
  async ensureConfigDir() {
    try {
      await fs.access(this.configDir);
    } catch (error) {
      await fs.mkdir(this.configDir, { recursive: true });
    }
  }

  /**
   * 保存配置
   */
  async save(config) {
    try {
      await this.ensureConfigDir();
      
      const configToSave = {
        ...this.defaultConfig,
        ...config,
        lastSaved: new Date().toISOString()
      };
      
      await fs.writeFile(this.configFile, JSON.stringify(configToSave, null, 2));
      return true;
    } catch (error) {
      console.error('保存配置失败:', error.message);
      return false;
    }
  }

  /**
   * 加载配置
   */
  async load() {
    try {
      const configData = await fs.readFile(this.configFile, 'utf8');
      const config = JSON.parse(configData);
      
      // 合并默认配置和加载的配置
      return {
        ...this.defaultConfig,
        ...config
      };
    } catch (error) {
      // 如果文件不存在或解析失败，返回默认配置
      return this.defaultConfig;
    }
  }

  /**
   * 重置为默认配置
   */
  async reset() {
    try {
      await this.save(this.defaultConfig);
      return true;
    } catch (error) {
      console.error('重置配置失败:', error.message);
      return false;
    }
  }

  /**
   * 验证配置
   */
  validateConfig(config) {
    const errors = [];

    // 验证数据文件
    if (!config.dataFile || typeof config.dataFile !== 'string') {
      errors.push('数据文件路径无效');
    }

    // 验证索引范围
    if (typeof config.startIndex !== 'number' || config.startIndex < 0) {
      errors.push('起始索引无效');
    }

    if (config.endIndex !== null && (typeof config.endIndex !== 'number' || config.endIndex <= config.startIndex)) {
      errors.push('结束索引无效');
    }

    // 验证延迟时间
    if (typeof config.delayBetweenStudents !== 'number' || config.delayBetweenStudents < 0) {
      errors.push('延迟时间无效');
    }

    // 验证重试次数
    if (typeof config.maxRetries !== 'number' || config.maxRetries < 0) {
      errors.push('重试次数无效');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 添加最近使用的文件
   */
  async addRecentFile(filePath) {
    try {
      const config = await this.load();
      
      // 移除重复项
      config.recentFiles = config.recentFiles.filter(f => f !== filePath);
      
      // 添加到开头
      config.recentFiles.unshift(filePath);
      
      // 限制最多10个
      config.recentFiles = config.recentFiles.slice(0, 10);
      
      await this.save(config);
      return true;
    } catch (error) {
      console.error('添加最近文件失败:', error.message);
      return false;
    }
  }

  /**
   * 获取最近使用的文件
   */
  async getRecentFiles() {
    try {
      const config = await this.load();
      return config.recentFiles || [];
    } catch (error) {
      return [];
    }
  }

  /**
   * 保存最后使用的范围
   */
  async saveLastUsedRange(start, end) {
    try {
      const config = await this.load();
      config.lastUsedRange = { start, end };
      await this.save(config);
      return true;
    } catch (error) {
      console.error('保存最后使用范围失败:', error.message);
      return false;
    }
  }

  /**
   * 获取最后使用的范围
   */
  async getLastUsedRange() {
    try {
      const config = await this.load();
      return config.lastUsedRange || { start: 0, end: null };
    } catch (error) {
      return { start: 0, end: null };
    }
  }

  /**
   * 导出配置
   */
  async exportConfig(exportPath) {
    try {
      const config = await this.load();
      await fs.writeFile(exportPath, JSON.stringify(config, null, 2));
      return true;
    } catch (error) {
      console.error('导出配置失败:', error.message);
      return false;
    }
  }

  /**
   * 导入配置
   */
  async importConfig(importPath) {
    try {
      const configData = await fs.readFile(importPath, 'utf8');
      const config = JSON.parse(configData);
      
      // 验证配置
      const validation = this.validateConfig(config);
      if (!validation.isValid) {
        throw new Error(`配置验证失败: ${validation.errors.join(', ')}`);
      }
      
      await this.save(config);
      return true;
    } catch (error) {
      console.error('导入配置失败:', error.message);
      return false;
    }
  }

  /**
   * 获取配置文件路径
   */
  getConfigPath() {
    return this.configFile;
  }

  /**
   * 检查配置文件是否存在
   */
  async exists() {
    try {
      await fs.access(this.configFile);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取配置文件信息
   */
  async getConfigInfo() {
    try {
      const stats = await fs.stat(this.configFile);
      const config = await this.load();
      
      return {
        path: this.configFile,
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime,
        lastSaved: config.lastSaved || null,
        isValid: this.validateConfig(config).isValid
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * 创建配置备份
   */
  async createBackup() {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = path.join(this.configDir, `terminal-config-backup-${timestamp}.json`);
      
      const config = await this.load();
      await fs.writeFile(backupPath, JSON.stringify(config, null, 2));
      
      return backupPath;
    } catch (error) {
      console.error('创建配置备份失败:', error.message);
      return null;
    }
  }

  /**
   * 清理旧备份文件
   */
  async cleanupBackups(keepCount = 5) {
    try {
      const files = await fs.readdir(this.configDir);
      const backupFiles = files
        .filter(file => file.startsWith('terminal-config-backup-'))
        .map(file => ({
          name: file,
          path: path.join(this.configDir, file)
        }));

      if (backupFiles.length <= keepCount) {
        return 0;
      }

      // 按修改时间排序
      const filesWithStats = await Promise.all(
        backupFiles.map(async file => {
          const stats = await fs.stat(file.path);
          return { ...file, mtime: stats.mtime };
        })
      );

      filesWithStats.sort((a, b) => b.mtime - a.mtime);

      // 删除多余的备份文件
      const filesToDelete = filesWithStats.slice(keepCount);
      let deletedCount = 0;

      for (const file of filesToDelete) {
        try {
          await fs.unlink(file.path);
          deletedCount++;
        } catch (error) {
          console.error(`删除备份文件失败: ${file.name}`, error.message);
        }
      }

      return deletedCount;
    } catch (error) {
      console.error('清理备份文件失败:', error.message);
      return 0;
    }
  }
}

module.exports = { ConfigManager };
