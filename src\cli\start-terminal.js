#!/usr/bin/env node

/**
 * UNR自动化流程交互式终端启动器
 */

const { InteractiveTerminal } = require('./interactive-terminal');

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 处理Ctrl+C
process.on('SIGINT', () => {
  console.log('\n\n👋 程序已退出');
  process.exit(0);
});

// 启动交互式终端
async function main() {
  try {
    const terminal = new InteractiveTerminal();
    await terminal.start();
  } catch (error) {
    console.error('启动失败:', error.message);
    process.exit(1);
  }
}

main();
