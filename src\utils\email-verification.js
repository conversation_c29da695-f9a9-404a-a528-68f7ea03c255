/**
 * 邮箱验证码获取模块
 * 用于从QQ邮箱获取UNR系统发送的验证码
 */

const Imap = require('imap');
const { simpleParser } = require('mailparser');

/**
 * 邮箱验证码获取器
 */
class EmailVerificationRetriever {
  constructor(options = {}) {
    this.config = {
      imap: {
        user: options.user || '<EMAIL>',
        password: options.password || 'pnsbpvwlrvpybiei',
        host: options.host || 'imap.qq.com',
        port: options.port || 993,
        tls: true,
        tlsOptions: {
          rejectUnauthorized: false
        }
      },
      searchCriteria: options.searchCriteria || ['UNSEEN', ['FROM', 'unr.edu']],
      maxWaitTime: options.maxWaitTime || 300000, // 5分钟 (300秒)
      checkInterval: options.checkInterval || 10000, // 10秒检查一次
      recentEmailMinutes: options.recentEmailMinutes || 15, // 只检查最近15分钟的邮件
      maxRetries: options.maxRetries || 999, // 最大重试999次
      enableLogging: options.enableLogging !== false
    };
    
    this.imap = null;
  }

  /**
   * 获取验证码
   * @param {string} studentEmail - 学生的实际邮箱地址（用于匹配转发邮件内容）
   * @param {number} maxRetries - 最大重试次数（默认使用配置值）
   */
  async getVerificationCode(studentEmail, maxRetries = null) {
    maxRetries = maxRetries || this.config.maxRetries;
    this.log(`🔍 开始获取验证码`);
    this.log(`📧 学生邮箱: ${studentEmail}`);
    this.log(`📮 IMAP连接邮箱: ${this.config.imap.user}`);
    this.log(`💡 逻辑: UNR发送到学生邮箱 -> 转发到QQ邮箱 -> 从QQ邮箱获取`);

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        this.log(`📧 第${attempt}次尝试获取验证码...`);

        const code = await this.retrieveCodeFromEmail(studentEmail);
        if (code) {
          this.log(`✅ 成功获取验证码: ${code}`);
          return code;
        }

        if (attempt < maxRetries) {
          this.log(`⏳ 第${attempt}次未找到验证码，等待${this.config.checkInterval/1000}秒后重试...`);
          await this.sleep(this.config.checkInterval);
        }

      } catch (error) {
        this.log(`❌ 第${attempt}次获取验证码失败: ${error.message}`);
        if (attempt === maxRetries) {
          throw error;
        }
        await this.sleep(2000);
      }
    }

    this.log('⚠️  达到最大重试次数，未能获取到验证码，将使用备用策略');
    return null; // 返回null而不是抛出错误，让调用者处理备用策略
  }

  /**
   * 从邮箱检索验证码
   */
  async retrieveCodeFromEmail(studentEmail) {
    return new Promise((resolve, reject) => {
      this.imap = new Imap(this.config.imap);
      let resolved = false;

      const cleanup = () => {
        if (this.imap && this.imap.state !== 'disconnected') {
          this.imap.end();
        }
      };

      const handleError = (error) => {
        if (!resolved) {
          resolved = true;
          cleanup();
          reject(error);
        }
      };

      const handleSuccess = (code) => {
        if (!resolved) {
          resolved = true;
          cleanup();
          resolve(code);
        }
      };

      // 设置超时
      const timeout = setTimeout(() => {
        handleError(new Error('邮箱连接超时'));
      }, this.config.maxWaitTime);

      this.imap.once('ready', () => {
        this.log('📬 邮箱连接成功');
        
        this.imap.openBox('INBOX', false, (err, box) => {
          if (err) {
            clearTimeout(timeout);
            return handleError(err);
          }

          this.log(`📮 邮箱打开成功，共有 ${box.messages.total} 封邮件`);
          
          // 搜索最近的邮件 - 放宽搜索条件
          const recentTime = new Date(Date.now() - this.config.recentEmailMinutes * 60 * 1000);
          const searchCriteria = [
            ['SINCE', recentTime], // 最近N分钟的邮件
            'UNSEEN' // 包含未读邮件
          ];

          this.log(`🕐 搜索时间范围: ${recentTime.toLocaleString()} 至现在`);
          this.log(`🔍 搜索条件: 最近${this.config.recentEmailMinutes}分钟的邮件（包含未读）`);

          this.imap.search(searchCriteria, (err, results) => {
            if (err) {
              clearTimeout(timeout);
              return handleError(err);
            }

            if (!results || results.length === 0) {
              this.log('📭 未找到最近24小时的邮件');
              clearTimeout(timeout);
              return handleSuccess(null);
            }

            this.log(`📬 找到 ${results.length} 封最近的邮件，开始筛选相关邮件`);

            // 获取最新的邮件
            const fetch = this.imap.fetch(results.slice(-5), {
              bodies: '',
              markSeen: false
            });

            const emails = [];

            fetch.on('message', (msg, seqno) => {
              this.log(`📧 处理邮件 ${seqno}`);
              
              msg.on('body', (stream, info) => {
                let buffer = '';
                stream.on('data', (chunk) => {
                  buffer += chunk.toString('utf8');
                });
                
                stream.once('end', () => {
                  simpleParser(buffer, (err, parsed) => {
                    if (err) {
                      this.log(`⚠️  解析邮件失败: ${err.message}`);
                      return;
                    }

                    emails.push({
                      seqno,
                      subject: parsed.subject,
                      from: parsed.from?.text,
                      date: parsed.date,
                      text: parsed.text,
                      html: parsed.html
                    });
                  });
                });
              });
            });

            fetch.once('error', (err) => {
              clearTimeout(timeout);
              handleError(err);
            });

            fetch.once('end', () => {
              clearTimeout(timeout);
              
              // 从邮件中提取验证码
              const code = this.extractVerificationCode(emails, studentEmail);
              handleSuccess(code);
            });
          });
        });
      });

      this.imap.once('error', (err) => {
        clearTimeout(timeout);
        handleError(err);
      });

      this.imap.connect();
    });
  }

  /**
   * 从邮件内容中提取验证码
   * @param {Array} emails - 邮件列表
   * @param {string} studentEmail - 学生邮箱地址（用于匹配转发邮件内容）
   */
  extractVerificationCode(emails, studentEmail) {
    this.log(`🔍 分析 ${emails.length} 封邮件以查找验证码`);
    this.log(`🎯 目标学生邮箱: ${studentEmail}`);

    for (const email of emails.reverse()) { // 从最新的邮件开始
      this.log(`📧 检查邮件: ${email.subject} (${email.from})`);

      // 检查邮件是否与学生邮箱相关或来自UNR
      const emailContent = (email.text || '') + (email.html || '');
      const emailSubject = (email.subject || '').toLowerCase();
      const emailFrom = (email.from || '').toLowerCase();

      // UNR邮件识别条件（支持中英文）：
      // 优先级1: 来自UNR域名的邮件
      const isFromUNR = emailFrom.includes('unr.edu') ||
                       emailFrom.includes('nevada') ||
                       emailFrom.includes('<EMAIL>');

      // 优先级2: 包含UNR特征内容
      const hasUNRContent = emailContent.toLowerCase().includes('university of nevada') ||
                           emailContent.includes('内华达大学里诺分校') ||
                           emailContent.toLowerCase().includes('nevada, reno');

      // 优先级3: 包含验证相关关键词
      const hasVerificationKeywords =
        emailSubject.toLowerCase().includes('verification') ||
        emailSubject.toLowerCase().includes('pin') ||
        emailSubject.toLowerCase().includes('registration') ||
        emailSubject.toLowerCase().includes('account') ||
        emailContent.toLowerCase().includes('temporary pin') ||
        emailContent.includes('临时 PIN') ||
        emailContent.includes('临时PIN') ||
        emailContent.includes('验证申请') ||
        emailContent.includes('激活您的帐户') ||
        emailContent.includes('输入以下临时');

      // 优先级4: 邮件内容包含学生邮箱地址（转发邮件）
      const containsStudentEmail = emailContent.toLowerCase().includes(studentEmail.toLowerCase());

      // 简化识别逻辑：放宽识别条件
      const isRelevant = isFromUNR ||
                        hasUNRContent ||
                        hasVerificationKeywords ||
                        containsStudentEmail ||
                        emailContent.includes('PIN') ||
                        emailContent.includes('pin') ||
                        /\d{9}/.test(emailContent); // 包含9位数字的邮件

      this.log(`🔍 邮件相关性检查:`);
      this.log(`   来自UNR: ${isFromUNR}`);
      this.log(`   包含UNR内容: ${hasUNRContent}`);
      this.log(`   包含验证关键词: ${hasVerificationKeywords}`);
      this.log(`   包含学生邮箱: ${containsStudentEmail}`);
      this.log(`   包含PIN: ${emailContent.includes('PIN') || emailContent.includes('pin')}`);
      this.log(`   包含9位数字: ${/\d{9}/.test(emailContent)}`);

      if (!isRelevant) {
        this.log(`⏭️  邮件不相关，跳过`);
        this.log(`📄 邮件内容预览: ${emailContent.substring(0, 300)}...`);
        continue;
      }

      this.log(`✅ 找到相关邮件，开始提取验证码`);

      // UNR PIN码提取模式 - 优先9位PIN码
      const patterns = [
        // 第一优先级：9位PIN码的特定格式
        /输入以下临时\s*PIN[：:\s]*(\d{9})/i,
        /输入以下临时\s*pin[：:\s]*(\d{9})/i,
        /临时\s*PIN[：:\s]*(\d{9})/i,
        /临时\s*pin[：:\s]*(\d{9})/i,
        /temporary\s+pin[:\s]*(\d{9})/i,
        /your\s+temporary\s+pin\s+is[:\s]*(\d{9})/i,
        /pin[:\s]*(\d{9})/i,

        // 第二优先级：9位数字模式（UNR标准格式）
        /\b(\d{9})\b/g,

        // 第三优先级：其他长度（仅在没有9位PIN时使用）
        /\b(\d{8})\b/g,
        /\b(\d{7})\b/g,
        /\b(\d{6})\b/g
      ];

      // 收集所有找到的PIN码及其优先级
      const foundPINs = [];

      for (const pattern of patterns) {
        const matches = emailContent.match(pattern);
        if (matches) {
          const code = matches[1] || matches[0];
          if (code && this.validatePIN(code)) {
            const priority = this.getPINPriority(code);
            foundPINs.push({
              code: code,
              pattern: pattern.source,
              priority: priority
            });
            this.log(`🔍 找到PIN码: ${code} (长度: ${code.length}, 优先级: ${priority})`);
          }
        }
      }

      // 如果找到多个PIN码，选择优先级最高的（9位PIN优先）
      if (foundPINs.length > 0) {
        foundPINs.sort((a, b) => b.priority - a.priority);
        const bestPIN = foundPINs[0];
        this.log(`✅ 选择最佳PIN码: ${bestPIN.code} (模式: ${bestPIN.pattern})`);
        this.log(`✅ PIN码验证通过: 长度=${bestPIN.code.length}, 优先级=${bestPIN.priority}`);
        return bestPIN.code;
      }

      this.log(`📄 邮件内容预览: ${emailContent.substring(0, 200)}...`);
    }

    this.log('❌ 未在邮件中找到验证码');
    return null;
  }

  /**
   * 验证PIN码格式
   * @param {string} pin - 待验证的PIN码
   * @returns {boolean} - 是否为有效的PIN码
   */
  validatePIN(pin) {
    if (!pin || typeof pin !== 'string') {
      return false;
    }

    // 移除空格和特殊字符
    const cleanPin = pin.trim().replace(/\s/g, '');

    // 检查是否为纯数字
    if (!/^\d+$/.test(cleanPin)) {
      return false;
    }

    // UNR PIN码长度验证（严格优先9位）
    const length = cleanPin.length;
    if (length === 9) {
      this.log(`✅ 标准9位PIN码: ${cleanPin}`);
      return true;
    } else if (length >= 6 && length <= 8) {
      this.log(`⚠️  非标准长度PIN码: ${cleanPin} (长度: ${length})`);
      return true;
    }

    this.log(`❌ PIN码长度不符合要求: ${cleanPin} (长度: ${length})`);
    return false;
  }

  /**
   * 获取PIN码优先级分数（9位PIN码优先级最高）
   */
  getPINPriority(pin) {
    if (!pin) return 0;
    const length = pin.length;
    if (length === 9) return 100; // 最高优先级
    if (length === 8) return 80;
    if (length === 7) return 60;
    if (length === 6) return 40;
    return 20; // 其他长度最低优先级
  }

  /**
   * 等待指定时间
   */
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 日志输出
   */
  log(message) {
    if (this.config.enableLogging) {
      console.log(`[EmailVerification] ${message}`);
    }
  }

  /**
   * 测试邮箱连接
   */
  async testConnection() {
    this.log('🧪 测试邮箱连接...');
    
    return new Promise((resolve, reject) => {
      const imap = new Imap(this.config.imap);
      
      const timeout = setTimeout(() => {
        imap.end();
        reject(new Error('连接超时'));
      }, 10000);

      imap.once('ready', () => {
        clearTimeout(timeout);
        this.log('✅ 邮箱连接测试成功');
        imap.end();
        resolve(true);
      });

      imap.once('error', (err) => {
        clearTimeout(timeout);
        this.log(`❌ 邮箱连接测试失败: ${err.message}`);
        reject(err);
      });

      imap.connect();
    });
  }

  /**
   * 获取最近的邮件列表（用于调试）
   */
  async getRecentEmails(limit = 5) {
    this.log('📬 获取最近的邮件列表...');
    
    return new Promise((resolve, reject) => {
      const imap = new Imap(this.config.imap);
      
      imap.once('ready', () => {
        imap.openBox('INBOX', true, (err, box) => {
          if (err) return reject(err);

          const total = box.messages.total;
          const start = Math.max(1, total - limit + 1);
          
          const fetch = imap.fetch(`${start}:${total}`, {
            bodies: 'HEADER.FIELDS (FROM TO SUBJECT DATE)',
            struct: true
          });

          const emails = [];

          fetch.on('message', (msg, seqno) => {
            msg.on('body', (stream, info) => {
              let buffer = '';
              stream.on('data', chunk => buffer += chunk);
              stream.once('end', () => {
                const header = Imap.parseHeader(buffer);
                emails.push({
                  seqno,
                  from: header.from?.[0],
                  to: header.to?.[0],
                  subject: header.subject?.[0],
                  date: header.date?.[0]
                });
              });
            });
          });

          fetch.once('end', () => {
            imap.end();
            resolve(emails.reverse());
          });

          fetch.once('error', reject);
        });
      });

      imap.once('error', reject);
      imap.connect();
    });
  }
}

module.exports = { EmailVerificationRetriever };
