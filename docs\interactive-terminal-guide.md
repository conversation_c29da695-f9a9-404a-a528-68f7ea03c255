# UNR自动化流程交互式终端使用指南

## 概述

UNR自动化流程交互式终端是一个功能完整的命令行界面工具，提供了直观的菜单系统来管理UNR（内华达大学里诺分校）的自动化注册和申请流程。

## 功能特性

### 🎯 核心功能
- **交互式菜单系统** - 直观的命令行界面
- **浏览器参数随机化** - 避免检测的指纹随机化
- **文件名读取支持** - 灵活的Excel数据文件管理
- **参数配置界面** - 完整的设置管理
- **实时监控功能** - 进度跟踪和状态显示

### 🛠️ 主要模块
1. **交互式终端** (`src/cli/interactive-terminal.js`)
2. **浏览器随机化** (`src/cli/browser-randomizer.js`)
3. **配置管理** (`src/cli/config-manager.js`)

## 安装和启动

### 安装依赖
```bash
npm install chalk@4.1.2 figlet@1.6.0
```

### 启动终端
```bash
# 方法1：使用npm script
npm run terminal

# 方法2：使用别名
npm run cli

# 方法3：直接运行
node src/cli/start-terminal.js
```

## 主菜单功能

### 1. 🚀 启动浏览器
- 启动Chrome浏览器用于自动化
- 支持浏览器随机化配置
- 显示启动状态和连接信息

### 2. 📝 运行注册流程
- 为指定学生执行UNR账户注册
- 交互式选择学生编号
- 实时显示注册进度

### 3. 📋 运行申请流程
- 为指定学生执行UNR申请提交
- 支持已注册学生的申请流程
- 自动处理申请表单填写

### 4. 🔄 完整自动化流程
- 批量执行注册+申请的完整流程
- 支持学生范围选择
- 提供详细的执行报告

### 5. ⚙️ 配置设置
- **数据文件配置** - 设置Excel文件路径
- **功能开关** - 截图、日志等功能控制
- **性能参数** - 延迟时间、重试次数设置
- **自动保存** - 配置自动保存功能

### 6. 📊 查看数据文件
- 显示Excel文件路径和状态
- 预览学生数据（前5个）
- 显示学生总数统计

### 7. 🎲 浏览器随机化设置
- **生成随机配置** - 创建新的浏览器指纹
- **查看当前配置** - 显示已保存的配置
- **保存/加载配置** - 配置文件管理
- **测试配置** - 验证配置有效性

### 8. 📈 查看运行报告
- 显示历史执行记录
- 成功率统计分析
- 错误日志查看

### 9. ❓ 帮助
- 详细的使用指南
- 功能说明和注意事项
- 数据文件格式要求

## 浏览器随机化功能

### 随机化参数
- **User-Agent字符串** - 不同浏览器和版本
- **屏幕分辨率** - 常见分辨率随机选择
- **时区设置** - 全球主要时区
- **语言偏好** - 多种语言组合
- **硬件信息** - CPU核心数、内存大小
- **WebGL指纹** - 显卡供应商和渲染器
- **Canvas指纹** - 画布噪声注入
- **音频指纹** - 音频上下文随机化

### 配置管理
```javascript
// 生成随机配置
const config = browserRandomizer.generateRandomConfig();

// 保存配置
browserRandomizer.saveConfig(config, 'my-config.json');

// 加载配置
const savedConfig = browserRandomizer.loadConfig('my-config.json');
```

## 配置文件管理

### 配置文件位置
- 主配置：`config/terminal-config.json`
- 浏览器配置：`config/browser-config.json`
- 备份文件：`config/terminal-config-backup-*.json`

### 配置项说明
```json
{
  "dataFile": "test.xlsx",           // Excel数据文件路径
  "startIndex": 0,                   // 起始学生索引
  "endIndex": null,                  // 结束学生索引
  "enableScreenshots": true,         // 启用截图
  "enableLogging": true,             // 启用日志
  "delayBetweenStudents": 3000,     // 学生间延迟(ms)
  "maxRetries": 2,                   // 最大重试次数
  "randomizeBrowser": true,          // 启用浏览器随机化
  "autoSaveConfig": true             // 自动保存配置
}
```

## 数据文件格式

### Excel文件要求
- 文件格式：`.xlsx`
- 工作表名：`个人信息`
- 编码：UTF-8

### 必需列
- `邮箱` - 学生邮箱地址
- `名` - 学生名字
- `姓` - 学生姓氏
- `生日` - 出生日期
- `密码` - 账户密码
- `完整地址` - 完整地址信息
- `电话` - 联系电话
- `社会安全号` - SSN

## 使用流程

### 基本流程
1. 启动交互式终端：`npm run terminal`
2. 配置数据文件路径（如需要）
3. 启动浏览器
4. 选择执行模式：
   - 单个学生注册/申请
   - 批量完整自动化流程
5. 监控执行进度
6. 查看执行报告

### 最佳实践
1. **首次使用**：
   - 配置数据文件路径
   - 启用浏览器随机化
   - 设置合适的延迟时间

2. **批量处理**：
   - 使用完整自动化流程
   - 设置合理的学生范围
   - 启用截图和日志记录

3. **故障排除**：
   - 查看日志文件
   - 检查数据文件格式
   - 验证浏览器连接状态

## 故障排除

### 常见问题
1. **浏览器连接失败**
   - 确保浏览器已启动
   - 检查端口9222是否被占用
   - 重启浏览器服务

2. **数据文件读取失败**
   - 检查文件路径是否正确
   - 验证Excel文件格式
   - 确认工作表名称

3. **配置保存失败**
   - 检查config目录权限
   - 确保磁盘空间充足
   - 验证JSON格式正确性

### 日志文件位置
- 应用日志：`logs/`目录
- 截图文件：`screenshots/`目录
- 配置备份：`config/`目录

## 高级功能

### 自定义浏览器配置
```javascript
// 创建自定义配置
const customConfig = {
  userAgent: 'Custom User Agent',
  viewport: { width: 1920, height: 1080 },
  timezone: 'America/New_York',
  language: 'en-US'
};

// 验证配置
browserRandomizer.validateConfig(customConfig);
```

### 批量配置管理
```javascript
// 导出配置
await configManager.exportConfig('backup.json');

// 导入配置
await configManager.importConfig('backup.json');

// 创建备份
const backupPath = await configManager.createBackup();
```

## 技术支持

如遇到问题，请检查：
1. Node.js版本（建议16+）
2. 依赖包安装状态
3. 浏览器版本兼容性
4. 系统权限设置

---

**版本**: 1.0.0  
**更新日期**: 2025-07-17  
**作者**: AI Assistant
