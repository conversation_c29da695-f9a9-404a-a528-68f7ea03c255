/**
 * UNR完整注册流程处理器
 * 包含邮箱验证码获取和两步注册流程
 */

const { FormFiller } = require('../form-filler');
const { ExcelReader } = require('../utils/excel-reader');
const { EmailVerificationRetriever } = require('../utils/email-verification');
const path = require('path');

/**
 * UNR完整注册流程管理器
 */
class UNRCompleteFlow {
  constructor(options = {}) {
    this.options = {
      enableLogging: options.enableLogging !== false,
      enableScreenshots: options.enableScreenshots !== false,
      screenshotPath: options.screenshotPath || './screenshots',
      waitBetweenSteps: options.waitBetweenSteps || 2000,
      emailRetriever: options.emailRetriever || new EmailVerificationRetriever({
        enableLogging: options.enableLogging !== false,
        maxRetries: 999,
        checkInterval: 10000,
        recentEmailMinutes: 5
      }),
      fallbackCodes: options.fallbackCodes || ['123456', '000000', '111111', '888888'], // 备用验证码
      ...options
    };
    
    this.formFiller = null;
    this.studentData = null;
    this.verificationCode = null;
  }

  /**
   * 初始化
   */
  async initialize() {
    this.formFiller = new FormFiller({
      enableLogging: this.options.enableLogging,
      enableScreenshots: this.options.enableScreenshots,
      screenshotPath: this.options.screenshotPath,
      stopOnError: false
    });

    await this.formFiller.initialize();
    this.log('🚀 UNR完整注册流程初始化完成');
  }

  /**
   * 执行完整注册流程
   */
  async executeCompleteFlow(studentData) {
    this.studentData = studentData;
    this.log(`\n📝 开始完整注册流程: ${studentData.firstName} ${studentData.lastName}`);
    this.log(`📧 邮箱: ${studentData.email}`);

    try {
      // 步骤1: 填写注册表单
      await this.step1_FillRegistrationForm();
      
      // 步骤2: 获取验证码并完成登录
      await this.step2_HandleVerificationAndLogin();
      
      // 步骤3: 处理密码设置页面
      await this.step3_HandlePasswordSetup();

      // 步骤4: 处理后续页面
      await this.step4_HandleNextSteps();
      
      this.log(`✅ 学生 ${studentData.firstName} ${studentData.lastName} 完整注册流程完成`);
      return {
        success: true,
        studentData: this.studentData,
        verificationCode: this.verificationCode
      };

    } catch (error) {
      this.log(`❌ 学生 ${studentData.firstName} ${studentData.lastName} 注册失败: ${error.message}`);
      return {
        success: false,
        studentData: this.studentData,
        error: error.message
      };
    }
  }

  /**
   * 步骤1: 填写注册表单
   */
  async step1_FillRegistrationForm() {
    this.log('\n📋 步骤1: 填写注册表单');
    
    // 导航到注册页面
    await this.formFiller.currentPage.goto('https://admissions.unr.edu/account/register', {
      waitUntil: 'domcontentloaded',
      timeout: 30000
    });

    await this.formFiller.currentPage.waitForSelector('#register_form', { timeout: 30000 });
    this.log('✅ 注册页面加载完成');

    // 截图
    if (this.options.enableScreenshots) {
      await this.takeScreenshot('step1-page-loaded');
    }

    // 填写表单字段
    this.log('📝 填写注册表单字段...');
    
    // 清空并填写邮箱
    await this.formFiller.currentPage.click('#email', { clickCount: 3 });
    await this.formFiller.currentPage.type('#email', this.studentData.email, { delay: 50 });
    
    // 清空并填写名字
    await this.formFiller.currentPage.click('#first', { clickCount: 3 });
    await this.formFiller.currentPage.type('#first', this.studentData.firstName, { delay: 50 });
    
    // 清空并填写姓氏
    await this.formFiller.currentPage.click('#last', { clickCount: 3 });
    await this.formFiller.currentPage.type('#last', this.studentData.lastName, { delay: 50 });

    // 填写生日
    if (this.studentData.birthMonth) {
      await this.formFiller.currentPage.select('#birthdate_m', this.studentData.birthMonth);
    }
    if (this.studentData.birthDay) {
      await this.formFiller.currentPage.select('#birthdate_d', this.studentData.birthDay);
    }
    if (this.studentData.birthYear) {
      await this.formFiller.currentPage.select('#birthdate_y', this.studentData.birthYear);
    }

    await new Promise(resolve => setTimeout(resolve, 1000));

    // 截图
    if (this.options.enableScreenshots) {
      await this.takeScreenshot('step1-form-filled');
    }

    // 验证填写结果
    await this.verifyStep1Form();

    // 点击Continue按钮
    this.log('🔘 点击Continue按钮...');
    const continueButton = await this.formFiller.currentPage.$('button.default');
    if (!continueButton) {
      throw new Error('找不到Continue按钮');
    }

    await continueButton.click();
    this.log('✅ Continue按钮已点击');

    // 等待页面跳转
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    try {
      await this.formFiller.currentPage.waitForNavigation({ timeout: 10000 });
    } catch (error) {
      this.log('⚠️  页面导航超时，但继续处理');
    }

    this.log('✅ 步骤1完成');
  }

  /**
   * 步骤2: 处理验证码和登录
   */
  async step2_HandleVerificationAndLogin() {
    this.log('\n📋 步骤2: 处理验证码和登录');

    // 截图当前页面
    if (this.options.enableScreenshots) {
      await this.takeScreenshot('step2-login-page');
    }

    // 获取当前页面信息
    const currentUrl = this.formFiller.currentPage.url();
    this.log(`📍 当前页面: ${currentUrl}`);

    // 检查是否是登录页面
    const isLoginPage = currentUrl.includes('/login') || 
                       await this.formFiller.currentPage.$('#password') !== null;

    if (!isLoginPage) {
      throw new Error('未跳转到预期的登录页面');
    }

    // 等待一段时间让邮件发送
    this.log('⏳ 等待UNR系统发送验证邮件...');
    await new Promise(resolve => setTimeout(resolve, 10000));

    // 获取验证码
    this.log('📧 开始获取邮箱验证码...');
    this.verificationCode = await this.options.emailRetriever.getVerificationCode(
      this.studentData.email
    );

    if (!this.verificationCode) {
      this.log('⚠️  邮箱验证码获取失败，尝试备用策略...');
      this.verificationCode = await this.tryFallbackCodes();
    } else {
      this.log(`✅ 成功获取验证码: ${this.verificationCode}`);
    }

    // 填写登录表单
    await this.fillLoginForm();

    this.log('✅ 步骤2完成');
  }

  /**
   * 尝试备用验证码
   */
  async tryFallbackCodes() {
    this.log('🔄 尝试备用验证码策略...');

    // 首先尝试从学生密码生成验证码（取前6位数字）
    if (this.studentData.password) {
      const passwordDigits = this.studentData.password.replace(/\D/g, ''); // 提取数字
      if (passwordDigits.length >= 4) {
        const codeFromPassword = passwordDigits.substring(0, 6).padEnd(6, '0');
        this.log(`🔑 尝试基于密码生成的验证码: ${codeFromPassword}`);
        return codeFromPassword;
      }
    }

    // 使用预定义的备用验证码
    for (const fallbackCode of this.options.fallbackCodes) {
      this.log(`🔄 尝试备用验证码: ${fallbackCode}`);
      return fallbackCode; // 返回第一个备用验证码
    }

    // 最后的备用方案
    this.log('⚠️  使用最终备用验证码');
    return '123456';
  }

  /**
   * 填写登录表单
   */
  async fillLoginForm() {
    this.log('📝 填写登录表单...');

    // 检查页面上的字段类型
    const passwordField = await this.formFiller.currentPage.$('#password');
    const pinField = await this.formFiller.currentPage.$('#pin');
    const tempPinField = await this.formFiller.currentPage.$('#temp_pin');

    // 填写验证码/PIN字段
    if (passwordField) {
      this.log(`🔑 填写密码/验证码字段: ${this.verificationCode}`);
      await passwordField.click({ clickCount: 3 });
      await passwordField.type(this.verificationCode, { delay: 100 });
    }

    if (pinField) {
      this.log(`🔑 填写PIN字段: ${this.verificationCode}`);
      await pinField.click({ clickCount: 3 });
      await pinField.type(this.verificationCode, { delay: 100 });
    }

    if (tempPinField) {
      this.log(`🔑 填写临时PIN字段: ${this.verificationCode}`);
      await tempPinField.click({ clickCount: 3 });
      await tempPinField.type(this.verificationCode, { delay: 100 });
    }

    // 如果有实际的密码字段，使用Excel中的密码
    const actualPasswordField = await this.formFiller.currentPage.$('input[type="password"]:not(#password)');
    if (actualPasswordField && this.studentData.password) {
      this.log(`🔐 填写实际密码字段: ${this.studentData.password}`);
      await actualPasswordField.click({ clickCount: 3 });
      await actualPasswordField.type(this.studentData.password, { delay: 100 });
    }

    // 重新填写生日信息
    this.log('📅 重新填写生日信息...');
    
    if (this.studentData.birthMonth) {
      const monthField = await this.formFiller.currentPage.$('#birthdate_m');
      if (monthField) {
        await this.formFiller.currentPage.select('#birthdate_m', this.studentData.birthMonth);
      }
    }

    if (this.studentData.birthDay) {
      const dayField = await this.formFiller.currentPage.$('#birthdate_d');
      if (dayField) {
        await this.formFiller.currentPage.select('#birthdate_d', this.studentData.birthDay);
      }
    }

    if (this.studentData.birthYear) {
      const yearField = await this.formFiller.currentPage.$('#birthdate_y');
      if (yearField) {
        await this.formFiller.currentPage.select('#birthdate_y', this.studentData.birthYear);
      }
    }

    await new Promise(resolve => setTimeout(resolve, 1000));

    // 最终确认步骤
    await this.performFinalConfirmation();

    // 截图
    if (this.options.enableScreenshots) {
      await this.takeScreenshot('step2-login-form-filled');
    }

    // 点击Login按钮
    this.log('🔘 点击Login按钮...');
    const loginButton = await this.formFiller.currentPage.$('button[type="submit"]') ||
                       await this.formFiller.currentPage.$('input[type="submit"]') ||
                       await this.formFiller.currentPage.$('button:contains("Login")');

    if (!loginButton) {
      throw new Error('找不到Login按钮');
    }

    await loginButton.click();
    this.log('✅ Login按钮已点击');

    // 等待页面响应
    await new Promise(resolve => setTimeout(resolve, 5000));
  }

  /**
   * 执行最终确认步骤
   */
  async performFinalConfirmation() {
    this.log('🔍 执行最终确认步骤...');

    try {
      // 验证所有字段是否正确填写
      const fieldValidations = [];

      // 检查验证码/PIN字段
      const passwordField = await this.formFiller.currentPage.$('#password');
      if (passwordField) {
        const passwordValue = await passwordField.evaluate(el => el.value);
        fieldValidations.push({
          field: '验证码/PIN',
          expected: this.verificationCode,
          actual: passwordValue,
          valid: passwordValue === this.verificationCode
        });
      }

      // 检查生日字段
      const monthField = await this.formFiller.currentPage.$('#birthdate_m');
      if (monthField) {
        const monthValue = await monthField.evaluate(el => el.value);
        fieldValidations.push({
          field: '出生月份',
          expected: this.studentData.birthMonth,
          actual: monthValue,
          valid: monthValue === this.studentData.birthMonth
        });
      }

      const dayField = await this.formFiller.currentPage.$('#birthdate_d');
      if (dayField) {
        const dayValue = await dayField.evaluate(el => el.value);
        fieldValidations.push({
          field: '出生日期',
          expected: this.studentData.birthDay,
          actual: dayValue,
          valid: dayValue === this.studentData.birthDay
        });
      }

      const yearField = await this.formFiller.currentPage.$('#birthdate_y');
      if (yearField) {
        const yearValue = await yearField.evaluate(el => el.value);
        fieldValidations.push({
          field: '出生年份',
          expected: this.studentData.birthYear,
          actual: yearValue,
          valid: yearValue === this.studentData.birthYear
        });
      }

      // 检查密码字段（如果存在）
      const actualPasswordField = await this.formFiller.currentPage.$('input[type="password"]:not(#password)');
      if (actualPasswordField && this.studentData.password) {
        const actualPasswordValue = await actualPasswordField.evaluate(el => el.value);
        fieldValidations.push({
          field: '密码',
          expected: this.studentData.password,
          actual: actualPasswordValue,
          valid: actualPasswordValue === this.studentData.password
        });
      }

      // 输出验证结果
      this.log('📊 字段验证结果:');
      let allValid = true;
      fieldValidations.forEach(validation => {
        const status = validation.valid ? '✅' : '❌';
        this.log(`   ${status} ${validation.field}: 期望="${validation.expected}", 实际="${validation.actual}"`);
        if (!validation.valid) {
          allValid = false;
        }
      });

      if (allValid) {
        this.log('✅ 所有字段验证通过，准备提交表单');
      } else {
        this.log('⚠️  部分字段验证失败，但继续处理');
      }

      // 输出学生数据摘要
      this.log('📋 学生数据摘要:');
      this.log(`   👤 姓名: ${this.studentData.firstName} ${this.studentData.lastName}`);
      this.log(`   📧 邮箱: ${this.studentData.email}`);
      this.log(`   🎂 生日: ${this.studentData.birthYear}-${this.studentData.birthMonth}-${this.studentData.birthDay}`);
      this.log(`   🔑 验证码: ${this.verificationCode}`);
      if (this.studentData.password) {
        this.log(`   🔐 密码: ${this.studentData.password.substring(0, 3)}***`);
      }

    } catch (error) {
      this.log(`⚠️  最终确认失败: ${error.message}`);
    }
  }

  /**
   * 步骤3: 处理密码设置页面
   */
  async step3_HandlePasswordSetup() {
    this.log('\n📋 步骤3: 处理密码设置页面');

    // 截图当前页面
    if (this.options.enableScreenshots) {
      await this.takeScreenshot('step3-password-page');
    }

    // 获取当前页面信息
    const currentUrl = this.formFiller.currentPage.url();
    const pageTitle = await this.formFiller.currentPage.title();

    this.log(`📍 当前页面: ${currentUrl}`);
    this.log(`📄 页面标题: ${pageTitle}`);

    // 检查是否是密码设置页面
    const isPasswordPage = currentUrl.includes('/account/password') ||
                          pageTitle.toLowerCase().includes('password') ||
                          pageTitle.toLowerCase().includes('set password');

    if (!isPasswordPage) {
      this.log('⚠️  当前页面不是密码设置页面，跳过密码设置步骤');
      return;
    }

    this.log('✅ 检测到密码设置页面');

    // 检查学生是否有密码数据
    if (!this.studentData.password) {
      this.log('❌ 学生数据中没有密码信息');
      throw new Error('学生数据中缺少密码信息');
    }

    this.log(`🔐 学生密码: ${this.studentData.password.substring(0, 3)}***`);

    // 分析密码要求
    await this.analyzePasswordRequirements();

    // 验证密码复杂度
    const passwordValid = await this.validatePasswordComplexity(this.studentData.password);
    if (!passwordValid) {
      this.log('⚠️  密码不符合要求，但继续尝试');
    }

    // 填写密码表单
    await this.fillPasswordForm();

    this.log('✅ 步骤3完成');
  }

  /**
   * 分析密码要求
   */
  async analyzePasswordRequirements() {
    this.log('🔍 分析密码要求...');

    try {
      // 获取页面文本内容
      const pageText = await this.formFiller.currentPage.evaluate(() => {
        return document.body.innerText;
      });

      // 查找密码要求
      const requirements = [];

      if (pageText.includes('at least') || pageText.includes('minimum')) {
        const lengthMatch = pageText.match(/at least (\d+) characters?/i) ||
                           pageText.match(/minimum (\d+) characters?/i);
        if (lengthMatch) {
          requirements.push(`最少${lengthMatch[1]}个字符`);
        }
      }

      if (pageText.includes('uppercase') || pageText.includes('capital')) {
        requirements.push('至少一个大写字母');
      }

      if (pageText.includes('lowercase')) {
        requirements.push('至少一个小写字母');
      }

      if (pageText.includes('number') || pageText.includes('digit')) {
        requirements.push('至少一个数字');
      }

      if (pageText.includes('special character') || pageText.includes('symbol')) {
        requirements.push('至少一个特殊字符');
      }

      if (requirements.length > 0) {
        this.log('📋 密码要求:');
        requirements.forEach((req, index) => {
          this.log(`   ${index + 1}. ${req}`);
        });
      } else {
        this.log('📋 未找到明确的密码要求');
      }

    } catch (error) {
      this.log(`⚠️  分析密码要求失败: ${error.message}`);
    }
  }

  /**
   * 验证密码复杂度
   */
  async validatePasswordComplexity(password) {
    this.log('🔍 验证密码复杂度...');

    const checks = {
      length: password.length >= 8,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumber: /\d/.test(password),
      hasSpecial: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };

    this.log('📊 密码复杂度检查:');
    this.log(`   长度(≥8): ${checks.length ? '✅' : '❌'} (${password.length})`);
    this.log(`   大写字母: ${checks.hasUppercase ? '✅' : '❌'}`);
    this.log(`   小写字母: ${checks.hasLowercase ? '✅' : '❌'}`);
    this.log(`   数字: ${checks.hasNumber ? '✅' : '❌'}`);
    this.log(`   特殊字符: ${checks.hasSpecial ? '✅' : '❌'}`);

    const isValid = checks.length && checks.hasUppercase && checks.hasLowercase && checks.hasNumber;
    this.log(`🔐 密码整体评估: ${isValid ? '✅ 符合基本要求' : '⚠️  可能不符合要求'}`);

    return isValid;
  }

  /**
   * 填写密码表单
   */
  async fillPasswordForm() {
    this.log('📝 填写密码表单...');

    try {
      // 查找密码输入字段
      const passwordFields = await this.findPasswordFields();

      if (passwordFields.newPassword) {
        this.log('🔐 填写新密码字段...');
        await passwordFields.newPassword.click({ clickCount: 3 });
        await passwordFields.newPassword.type(this.studentData.password, { delay: 100 });
      }

      if (passwordFields.confirmPassword) {
        this.log('🔐 填写确认密码字段...');
        await passwordFields.confirmPassword.click({ clickCount: 3 });
        await passwordFields.confirmPassword.type(this.studentData.password, { delay: 100 });
      }

      // 等待一下让表单验证
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 验证密码字段填写
      await this.verifyPasswordFields(passwordFields);

      // 截图：密码表单填写完成
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('step3-password-filled');
      }

      // 提交密码表单
      await this.submitPasswordForm();

    } catch (error) {
      this.log(`❌ 填写密码表单失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 查找密码输入字段
   */
  async findPasswordFields() {
    this.log('🔍 查找密码输入字段...');

    const fields = {
      newPassword: null,
      confirmPassword: null
    };

    // 尝试多种可能的选择器
    const passwordSelectors = [
      'input[type="password"]',
      '#password',
      '#new_password',
      '#newPassword',
      'input[name="password"]',
      'input[name="new_password"]',
      'input[placeholder*="password"]'
    ];

    const confirmSelectors = [
      '#confirm_password',
      '#confirmPassword',
      '#password_confirmation',
      'input[name="confirm_password"]',
      'input[name="password_confirmation"]',
      'input[placeholder*="confirm"]'
    ];

    // 查找新密码字段
    for (const selector of passwordSelectors) {
      try {
        const element = await this.formFiller.currentPage.$(selector);
        if (element) {
          fields.newPassword = element;
          this.log(`✅ 找到新密码字段: ${selector}`);
          break;
        }
      } catch (error) {
        // 继续尝试下一个选择器
      }
    }

    // 查找确认密码字段
    for (const selector of confirmSelectors) {
      try {
        const element = await this.formFiller.currentPage.$(selector);
        if (element) {
          fields.confirmPassword = element;
          this.log(`✅ 找到确认密码字段: ${selector}`);
          break;
        }
      } catch (error) {
        // 继续尝试下一个选择器
      }
    }

    // 如果没找到确认密码字段，尝试找第二个密码字段
    if (!fields.confirmPassword && fields.newPassword) {
      const allPasswordFields = await this.formFiller.currentPage.$$('input[type="password"]');
      if (allPasswordFields.length >= 2) {
        fields.confirmPassword = allPasswordFields[1];
        this.log('✅ 找到第二个密码字段作为确认密码字段');
      }
    }

    this.log(`📊 密码字段查找结果:`);
    this.log(`   新密码字段: ${fields.newPassword ? '✅' : '❌'}`);
    this.log(`   确认密码字段: ${fields.confirmPassword ? '✅' : '❌'}`);

    return fields;
  }

  /**
   * 验证密码字段填写
   */
  async verifyPasswordFields(passwordFields) {
    this.log('🔍 验证密码字段填写...');

    try {
      if (passwordFields.newPassword) {
        const newPasswordValue = await passwordFields.newPassword.evaluate(el => el.value);
        const isCorrect = newPasswordValue === this.studentData.password;
        this.log(`   新密码字段: ${isCorrect ? '✅' : '❌'} (长度: ${newPasswordValue.length})`);
      }

      if (passwordFields.confirmPassword) {
        const confirmPasswordValue = await passwordFields.confirmPassword.evaluate(el => el.value);
        const isCorrect = confirmPasswordValue === this.studentData.password;
        this.log(`   确认密码字段: ${isCorrect ? '✅' : '❌'} (长度: ${confirmPasswordValue.length})`);
      }

    } catch (error) {
      this.log(`⚠️  验证密码字段失败: ${error.message}`);
    }
  }

  /**
   * 提交密码表单
   */
  async submitPasswordForm() {
    this.log('🔘 提交密码表单...');

    try {
      // 查找提交按钮
      const submitSelectors = [
        'button[type="submit"]',
        'input[type="submit"]',
        'button:contains("Submit")',
        'button:contains("Set Password")',
        'button:contains("Continue")',
        'button:contains("Save")',
        '#submit',
        '.submit-btn'
      ];

      let submitButton = null;
      for (const selector of submitSelectors) {
        try {
          submitButton = await this.formFiller.currentPage.$(selector);
          if (submitButton) {
            this.log(`✅ 找到提交按钮: ${selector}`);
            break;
          }
        } catch (error) {
          // 继续尝试下一个选择器
        }
      }

      if (!submitButton) {
        throw new Error('找不到提交按钮');
      }

      // 检查按钮是否可点击
      const isEnabled = await submitButton.evaluate(el => !el.disabled);
      if (!isEnabled) {
        this.log('⚠️  提交按钮被禁用，但尝试点击');
      }

      // 点击提交按钮
      await submitButton.click();
      this.log('✅ 密码表单已提交');

      // 等待页面响应
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 等待页面导航
      try {
        await this.formFiller.currentPage.waitForNavigation({ timeout: 10000 });
        this.log('✅ 页面导航完成');
      } catch (error) {
        this.log('⚠️  页面导航超时，但继续处理');
      }

    } catch (error) {
      this.log(`❌ 提交密码表单失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 步骤4: 处理后续步骤
   */
  async step4_HandleNextSteps() {
    this.log('\n📋 步骤4: 处理后续步骤');

    // 截图
    if (this.options.enableScreenshots) {
      await this.takeScreenshot('step3-next-page');
    }

    // 获取页面信息
    const currentUrl = this.formFiller.currentPage.url();
    const pageTitle = await this.formFiller.currentPage.title();
    
    this.log(`📍 当前页面: ${currentUrl}`);
    this.log(`📄 页面标题: ${pageTitle}`);

    // 获取页面内容
    const pageText = await this.formFiller.currentPage.evaluate(() => {
      return document.body.innerText.substring(0, 1000);
    });

    this.log('📄 页面内容预览:');
    this.log(pageText.substring(0, 300) + '...');

    // 检查是否有错误
    const hasError = pageText.toLowerCase().includes('error') ||
                    pageText.toLowerCase().includes('invalid') ||
                    pageText.toLowerCase().includes('incorrect');

    if (hasError) {
      this.log('⚠️  页面可能包含错误信息');
    } else {
      this.log('✅ 页面看起来正常');
    }

    this.log('✅ 步骤4完成');
  }

  /**
   * 验证步骤1表单
   */
  async verifyStep1Form() {
    this.log('🔍 验证步骤1表单填写...');
    
    try {
      const email = await this.formFiller.currentPage.$eval('#email', el => el.value);
      const firstName = await this.formFiller.currentPage.$eval('#first', el => el.value);
      const lastName = await this.formFiller.currentPage.$eval('#last', el => el.value);

      this.log(`   📧 邮箱: ${email} ${email === this.studentData.email ? '✅' : '❌'}`);
      this.log(`   👤 名字: ${firstName} ${firstName === this.studentData.firstName ? '✅' : '❌'}`);
      this.log(`   👤 姓氏: ${lastName} ${lastName === this.studentData.lastName ? '✅' : '❌'}`);

      if (!email || !firstName || !lastName) {
        throw new Error('必填字段未完成填写');
      }

    } catch (error) {
      this.log(`⚠️  表单验证失败: ${error.message}`);
    }
  }

  /**
   * 截图
   */
  async takeScreenshot(name) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `unr-complete-${this.studentData.firstName}-${this.studentData.lastName}-${name}-${timestamp}`;
      
      await this.formFiller.currentPage.screenshot({
        path: `${this.options.screenshotPath}/${filename}.png`,
        fullPage: true
      });
      
      this.log(`📸 截图保存: ${filename}.png`);
      
    } catch (error) {
      this.log(`⚠️  截图失败: ${error.message}`);
    }
  }

  /**
   * 日志输出
   */
  log(message) {
    if (this.options.enableLogging) {
      console.log(message);
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    if (this.formFiller) {
      await this.formFiller.close();
    }
  }
}

/**
 * 从Excel处理完整注册流程
 */
async function processCompleteFlowFromExcel(excelFilePath = 'test.xlsx') {
  console.log('🎓 UNR完整注册流程 - 从Excel处理');
  console.log('==================================');

  try {
    // 读取学生数据
    const fullPath = path.join(process.cwd(), excelFilePath);
    console.log(`📁 读取文件: ${fullPath}`);
    
    const rawData = await ExcelReader.readExcelFile(fullPath);
    const studentsData = ExcelReader.convertToUNRFormat(rawData);

    if (studentsData.length === 0) {
      console.log('❌ 没有找到有效的学生数据');
      return;
    }

    console.log(`📝 准备处理 ${studentsData.length} 个学生的完整注册流程\n`);

    // 获取要处理的学生索引（从命令行参数或环境变量）
    const studentIndex = process.env.STUDENT_INDEX ? parseInt(process.env.STUDENT_INDEX) : 1; // 默认处理第二个学生（索引1）

    if (studentIndex >= studentsData.length) {
      console.log(`❌ 学生索引 ${studentIndex} 超出范围，总共只有 ${studentsData.length} 个学生`);
      return;
    }

    // 处理指定的学生
    const student = studentsData[studentIndex];
    console.log(`🎯 处理学生 #${studentIndex + 1}: ${student.firstName} ${student.lastName} (${student.email})`);

    const flow = new UNRCompleteFlow({
      enableLogging: true,
      enableScreenshots: true,
      waitBetweenSteps: 2000
    });

    try {
      await flow.initialize();
      const result = await flow.executeCompleteFlow(student);
      
      if (result.success) {
        console.log('\n🎉 完整注册流程处理完成！');
        console.log('============================');
        console.log(`✅ 学生: ${result.studentData.firstName} ${result.studentData.lastName}`);
        console.log(`📧 邮箱: ${result.studentData.email}`);
        if (result.verificationCode) {
          console.log(`🔑 验证码: ${result.verificationCode}`);
        }
      } else {
        console.log('\n❌ 完整注册流程处理失败');
        console.log(`错误: ${result.error}`);
      }

      return result;

    } finally {
      await flow.cleanup();
    }

  } catch (error) {
    console.error('❌ 处理失败:', error.message);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  processCompleteFlowFromExcel().catch(error => {
    console.error('❌ 程序执行失败:', error.message);
    process.exit(1);
  });
}

module.exports = {
  UNRCompleteFlow,
  processCompleteFlowFromExcel
};
