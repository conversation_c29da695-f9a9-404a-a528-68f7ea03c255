/**
 * 浏览器配置管理模块
 * 管理浏览器启动参数、调试端口等配置
 */

// 延迟加载路径配置，避免打包时的循环依赖问题
function getPathConfig() {
  try {
    const { PathManager } = require('../src/config/path-manager');
    const pathManager = new PathManager();
    return pathManager.loadConfig();
  } catch (error) {
    // 如果加载失败，返回默认配置
    const os = require('os');
    const path = require('path');
    const userHome = os.homedir();

    return {
      chromePath: 'C:\\Users\\<USER>\\AppData\\Local\\Chromium\\Application\\chrome.exe',
      cacheDir: path.join(userHome, 'AppData\\Local\\UNR-Automation\\Cache'),
      userDataDir: path.join(userHome, 'AppData\\Local\\UNR-Automation\\Cache\\UserData'),
      downloadsDir: path.join(userHome, 'AppData\\Local\\UNR-Automation\\Cache\\Downloads'),
      screenshotsDir: path.join(userHome, 'AppData\\Local\\UNR-Automation\\Cache\\Screenshots'),
      logsDir: path.join(userHome, 'AppData\\Local\\UNR-Automation\\Cache\\Logs')
    };
  }
}

const browserConfig = {
  // 浏览器路径配置（动态加载）
  get paths() {
    const pathConfig = getPathConfig();
    return {
      chromePath: pathConfig.chromePath,
      cacheDir: pathConfig.cacheDir,
      userDataDir: pathConfig.userDataDir,
      downloadsDir: pathConfig.downloadsDir,
      screenshotsDir: pathConfig.screenshotsDir,
      logsDir: pathConfig.logsDir,
      // 保持向后兼容（已废弃，使用chromePath）
      chromium: pathConfig.chromePath ? require('path').dirname(pathConfig.chromePath) : pathConfig.cacheDir,
      chrome: pathConfig.chromePath ? require('path').dirname(pathConfig.chromePath) : pathConfig.cacheDir,
      executable: pathConfig.chromePath ? require('path').basename(pathConfig.chromePath) : "chrome.exe"
    };
  },

  // 远程调试配置
  debugging: {
    port: 9222,
    host: "localhost",
    wsEndpoint: function() {
      return `ws://${this.host}:${this.port}`;
    }
  },

  // 用户原始参数（保持指纹伪装效果）
  originalArgs: [
    "--fingerprint=2024",
    "--fingerprint-platform=macos",
    "--fingerprint-platform-version=15.2.0",
    "--fingerprint-brand=Edge",
    "--fingerprint-hardware-concurrency=4",
    "--timezone=America/Los_Angeles",
    "--lang=en-US",
    "--accept-lang=en-US,zh-CN",
    // "--disable-non-proxied-udp",
    // "--proxy-server=socks5://127.0.0.1:7897",
    "--disable-webgl",
    "--disable-webgl2",
    "--disable-accelerated-2d-canvas",
    "--user-data-dir=D:\\chrome\\bycache"
  ],

  // 必需的调试参数
  debuggingArgs: [
    "--remote-debugging-port=9222",
    "--remote-allow-origins=*"
  ],

  // 获取完整的启动参数
  getAllArgs: function() {
    return [...this.originalArgs, ...this.debuggingArgs];
  },

  // 获取完整的启动命令
  getStartCommand: function(browserPath = null) {
    const path = browserPath || `${this.paths.chromium}\\${this.paths.executable}`;
    const args = this.getAllArgs().join(" ");
    return `"${path}" ${args}`;
  },

  // 获取PowerShell启动命令
  getPowerShellCommand: function(browserPath = null) {
    const path = browserPath || `${this.paths.chromium}\\${this.paths.executable}`;
    const args = this.getAllArgs().join(" ");
    return `cd "${this.paths.chromium}"; .\\${this.paths.executable} ${args}`;
  },

  // 获取批处理启动命令
  getBatchCommand: function(browserPath = null) {
    const path = browserPath || `${this.paths.chromium}\\${this.paths.executable}`;
    const args = this.getAllArgs().join(" ");
    return `cd /d "${this.paths.chromium}" && "${this.paths.executable}" ${args}`;
  }
};

module.exports = browserConfig;
