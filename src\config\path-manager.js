/**
 * 路径管理模块
 * 处理Chrome浏览器路径和缓存目录的配置
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

class PathManager {
  constructor() {
    this.configFile = this.getConfigPath();
    this.defaultPaths = this.getDefaultPaths();
  }

  /**
   * 获取配置文件路径
   */
  getConfigPath() {
    // 如果是打包后的程序，使用可执行文件目录
    const isPackaged = process.pkg !== undefined;

    if (isPackaged) {
      const execDir = path.dirname(process.execPath);
      return path.join(execDir, 'assets', 'config', 'paths.json');
    } else {
      return path.join(__dirname, '../../config/paths.json');
    }
  }

  /**
   * 获取默认路径配置
   */
  getDefaultPaths() {
    const platform = os.platform();
    const userHome = os.homedir();
    
    let chromePaths = [];
    let defaultCacheDir = '';

    switch (platform) {
      case 'win32':
        chromePaths = [
          'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
          'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
          'C:\\Users\\<USER>\\AppData\\Local\\Chromium\\Application\\chrome.exe',
          path.join(userHome, 'AppData\\Local\\Google\\Chrome\\Application\\chrome.exe'),
          path.join(userHome, 'AppData\\Local\\Chromium\\Application\\chrome.exe')
        ];
        defaultCacheDir = path.join(userHome, 'AppData\\Local\\UNR-Automation\\Cache');
        break;
        
      case 'darwin':
        chromePaths = [
          '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
          '/Applications/Chromium.app/Contents/MacOS/Chromium'
        ];
        defaultCacheDir = path.join(userHome, 'Library/Application Support/UNR-Automation/Cache');
        break;
        
      case 'linux':
        chromePaths = [
          '/usr/bin/google-chrome',
          '/usr/bin/chromium-browser',
          '/usr/bin/chromium',
          '/snap/bin/chromium'
        ];
        defaultCacheDir = path.join(userHome, '.config/UNR-Automation/Cache');
        break;
        
      default:
        chromePaths = [];
        defaultCacheDir = path.join(userHome, '.unr-automation-cache');
    }

    return {
      chromePaths,
      defaultCacheDir,
      platform
    };
  }

  /**
   * 自动检测Chrome路径
   */
  detectChromePath() {
    for (const chromePath of this.defaultPaths.chromePaths) {
      if (fs.existsSync(chromePath)) {
        return chromePath;
      }
    }
    return null;
  }

  /**
   * 验证Chrome路径
   */
  validateChromePath(chromePath) {
    if (!chromePath) return false;
    
    try {
      const stats = fs.statSync(chromePath);
      return stats.isFile();
    } catch (error) {
      return false;
    }
  }

  /**
   * 创建缓存目录
   */
  createCacheDirectory(cacheDir) {
    try {
      if (!fs.existsSync(cacheDir)) {
        fs.mkdirSync(cacheDir, { recursive: true });
      }
      return true;
    } catch (error) {
      console.error('创建缓存目录失败:', error.message);
      return false;
    }
  }

  /**
   * 加载路径配置
   */
  loadConfig() {
    try {
      if (fs.existsSync(this.configFile)) {
        const configData = fs.readFileSync(this.configFile, 'utf8');
        const config = JSON.parse(configData);
        
        // 验证配置
        if (!this.validateChromePath(config.chromePath)) {
          console.warn('配置中的Chrome路径无效，尝试自动检测...');
          config.chromePath = this.detectChromePath();
        }
        
        return {
          chromePath: config.chromePath || this.detectChromePath(),
          cacheDir: config.cacheDir || this.defaultPaths.defaultCacheDir,
          userDataDir: config.userDataDir || path.join(config.cacheDir || this.defaultPaths.defaultCacheDir, 'UserData'),
          ...config
        };
      }
    } catch (error) {
      console.warn('加载路径配置失败，使用默认配置:', error.message);
    }

    // 返回默认配置
    const chromePath = this.detectChromePath();
    const cacheDir = this.defaultPaths.defaultCacheDir;
    
    return {
      chromePath,
      cacheDir,
      userDataDir: path.join(cacheDir, 'UserData'),
      downloadsDir: path.join(cacheDir, 'Downloads'),
      screenshotsDir: path.join(cacheDir, 'Screenshots'),
      logsDir: path.join(cacheDir, 'Logs')
    };
  }

  /**
   * 保存路径配置
   */
  saveConfig(config) {
    try {
      // 确保配置目录存在
      const configDir = path.dirname(this.configFile);
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }

      // 验证Chrome路径
      if (config.chromePath && !this.validateChromePath(config.chromePath)) {
        throw new Error('Chrome路径无效');
      }

      // 创建缓存目录
      if (config.cacheDir) {
        this.createCacheDirectory(config.cacheDir);
        this.createCacheDirectory(path.join(config.cacheDir, 'UserData'));
        this.createCacheDirectory(path.join(config.cacheDir, 'Downloads'));
        this.createCacheDirectory(path.join(config.cacheDir, 'Screenshots'));
        this.createCacheDirectory(path.join(config.cacheDir, 'Logs'));
      }

      // 保存配置
      const configToSave = {
        ...config,
        lastUpdated: new Date().toISOString(),
        platform: this.defaultPaths.platform
      };

      fs.writeFileSync(this.configFile, JSON.stringify(configToSave, null, 2));
      return true;
    } catch (error) {
      console.error('保存路径配置失败:', error.message);
      return false;
    }
  }

  /**
   * 重置为默认配置
   */
  resetConfig() {
    try {
      if (fs.existsSync(this.configFile)) {
        fs.unlinkSync(this.configFile);
      }
      return true;
    } catch (error) {
      console.error('重置配置失败:', error.message);
      return false;
    }
  }

  /**
   * 获取浏览器启动参数
   */
  getBrowserArgs(config) {
    const args = [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor',
      '--no-first-run',
      '--no-zygote',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--disable-features=TranslateUI',
      '--disable-ipc-flooding-protection',
      '--remote-debugging-port=9222',
      '--remote-allow-origins=*'
    ];

    // 添加用户数据目录
    if (config.userDataDir) {
      args.push(`--user-data-dir=${config.userDataDir}`);
    }

    // 添加下载目录
    if (config.downloadsDir) {
      args.push(`--download-default-directory=${config.downloadsDir}`);
    }

    return args;
  }

  /**
   * 获取可用的Chrome路径列表
   */
  getAvailableChromePaths() {
    return this.defaultPaths.chromePaths.filter(chromePath => 
      fs.existsSync(chromePath)
    );
  }

  /**
   * 获取配置信息
   */
  getConfigInfo() {
    const config = this.loadConfig();
    const availablePaths = this.getAvailableChromePaths();
    
    return {
      currentConfig: config,
      availableChromePaths: availablePaths,
      defaultPaths: this.defaultPaths,
      configFile: this.configFile,
      isPackaged: process.pkg !== undefined
    };
  }

  /**
   * 清理缓存目录
   */
  cleanCache(config) {
    try {
      const cacheDir = config.cacheDir;
      if (!cacheDir || !fs.existsSync(cacheDir)) {
        return false;
      }

      // 清理临时文件，保留配置
      const itemsToClean = ['UserData/Default/Cache', 'UserData/Default/Code Cache'];
      
      for (const item of itemsToClean) {
        const itemPath = path.join(cacheDir, item);
        if (fs.existsSync(itemPath)) {
          fs.rmSync(itemPath, { recursive: true, force: true });
        }
      }

      return true;
    } catch (error) {
      console.error('清理缓存失败:', error.message);
      return false;
    }
  }
}

module.exports = { PathManager };
