#!/usr/bin/env node

/**
 * UNR自动化工具主入口
 * 简化的启动器，专为打包优化
 */

// 设置进程标题
process.title = 'UNR自动化工具';

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('❌ 程序异常:', error.message);
  console.error('请检查配置或联系技术支持');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 程序错误:', reason);
  console.error('请检查配置或联系技术支持');
  process.exit(1);
});

// 处理Ctrl+C
process.on('SIGINT', () => {
  console.log('\n\n👋 程序已退出');
  process.exit(0);
});

// 检查运行环境
function checkEnvironment() {
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 16) {
    console.error('❌ Node.js版本过低，需要16.0.0或更高版本');
    console.error(`当前版本: ${nodeVersion}`);
    process.exit(1);
  }
}

// 主函数
async function main() {
  try {
    // 检查环境
    checkEnvironment();
    
    // 显示启动信息
    console.log('🚀 正在启动UNR自动化工具...');
    
    // 动态导入交互式终端
    const { InteractiveTerminal } = require('./interactive-terminal');
    
    // 创建并启动终端
    const terminal = new InteractiveTerminal();
    await terminal.start();
    
  } catch (error) {
    console.error('❌ 启动失败:', error.message);
    
    // 如果是模块加载错误，提供更详细的信息
    if (error.code === 'MODULE_NOT_FOUND') {
      console.error('可能的原因:');
      console.error('1. 缺少必要的依赖文件');
      console.error('2. 文件路径配置错误');
      console.error('3. 程序文件损坏');
      console.error('\n请重新下载程序或联系技术支持');
    }
    
    // 在Windows环境下暂停，让用户看到错误信息
    if (process.platform === 'win32') {
      const readline = require('readline');
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      await new Promise(resolve => {
        rl.question('\n按回车键退出...', () => {
          rl.close();
          resolve();
        });
      });
    }
    
    process.exit(1);
  }
}

// 启动应用
main();
