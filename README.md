# UNR大学自动化系统

基于JavaScript + Puppeteer的UNR大学自动化系统，专门用于自动化完成内华达大学里诺分校(University of Nevada, Reno)的学生注册和登录流程。

## 系统特点

- 🎓 **完整注册流程**：从表单填写到密码设置的端到端自动化
- 🔐 **智能登录模块**：独立的登录自动化功能
- 📊 **Excel数据集成**：从Excel文件读取学生信息
- 📧 **邮箱验证码获取**：自动获取和处理UNR发送的验证码
- 🔄 **智能重试机制**：网络问题和验证码获取的自动重试
- 📸 **完整记录**：每个步骤的自动截图和详细日志
- 🛡️ **错误处理**：完善的错误处理和恢复机制

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 准备学生数据
将学生信息保存在 `test.xlsx` 文件中，包含以下列：
- 邮箱 (Email)
- 名 (First Name) 
- 姓 (Last Name)
- 生日 (Birth Date)
- 密码 (Password)

### 3. 启动浏览器
```bash
# 使用Node.js脚本启动（推荐）
npm run browser

# 或使用批处理文件（Windows）
npm run browser:bat

# 或使用PowerShell脚本（Windows）
npm run browser:ps1
```

### 4. 运行自动化流程
```bash
# 执行完整的UNR注册自动化（从注册到密码设置）
npm run unr:full

# 或使用start命令
npm start

# 执行UNR登录自动化（直接登录已有账户）
npm run unr:login

# 执行UNR申请启动自动化（启动申请并填写申请信息表单）
npm run unr:start
```

## 系统架构

```
unr-automation-system/
├── src/
│   ├── form-filler.js              # 核心表单填写引擎
│   ├── utils/                      # 工具模块
│   │   ├── browser-connector.js        # 浏览器连接器
│   │   ├── excel-reader.js             # Excel数据读取器
│   │   ├── email-verification.js       # 邮箱验证码获取器
│   │   ├── form-handlers.js            # 表单处理器
│   │   └── retry-helper.js             # 重试助手
│   └── examples/                   # 主要应用
│       ├── unr-complete-flow.js        # UNR完整注册流程
│       └── unr-login.js                # UNR登录模块
├── scripts/                        # 浏览器启动脚本
├── config/                         # 配置文件
├── docs/                           # 文档
├── screenshots/                    # 自动截图保存目录
├── test.xlsx                       # 学生数据文件
└── package.json                    # 项目配置
```

## 功能模块说明

### 1. 完整注册流程 (`unr:full`)
- **步骤1**：注册表单填写
- **步骤2**：邮箱验证处理
- **步骤3**：密码设置
- **步骤4**：完成注册

### 2. 登录模块 (`unr:login`)
- **数据验证**：验证邮箱和密码格式
- **页面导航**：自动导航到登录页面
- **表单填写**：智能填写邮箱和密码字段
- **登录验证**：验证登录是否成功
- **错误处理**：完善的错误处理机制

### 3. 申请启动模块 (`unr:start`)
- **智能申请检测**：自动检测现有申请，优先使用进行中的申请
- **申请启动**：点击开始申请链接并处理对话框（仅在无现有申请时）
- **申请类型选择**：选择2025 Undergraduate Application
- **状态验证**：验证申请状态为"In Progress"
- **表单导航**：打开申请表单页面
- **智能状态检测**：多层次检测页面完成状态（CSS类、进度指标、字段填写度）
- **条件导航逻辑**：根据页面完成状态智能跳过已完成页面或继续填写
- **字段级智能填写**：检测已填写字段，避免覆盖现有数据
- **信息表单填写**：自动填写申请信息表单的6个选择项（智能跳过已选择项）
- **多页面处理**：自动处理Nondegree说明、申请学期、个人背景等页面
- **地址信息填写**：智能解析Excel中的完整地址并填写到相应字段
- **优化地址验证**：仅在页面提交后检测和处理地址验证提示框
- **智能邮寄地址处理**：条件性处理邮寄地址链接，支持已配置场景
- **个人信息填写**：填写电话、性别、出生地、国籍、SSN等信息
- **背景信息续页**：处理Background Continued页面的语言、代词、性别认知、性取向选择
- **智能性别映射**：根据Excel中的性别数据自动选择对应的代词和性别认知
- **紧急联系人页面**：处理Emergency Contact页面，生成随机联系人信息并重用地址数据
- **智能数据生成**：随机生成联系人姓名和邮箱，重用学生地址和电话信息
- **学术历史页面**：处理Academic History页面，从Excel读取高中信息并添加学校记录
- **智能学校检测**：检查现有学校记录避免重复添加，支持学校搜索和选择
- **项目选择页面**：处理Program Selection页面，智能检测现有选择并选择默认专业
- **签名页面**：处理Signature页面，使用学生姓名自动填写电子签名
- **审核页面**：处理Review页面，检查申请状态并自动提交申请
- **智能提交**：检查页面错误状态，确认无误后自动提交申请
- **断点续传支持**：支持重复运行和从中断点继续，保护现有数据
- **表单提交**：完成所有表单提交并验证结果

## 邮箱验证配置

系统使用以下邮箱配置进行验证码获取：
- **IMAP服务器**: imap.qq.com:993
- **连接邮箱**: <EMAIL>
- **验证逻辑**: UNR发送到学生邮箱 → 自动转发到QQ邮箱 → 系统获取验证码

## 使用示例

### 完整注册流程
```bash
# 运行完整注册流程
npm run unr:full
```

### 独立登录功能
```bash
# 运行登录模块
npm run unr:login
```

### 高级配置
```javascript
const { UNRLoginHandler } = require('./src/examples/unr-login');

// 自定义配置登录
const loginHandler = new UNRLoginHandler({
  enableLogging: true,
  enableScreenshots: true,
  waitAfterLogin: 3000,
  loginTimeout: 30000
});

await loginHandler.initialize();
const result = await loginHandler.executeLogin(studentData);
```

## 配置选项

### 主要配置
- `enableLogging`: 启用详细日志 (默认: true)
- `enableScreenshots`: 启用自动截图 (默认: true)
- `waitAfterLogin`: 登录后等待时间 (默认: 3000ms)
- `loginTimeout`: 登录超时时间 (默认: 30000ms)

### 邮箱验证配置
- `maxRetries`: 最大重试次数 (默认: 5)
- `checkInterval`: 重试间隔 (默认: 10秒)
- `recentEmailMinutes`: 邮件时间窗口 (默认: 5分钟)

## 故障排除

### 常见问题

1. **浏览器连接失败**
   ```bash
   # 重新启动浏览器
   npm run browser
   ```

2. **登录失败**
   - 检查Excel中的邮箱和密码是否正确
   - 确认账户已经注册
   - 查看截图确认页面状态

3. **验证码获取失败**
   - 检查邮箱转发设置
   - 确认UNR邮件已发送
   - 系统会自动使用备用验证码

4. **Excel文件读取失败**
   - 确保test.xlsx文件存在
   - 检查文件格式和列名
   - 确保包含必要的学生信息

### 调试技巧

1. **查看详细日志**：系统默认启用详细日志
2. **检查截图**：在screenshots目录查看每步截图
3. **验证数据**：确保Excel数据格式正确

## 系统要求

- Node.js >= 16.0.0
- Windows 10/11 (推荐)
- Chrome/Chromium 浏览器
- 稳定的网络连接

## 许可证

MIT License

## 更新日志

### v1.1.0
- 新增独立登录模块
- 支持直接登录已有账户
- 增强错误处理和验证机制
- 完善的截图记录功能

### v1.0.0
- 完整的UNR注册自动化系统
- Excel数据集成
- 邮箱验证码自动获取
- 密码设置自动化
- 完整的错误处理和日志记录
