/**
 * Excel文件读取工具
 * 用于从Excel文件中读取学生注册数据
 */

const fs = require('fs');
const path = require('path');
const XLSX = require('xlsx');

/**
 * 简单的CSV解析器（作为Excel的替代方案）
 * 如果需要真正的Excel支持，可以安装xlsx包
 */
class ExcelReader {
  /**
   * 读取Excel文件（目前支持CSV格式）
   */
  static async readExcelFile(filePath) {
    try {
      console.log(`📖 读取数据文件: ${filePath}`);
      
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        throw new Error(`文件不存在: ${filePath}`);
      }

      // 读取文件内容
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 如果是CSV文件，解析CSV
      if (filePath.toLowerCase().endsWith('.csv')) {
        return this.parseCSV(content);
      }
      
      // 如果是Excel文件，直接读取
      if (filePath.toLowerCase().endsWith('.xlsx') || filePath.toLowerCase().endsWith('.xls')) {
        console.log('📊 检测到Excel文件，正在读取...');
        return this.parseExcel(filePath);
      }

      throw new Error('不支持的文件格式，请使用CSV或Excel文件');

    } catch (error) {
      console.error('❌ 读取文件失败:', error.message);
      throw error;
    }
  }

  /**
   * 解析Excel文件
   */
  static parseExcel(filePath) {
    try {
      console.log(`📊 读取Excel文件: ${filePath}`);

      // 读取Excel文件
      const workbook = XLSX.readFile(filePath);

      // 获取第一个工作表
      const sheetName = workbook.SheetNames[0];
      console.log(`📋 使用工作表: ${sheetName}`);

      const worksheet = workbook.Sheets[sheetName];

      // 转换为JSON格式
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      if (jsonData.length < 2) {
        throw new Error('Excel文件至少需要包含标题行和一行数据');
      }

      // 解析标题行
      const headers = jsonData[0];
      console.log(`📋 发现列标题: ${headers.join(', ')}`);

      // 解析数据行
      const data = [];
      for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i];
        if (row && row.some(cell => cell !== undefined && cell !== '')) {
          const record = {};
          headers.forEach((header, index) => {
            record[header] = row[index] !== undefined ? String(row[index]).trim() : '';
          });
          data.push(record);
        }
      }

      console.log(`✅ 成功读取 ${data.length} 条记录`);
      return data;

    } catch (error) {
      console.error('❌ 读取Excel文件失败:', error.message);
      throw error;
    }
  }

  /**
   * 解析CSV内容
   */
  static parseCSV(content) {
    const lines = content.trim().split('\n');
    if (lines.length < 2) {
      throw new Error('CSV文件至少需要包含标题行和一行数据');
    }

    // 解析标题行
    const headers = this.parseCSVLine(lines[0]);
    console.log(`📋 发现列标题: ${headers.join(', ')}`);

    // 解析数据行
    const data = [];
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line) {
        const values = this.parseCSVLine(line);
        const record = {};
        
        headers.forEach((header, index) => {
          record[header.trim()] = values[index] ? values[index].trim() : '';
        });
        
        data.push(record);
      }
    }

    console.log(`✅ 成功读取 ${data.length} 条记录`);
    return data;
  }

  /**
   * 解析CSV行（处理引号和逗号）
   */
  static parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current);
        current = '';
      } else {
        current += char;
      }
    }
    
    result.push(current);
    return result;
  }

  /**
   * 将通用数据转换为UNR注册格式
   */
  static convertToUNRFormat(rawData) {
    return rawData.map((record, index) => {
      try {
        // 尝试多种可能的列名映射
        const email = this.getFieldValue(record, ['email', 'Email', 'EMAIL', '邮箱', 'Email Address']);
        const firstName = this.getFieldValue(record, ['firstName', 'first_name', 'First Name', 'first', '名', '名字', '姓名']);
        const lastName = this.getFieldValue(record, ['lastName', 'last_name', 'Last Name', 'last', '姓', '姓氏']);
        const birthDate = this.getFieldValue(record, ['birthDate', 'birth_date', 'Birth Date', 'Birthday', 'DOB', '生日', '出生日期']);
        const password = this.getFieldValue(record, ['password', 'Password', 'PASSWORD', '密码', 'pwd']);

        // 新增字段
        const completeAddress = this.getFieldValue(record, ['completeAddress', 'complete_address', 'Complete Address', '完整地址', 'address', 'Address']);
        const phone = this.getFieldValue(record, ['phone', 'Phone', 'PHONE', '电话', 'telephone', 'mobile']);
        const gender = this.getFieldValue(record, ['gender', 'Gender', 'GENDER', '性别', 'sex', 'Sex']);
        const ssn = this.getFieldValue(record, ['ssn', 'SSN', 'Social Security Number', '社会安全号', 'social_security']);

        // 解析生日
        let birthMonth = '', birthDay = '', birthYear = '';
        if (birthDate) {
          const parsed = this.parseBirthDate(birthDate);
          birthMonth = parsed.month;
          birthDay = parsed.day;
          birthYear = parsed.year;
        }

        // 如果没有生日，尝试单独的字段
        if (!birthMonth) {
          birthMonth = this.getFieldValue(record, ['birthMonth', 'birth_month', 'Month', '月份']);
        }
        if (!birthDay) {
          birthDay = this.getFieldValue(record, ['birthDay', 'birth_day', 'Day', '日期']);
        }
        if (!birthYear) {
          birthYear = this.getFieldValue(record, ['birthYear', 'birth_year', 'Year', '年份']);
        }

        // 验证必需字段
        if (!email || !firstName || !lastName) {
          console.warn(`⚠️  第${index + 1}行数据不完整，跳过: email=${email}, firstName=${firstName}, lastName=${lastName}`);
          return null;
        }

        return {
          email: email,
          firstName: firstName,
          lastName: lastName,
          birthMonth: this.formatMonth(birthMonth),
          birthDay: this.formatDay(birthDay),
          birthYear: this.formatYear(birthYear),
          password: password || '', // 添加密码字段
          completeAddress: completeAddress || '', // 完整地址
          phone: phone || '', // 电话号码
          gender: gender || '', // 性别
          ssn: ssn || '' // 社会安全号
        };

      } catch (error) {
        console.warn(`⚠️  第${index + 1}行数据解析失败:`, error.message);
        return null;
      }
    }).filter(record => record !== null);
  }

  /**
   * 获取字段值（支持多种列名）
   */
  static getFieldValue(record, possibleNames) {
    for (const name of possibleNames) {
      if (record[name] !== undefined && record[name] !== '') {
        return record[name];
      }
    }
    return '';
  }

  /**
   * 解析生日字符串
   */
  static parseBirthDate(dateStr) {
    if (!dateStr) {
      return { year: '', month: '', day: '' };
    }

    const dateString = String(dateStr).trim();

    // 检查是否是Excel数字日期格式
    if (/^\d+$/.test(dateString)) {
      const excelDate = parseInt(dateString);
      if (excelDate > 1 && excelDate < 100000) {
        // Excel日期转换 (Excel的日期起始点是1900年1月1日)
        const date = new Date((excelDate - 25569) * 86400 * 1000);
        if (!isNaN(date.getTime())) {
          return {
            year: date.getFullYear().toString(),
            month: (date.getMonth() + 1).toString().padStart(2, '0'),
            day: date.getDate().toString().padStart(2, '0')
          };
        }
      }
    }

    // 支持多种日期格式
    const formats = [
      /^(\d{4})-(\d{1,2})-(\d{1,2})$/,     // 2000-03-15
      /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,   // 03/15/2000
      /^(\d{1,2})-(\d{1,2})-(\d{4})$/,     // 03-15-2000
      /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,   // 2000/03/15
    ];

    for (const format of formats) {
      const match = dateString.match(format);
      if (match) {
        if (format.source.startsWith('^(\\d{4})')) {
          // 年在前的格式
          return {
            year: match[1],
            month: match[2].padStart(2, '0'),
            day: match[3].padStart(2, '0')
          };
        } else {
          // 月在前的格式
          return {
            year: match[3],
            month: match[1].padStart(2, '0'),
            day: match[2].padStart(2, '0')
          };
        }
      }
    }

    console.warn(`⚠️  无法解析日期格式: ${dateString}`);
    return { year: '', month: '', day: '' };
  }

  /**
   * 格式化月份
   */
  static formatMonth(month) {
    if (!month) return '';
    const num = parseInt(month);
    if (num >= 1 && num <= 12) {
      return num.toString().padStart(2, '0');
    }
    return '';
  }

  /**
   * 格式化日期
   */
  static formatDay(day) {
    if (!day) return '';
    const num = parseInt(day);
    if (num >= 1 && num <= 31) {
      return num.toString().padStart(2, '0');
    }
    return '';
  }

  /**
   * 格式化年份
   */
  static formatYear(year) {
    if (!year) return '';
    const num = parseInt(year);
    if (num >= 1900 && num <= 2010) {  // 合理的出生年份范围
      return num.toString();
    }
    return '';
  }

  /**
   * 创建示例CSV文件
   */
  static createSampleCSV(filePath) {
    const sampleData = `Email,First Name,Last Name,Birth Date
<EMAIL>,John,Doe,2000-03-15
<EMAIL>,Jane,Smith,1999-07-22
<EMAIL>,Michael,Johnson,2001-11-08
<EMAIL>,Sarah,Wilson,2000-05-12
<EMAIL>,David,Brown,1998-09-30`;

    fs.writeFileSync(filePath, sampleData, 'utf8');
    console.log(`✅ 示例CSV文件已创建: ${filePath}`);
    console.log('📝 文件包含5个示例学生记录');
  }
}

module.exports = { ExcelReader };
