# 📚 API 文档

## FormFiller 核心引擎

### 构造函数

```javascript
new FormFiller(options)
```

**参数：**
- `options` (Object) - 配置选项
  - `browserOptions` (Object) - 浏览器连接选项
  - `formOptions` (Object) - 表单处理选项
  - `stopOnError` (Boolean) - 遇到错误是否停止，默认 `false`
  - `enableLogging` (Boolean) - 是否启用日志，默认 `true`
  - `enableScreenshots` (Boolean) - 是否启用截图，默认 `false`
  - `screenshotPath` (String) - 截图保存路径，默认 `'./screenshots'`

**示例：**
```javascript
const formFiller = new FormFiller({
  stopOnError: false,
  enableLogging: true,
  enableScreenshots: true,
  formOptions: {
    timeout: 30000,
    retryOptions: {
      maxRetries: 3,
      baseDelay: 500
    }
  }
});
```

### 方法

#### initialize()

初始化表单填写引擎，建立浏览器连接。

```javascript
await formFiller.initialize()
```

**返回值：** `Promise<Boolean>` - 初始化是否成功

#### fillForm(config)

根据配置填写表单。

```javascript
await formFiller.fillForm(config)
```

**参数：**
- `config` (Object) - 表单填写配置

**返回值：** `Promise<FormFillResult>` - 填写结果对象

#### close()

关闭浏览器连接，释放资源。

```javascript
await formFiller.close()
```

## 表单配置格式

### 基础配置

```javascript
{
  url: String,                    // 表单页面URL
  skipNavigation: Boolean,        // 是否跳过页面导航
  waitForForm: String|Object,     // 等待表单加载
  fields: Object,                 // 表单字段配置
  submit: String|Object,          // 提交配置
  validation: Object              // 验证配置
}
```

### 字段配置

#### 简单配置
```javascript
{
  '#selector': 'value'
}
```

#### 复杂配置
```javascript
{
  '#selector': {
    value: 'value',               // 字段值
    locatorType: 'css',           // 定位方式
    elementType: 'text_input',    // 元素类型
    condition: Object,            // 条件配置
    selectBy: 'value',            // 下拉框选择方式
    skip: Boolean                 // 是否跳过
  }
}
```

### 定位方式 (locatorType)

- `'css'` - CSS选择器（默认）
- `'xpath'` - XPath表达式
- `'text'` - 可见文本内容
- `'placeholder'` - placeholder属性
- `'label'` - 关联的label文本

### 元素类型 (elementType)

- `'text_input'` - 文本输入框
- `'password_input'` - 密码输入框
- `'email_input'` - 邮箱输入框
- `'number_input'` - 数字输入框
- `'textarea'` - 文本域
- `'select'` - 下拉框
- `'checkbox'` - 复选框
- `'radio'` - 单选框
- `'file_input'` - 文件上传
- `'button'` - 按钮
- `'submit'` - 提交按钮

### 条件配置 (condition)

```javascript
{
  selector: String,               // 条件元素选择器
  exists: Boolean,                // 元素是否存在
  visible: Boolean                // 元素是否可见
}
```

或使用函数：
```javascript
{
  condition: async (page) => {
    // 自定义条件逻辑
    return Boolean;
  }
}
```

### 提交配置 (submit)

#### 简单配置
```javascript
submit: '#submitButton'
```

#### 复杂配置
```javascript
submit: {
  selector: '#submitButton',
  waitForResponse: Boolean,       // 等待响应
  waitForNavigation: Boolean      // 等待页面导航
}
```

### 验证配置 (validation)

```javascript
validation: {
  errorName: {
    selector: '.error-selector'   // 错误元素选择器
  }
}
```

## FormFillResult 结果对象

### 属性

- `success` (Boolean) - 整体是否成功
- `totalFields` (Number) - 总字段数
- `successFields` (Number) - 成功字段数
- `failedFields` (Number) - 失败字段数
- `skippedFields` (Number) - 跳过字段数
- `fieldResults` (Array) - 详细字段结果
- `errors` (Array) - 错误信息列表
- `duration` (Number) - 执行时长（毫秒）

### 方法

#### getSummary()

获取执行摘要。

```javascript
result.getSummary()
```

**返回值：**
```javascript
{
  success: Boolean,
  totalFields: Number,
  successFields: Number,
  failedFields: Number,
  skippedFields: Number,
  duration: Number,
  successRate: String              // 成功率百分比
}
```

## 表单处理器

### TextInputHandler

文本输入处理器，支持各种文本输入元素。

```javascript
const textHandler = new TextInputHandler(page, options);

// 填写文本
await textHandler.fill(selector, value, options);

// 获取值
const value = await textHandler.getValue(selector);

// 清空
await textHandler.clear(selector);
```

### SelectHandler

下拉框处理器。

```javascript
const selectHandler = new SelectHandler(page, options);

// 选择选项
await selectHandler.select(selector, value, { selectBy: 'value' });

// 获取选中值
const value = await selectHandler.getSelectedValue(selector);

// 获取所有选项
const options = await selectHandler.getOptions(selector);
```

### CheckboxHandler

复选框处理器。

```javascript
const checkboxHandler = new CheckboxHandler(page, options);

// 设置状态
await checkboxHandler.setChecked(selector, true);

// 勾选
await checkboxHandler.check(selector);

// 取消勾选
await checkboxHandler.uncheck(selector);

// 获取状态
const isChecked = await checkboxHandler.isChecked(selector);
```

### RadioHandler

单选框处理器。

```javascript
const radioHandler = new RadioHandler(page, options);

// 选择选项
await radioHandler.select(name, value);

// 获取选中值
const value = await radioHandler.getSelectedValue(name);
```

### FileUploadHandler

文件上传处理器。

```javascript
const fileHandler = new FileUploadHandler(page, options);

// 上传文件
await fileHandler.upload(selector, filePath);

// 上传多个文件
await fileHandler.upload(selector, [file1, file2], { multiple: true });

// 清空文件选择
await fileHandler.clear(selector);

// 获取已选择文件
const files = await fileHandler.getSelectedFiles(selector);
```

### ButtonHandler

按钮处理器。

```javascript
const buttonHandler = new ButtonHandler(page, options);

// 点击按钮
await buttonHandler.click(selector);

// 提交表单
await buttonHandler.submit(selector, { waitForResponse: true });

// 获取按钮文本
const text = await buttonHandler.getText(selector);

// 检查是否可用
const isEnabled = await buttonHandler.isEnabled(selector);
```

## 重试机制

### 错误类型

- `NETWORK_ERROR` - 网络错误
- `TIMEOUT_ERROR` - 超时错误
- `ELEMENT_NOT_FOUND` - 元素未找到
- `ELEMENT_NOT_VISIBLE` - 元素不可见
- `ELEMENT_NOT_INTERACTABLE` - 元素不可交互
- `NAVIGATION_ERROR` - 导航错误
- `SCRIPT_ERROR` - 脚本错误

### 重试策略

- `'fixed'` - 固定延迟
- `'linear'` - 线性退避
- `'exponential'` - 指数退避

### 重试配置

```javascript
{
  maxRetries: 3,                  // 最大重试次数
  baseDelay: 500,                 // 基础延迟时间
  maxDelay: 10000,                // 最大延迟时间
  strategy: 'exponential',        // 重试策略
  jitter: true,                   // 是否添加随机抖动
  totalTimeout: 30000,            // 总超时时间
  onRetry: (attempt, error) => {  // 重试回调
    console.log(`第${attempt}次重试: ${error.message}`);
  }
}
```

## 浏览器连接器

### BrowserConnector

```javascript
const connector = new BrowserConnector(options);

// 连接浏览器
await connector.connect();

// 创建新页面
const page = await connector.newPage();

// 断开连接
await connector.disconnect();
```

### 连接选项

```javascript
{
  wsEndpoint: 'ws://localhost:9222',  // WebSocket端点
  timeout: 30000,                     // 连接超时
  retryAttempts: 3,                   // 重试次数
  retryDelay: 1000                    // 重试延迟
}
```

## 错误处理

### 自定义错误类

- `BrowserConnectionError` - 浏览器连接错误
- `FormValidationError` - 表单验证错误
- `ElementNotFoundError` - 元素未找到错误
- `TimeoutError` - 超时错误

### 错误处理最佳实践

```javascript
try {
  const result = await formFiller.fillForm(config);
  
  if (!result.success) {
    console.log('部分字段填写失败:');
    result.errors.forEach(error => console.log(`- ${error}`));
  }
  
} catch (error) {
  if (error.name === 'BrowserConnectionError') {
    console.log('浏览器连接失败，请检查浏览器是否启动');
  } else if (error.name === 'FormValidationError') {
    console.log('表单验证失败:', error.message);
  } else {
    console.log('未知错误:', error.message);
  }
}
```

## 配置验证

### FormConfigValidator

```javascript
const { FormConfigValidator } = require('./src/form-filler');

// 验证配置
const errors = FormConfigValidator.validate(config);

if (errors.length > 0) {
  console.log('配置错误:');
  errors.forEach(error => console.log(`- ${error}`));
}
```

### 验证规则

1. 必须提供 `url` 或设置 `skipNavigation: true`
2. 必须提供 `fields` 配置
3. 字段配置必须包含 `value` 或设置 `skip: true`
4. 选择器不能为空
5. 条件配置必须有效

---

更多详细信息请参考 [README.md](../README.md) 和示例代码。
