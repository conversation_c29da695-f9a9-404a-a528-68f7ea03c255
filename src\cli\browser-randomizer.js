/**
 * 浏览器参数随机化模块
 * 生成随机的浏览器指纹以避免检测
 */

class BrowserRandomizer {
  constructor() {
    this.userAgents = {
      chrome: [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      ],
      edge: [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0'
      ],
      firefox: [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0'
      ]
    };

    this.resolutions = [
      { width: 1920, height: 1080 },
      { width: 1366, height: 768 },
      { width: 1536, height: 864 },
      { width: 1440, height: 900 },
      { width: 1280, height: 720 },
      { width: 1600, height: 900 },
      { width: 2560, height: 1440 }
    ];

    this.timezones = [
      'America/New_York',
      'America/Los_Angeles',
      'America/Chicago',
      'America/Denver',
      'Europe/London',
      'Europe/Paris',
      'Europe/Berlin',
      'Asia/Tokyo',
      'Asia/Shanghai',
      'Australia/Sydney'
    ];

    this.languages = [
      'en-US,en;q=0.9',
      'en-US,en;q=0.9,zh-CN;q=0.8',
      'en-GB,en;q=0.9',
      'en-US,en;q=0.9,es;q=0.8',
      'en-US,en;q=0.9,fr;q=0.8'
    ];

    this.platforms = [
      'Win32',
      'MacIntel',
      'Linux x86_64'
    ];

    this.hardwareConcurrency = [2, 4, 6, 8, 12, 16];
    this.deviceMemory = [2, 4, 8, 16, 32];
  }

  /**
   * 生成随机浏览器配置
   */
  generateRandomConfig() {
    const browserType = this.randomChoice(['chrome', 'edge', 'firefox']);
    const userAgent = this.randomChoice(this.userAgents[browserType]);
    const resolution = this.randomChoice(this.resolutions);
    const timezone = this.randomChoice(this.timezones);
    const language = this.randomChoice(this.languages);
    const platform = this.randomChoice(this.platforms);
    const hardwareConcurrency = this.randomChoice(this.hardwareConcurrency);
    const deviceMemory = this.randomChoice(this.deviceMemory);

    return {
      userAgent,
      viewport: {
        width: resolution.width,
        height: resolution.height
      },
      timezone,
      language,
      platform,
      hardwareConcurrency,
      deviceMemory,
      webgl: {
        vendor: this.getRandomWebGLVendor(),
        renderer: this.getRandomWebGLRenderer()
      },
      canvas: {
        noise: Math.random() * 0.1,
        shift: Math.floor(Math.random() * 10)
      },
      audio: {
        noise: Math.random() * 0.001
      }
    };
  }

  /**
   * 生成随机User-Agent
   */
  generateRandomUserAgent() {
    const allUserAgents = [
      ...this.userAgents.chrome,
      ...this.userAgents.edge,
      ...this.userAgents.firefox
    ];
    return this.randomChoice(allUserAgents);
  }

  /**
   * 生成随机分辨率
   */
  generateRandomResolution() {
    return this.randomChoice(this.resolutions);
  }

  /**
   * 生成随机时区
   */
  generateRandomTimezone() {
    return this.randomChoice(this.timezones);
  }

  /**
   * 生成随机语言设置
   */
  generateRandomLanguage() {
    return this.randomChoice(this.languages);
  }

  /**
   * 获取随机WebGL供应商
   */
  getRandomWebGLVendor() {
    const vendors = [
      'Google Inc.',
      'Mozilla',
      'Apple Inc.',
      'Microsoft Corporation'
    ];
    return this.randomChoice(vendors);
  }

  /**
   * 获取随机WebGL渲染器
   */
  getRandomWebGLRenderer() {
    const renderers = [
      'ANGLE (Intel, Intel(R) HD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.8681)',
      'ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.5671)',
      'ANGLE (AMD, AMD Radeon RX 580 Series Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.1020.2002)',
      'Intel Iris OpenGL Engine',
      'AMD Radeon Pro 560X OpenGL Engine',
      'NVIDIA GeForce GTX 1650 OpenGL Engine'
    ];
    return this.randomChoice(renderers);
  }

  /**
   * 生成浏览器启动参数
   */
  generateBrowserArgs(config) {
    const args = [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--disable-features=TranslateUI',
      '--disable-ipc-flooding-protection',
      `--window-size=${config.viewport.width},${config.viewport.height}`,
      `--user-agent=${config.userAgent}`,
      `--lang=${config.language.split(',')[0]}`,
      `--timezone=${config.timezone}`
    ];

    // 添加随机化参数
    if (Math.random() > 0.5) {
      args.push('--disable-web-security');
    }

    if (Math.random() > 0.5) {
      args.push('--disable-features=VizDisplayCompositor');
    }

    return args;
  }

  /**
   * 生成指纹脚本
   */
  generateFingerprintScript(config) {
    return `
      // 覆盖navigator属性
      Object.defineProperty(navigator, 'platform', {
        get: () => '${config.platform}'
      });
      
      Object.defineProperty(navigator, 'hardwareConcurrency', {
        get: () => ${config.hardwareConcurrency}
      });
      
      Object.defineProperty(navigator, 'deviceMemory', {
        get: () => ${config.deviceMemory}
      });
      
      Object.defineProperty(navigator, 'language', {
        get: () => '${config.language.split(',')[0]}'
      });
      
      Object.defineProperty(navigator, 'languages', {
        get: () => ['${config.language.split(',')[0]}', 'en']
      });

      // 覆盖WebGL指纹
      const getParameter = WebGLRenderingContext.prototype.getParameter;
      WebGLRenderingContext.prototype.getParameter = function(parameter) {
        if (parameter === 37445) {
          return '${config.webgl.vendor}';
        }
        if (parameter === 37446) {
          return '${config.webgl.renderer}';
        }
        return getParameter.call(this, parameter);
      };

      // Canvas指纹随机化
      const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
      HTMLCanvasElement.prototype.toDataURL = function() {
        const context = this.getContext('2d');
        if (context) {
          const imageData = context.getImageData(0, 0, this.width, this.height);
          for (let i = 0; i < imageData.data.length; i += 4) {
            imageData.data[i] += Math.floor(Math.random() * ${config.canvas.noise * 255});
          }
          context.putImageData(imageData, 0, 0);
        }
        return originalToDataURL.apply(this, arguments);
      };

      // 音频指纹随机化
      const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
      AudioContext.prototype.createAnalyser = function() {
        const analyser = originalCreateAnalyser.call(this);
        const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
        analyser.getFloatFrequencyData = function(array) {
          originalGetFloatFrequencyData.call(this, array);
          for (let i = 0; i < array.length; i++) {
            array[i] += Math.random() * ${config.audio.noise};
          }
        };
        return analyser;
      };
    `;
  }

  /**
   * 随机选择数组中的元素
   */
  randomChoice(array) {
    return array[Math.floor(Math.random() * array.length)];
  }

  /**
   * 生成随机整数
   */
  randomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * 生成随机浮点数
   */
  randomFloat(min, max) {
    return Math.random() * (max - min) + min;
  }

  /**
   * 验证配置有效性
   */
  validateConfig(config) {
    const required = ['userAgent', 'viewport', 'timezone', 'language'];
    for (const field of required) {
      if (!config[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    if (!config.viewport.width || !config.viewport.height) {
      throw new Error('Invalid viewport configuration');
    }

    return true;
  }

  /**
   * 保存配置到文件
   */
  saveConfig(config, filename = 'browser-config.json') {
    const fs = require('fs');
    const path = require('path');
    
    const configPath = path.join(__dirname, '../../config', filename);
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
    
    return configPath;
  }

  /**
   * 从文件加载配置
   */
  loadConfig(filename = 'browser-config.json') {
    const fs = require('fs');
    const path = require('path');
    
    const configPath = path.join(__dirname, '../../config', filename);
    
    if (!fs.existsSync(configPath)) {
      return null;
    }
    
    const configData = fs.readFileSync(configPath, 'utf8');
    return JSON.parse(configData);
  }
}

module.exports = { BrowserRandomizer };
