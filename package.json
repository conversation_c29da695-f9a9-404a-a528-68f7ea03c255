{"name": "browser-automation", "version": "1.0.0", "description": "JavaScript + Puppeteer浏览器自动化项目，连接到已启动浏览器进行表单填写", "main": "src/cli/app.js", "scripts": {"start": "node src/examples/unr-complete-flow.js", "browser": "node scripts/start-browser.js", "browser:bat": "scripts\\start-browser.bat", "browser:ps1": "powershell -ExecutionPolicy Bypass -File scripts\\start-browser.ps1", "unr:full": "node src/examples/unr-complete-flow.js", "unr:login": "node src/examples/unr-login.js", "unr:start": "node src/examples/unr-start-application.js", "unr:automation": "node src/examples/unr-full-automation.js", "unr:auto": "node src/examples/unr-full-automation.js", "terminal": "node src/cli/start-terminal.js", "cli": "node src/cli/start-terminal.js", "build": "pkg . --out-path dist", "build:win": "pkg . --targets node18-win-x64 --out-path dist", "build:all": "pkg . --targets node18-win-x64,node18-macos-x64,node18-linux-x64 --out-path dist"}, "keywords": ["puppeteer", "automation", "form-filling", "browser", "javascript"], "author": "Browser Automation Project", "license": "MIT", "dependencies": {"chalk": "^4.1.2", "figlet": "^1.6.0", "imap": "^0.8.19", "mailparser": "^3.7.4", "puppeteer-core": "^23.11.1", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.0.2", "pkg": "^5.8.1"}, "bin": {"unr-automation": "src/cli/app.js"}, "pkg": {"scripts": ["src/**/*.js", "config/**/*.js"], "assets": ["test.xlsx", "config/**/*", "docs/**/*", "screenshots/**/*", "logs/**/*"], "outputPath": "dist", "targets": ["node18-win-x64"]}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "."}}