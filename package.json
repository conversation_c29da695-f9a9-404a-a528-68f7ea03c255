{"name": "browser-automation", "version": "1.0.0", "description": "JavaScript + Puppeteer浏览器自动化项目，连接到已启动浏览器进行表单填写", "main": "src/form-filler.js", "scripts": {"start": "node src/examples/unr-complete-flow.js", "browser": "node scripts/start-browser.js", "browser:bat": "scripts\\start-browser.bat", "browser:ps1": "powershell -ExecutionPolicy Bypass -File scripts\\start-browser.ps1", "unr:full": "node src/examples/unr-complete-flow.js", "unr:login": "node src/examples/unr-login.js", "unr:start": "node src/examples/unr-start-application.js", "unr:automation": "node src/examples/unr-full-automation.js", "unr:auto": "node src/examples/unr-full-automation.js"}, "keywords": ["puppeteer", "automation", "form-filling", "browser", "javascript"], "author": "Browser Automation Project", "license": "MIT", "dependencies": {"imap": "^0.8.19", "mailparser": "^3.7.4", "puppeteer-core": "^23.11.1", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "."}}