/**
 * UNR大学登录自动化模块
 * 专门用于自动化登录UNR招生门户网站
 */

const { FormFiller } = require('../form-filler');
const { ExcelReader } = require('../utils/excel-reader');
const path = require('path');

/**
 * UNR登录处理器
 */
class UNRLoginHandler {
  constructor(options = {}) {
    this.options = {
      enableLogging: options.enableLogging !== false,
      enableScreenshots: options.enableScreenshots !== false,
      screenshotPath: options.screenshotPath || './screenshots',
      waitAfterLogin: options.waitAfterLogin || 3000,
      loginTimeout: options.loginTimeout || 30000,
      ...options
    };
    
    this.formFiller = null;
    this.studentData = null;
  }

  /**
   * 初始化登录处理器
   */
  async initialize() {
    this.formFiller = new FormFiller({
      enableLogging: this.options.enableLogging,
      enableScreenshots: this.options.enableScreenshots,
      screenshotPath: this.options.screenshotPath,
      stopOnError: false
    });

    await this.formFiller.initialize();
    this.log('🚀 UNR登录处理器初始化完成');
  }

  /**
   * 执行登录流程
   */
  async executeLogin(studentData) {
    this.studentData = studentData;
    this.log(`\n📝 开始登录流程: ${studentData.firstName} ${studentData.lastName}`);
    this.log(`📧 邮箱: ${studentData.email}`);
    this.log(`🔐 密码: ${studentData.password ? studentData.password.substring(0, 3) + '***' : '未提供'}`);

    try {
      // 验证登录数据
      await this.validateLoginData();
      
      // 导航到登录页面
      await this.navigateToLoginPage();
      
      // 填写登录表单
      await this.fillLoginForm();
      
      // 提交登录表单
      await this.submitLoginForm();
      
      // 验证登录结果
      const loginResult = await this.verifyLoginResult();
      
      this.log(`✅ 学生 ${studentData.firstName} ${studentData.lastName} 登录流程完成`);
      return {
        success: true,
        studentData: this.studentData,
        loginResult: loginResult
      };

    } catch (error) {
      this.log(`❌ 学生 ${studentData.firstName} ${studentData.lastName} 登录失败: ${error.message}`);
      return {
        success: false,
        studentData: this.studentData,
        error: error.message
      };
    }
  }

  /**
   * 验证登录数据
   */
  async validateLoginData() {
    this.log('🔍 验证登录数据...');

    if (!this.studentData.email) {
      throw new Error('缺少邮箱地址');
    }

    if (!this.studentData.password) {
      throw new Error('缺少密码');
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(this.studentData.email)) {
      throw new Error('邮箱格式无效');
    }

    this.log('✅ 登录数据验证通过');
  }

  /**
   * 导航到登录页面
   */
  async navigateToLoginPage() {
    this.log('🌐 导航到UNR登录页面...');
    
    await this.formFiller.currentPage.goto('https://admissions.unr.edu/account/login', {
      waitUntil: 'domcontentloaded',
      timeout: this.options.loginTimeout
    });

    // 等待登录表单加载
    await this.formFiller.currentPage.waitForSelector('#email', { timeout: this.options.loginTimeout });
    await this.formFiller.currentPage.waitForSelector('#password', { timeout: this.options.loginTimeout });
    
    this.log('✅ 登录页面加载完成');

    // 截图：登录页面
    if (this.options.enableScreenshots) {
      await this.takeScreenshot('login-page-loaded');
    }
  }

  /**
   * 填写登录表单
   */
  async fillLoginForm() {
    this.log('📝 填写登录表单...');

    try {
      // 清空并填写邮箱字段
      this.log('📧 填写邮箱字段...');
      const emailField = await this.formFiller.currentPage.$('#email');
      if (!emailField) {
        throw new Error('找不到邮箱输入字段');
      }
      
      await emailField.click({ clickCount: 3 }); // 选中所有文本
      await emailField.type(this.studentData.email, { delay: 50 });

      // 清空并填写密码字段
      this.log('🔐 填写密码字段...');
      const passwordField = await this.formFiller.currentPage.$('#password');
      if (!passwordField) {
        throw new Error('找不到密码输入字段');
      }
      
      await passwordField.click({ clickCount: 3 }); // 选中所有文本
      await passwordField.type(this.studentData.password, { delay: 50 });

      // 等待填写完成
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 验证表单填写
      await this.verifyFormFilling();

      // 截图：表单填写完成
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('login-form-filled');
      }

      this.log('✅ 登录表单填写完成');

    } catch (error) {
      this.log(`❌ 填写登录表单失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证表单填写
   */
  async verifyFormFilling() {
    this.log('🔍 验证表单填写结果...');

    try {
      const emailValue = await this.formFiller.currentPage.$eval('#email', el => el.value);
      const passwordValue = await this.formFiller.currentPage.$eval('#password', el => el.value);

      this.log('📊 表单验证结果:');
      this.log(`   📧 邮箱: ${emailValue === this.studentData.email ? '✅' : '❌'} (${emailValue})`);
      this.log(`   🔐 密码: ${passwordValue === this.studentData.password ? '✅' : '❌'} (长度: ${passwordValue.length})`);

      if (emailValue !== this.studentData.email) {
        throw new Error('邮箱字段填写不正确');
      }

      if (passwordValue !== this.studentData.password) {
        throw new Error('密码字段填写不正确');
      }

    } catch (error) {
      this.log(`⚠️  表单验证失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 提交登录表单
   */
  async submitLoginForm() {
    this.log('🔘 提交登录表单...');

    try {
      // 查找登录按钮
      const loginButtonSelector = '#content > form > table > tbody > tr > td:nth-child(1) > div > button';
      const loginButton = await this.formFiller.currentPage.$(loginButtonSelector);
      
      if (!loginButton) {
        // 尝试备用选择器
        const alternativeSelectors = [
          'button[type="submit"]',
          'input[type="submit"]',
          'button:contains("Login")',
          'button:contains("Sign In")',
          '.login-btn',
          '#login-button'
        ];

        let foundButton = null;
        for (const selector of alternativeSelectors) {
          try {
            foundButton = await this.formFiller.currentPage.$(selector);
            if (foundButton) {
              this.log(`✅ 找到备用登录按钮: ${selector}`);
              break;
            }
          } catch (error) {
            // 继续尝试下一个选择器
          }
        }

        if (!foundButton) {
          throw new Error('找不到登录按钮');
        }

        // 使用找到的备用按钮
        await foundButton.click();
      } else {
        this.log('✅ 找到主要登录按钮');
        await loginButton.click();
      }

      this.log('✅ 登录按钮已点击');

      // 等待页面响应
      await new Promise(resolve => setTimeout(resolve, this.options.waitAfterLogin));

      // 等待页面导航
      try {
        await this.formFiller.currentPage.waitForNavigation({ 
          timeout: this.options.loginTimeout,
          waitUntil: 'domcontentloaded'
        });
        this.log('✅ 页面导航完成');
      } catch (error) {
        this.log('⚠️  页面导航超时，但继续处理');
      }

    } catch (error) {
      this.log(`❌ 提交登录表单失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证登录结果
   */
  async verifyLoginResult() {
    this.log('🔍 验证登录结果...');

    try {
      // 获取当前页面信息
      const currentUrl = this.formFiller.currentPage.url();
      const pageTitle = await this.formFiller.currentPage.title();
      
      this.log(`📍 当前页面URL: ${currentUrl}`);
      this.log(`📄 页面标题: ${pageTitle}`);

      // 截图：登录后页面
      if (this.options.enableScreenshots) {
        await this.takeScreenshot('login-result-page');
      }

      // 获取页面内容
      const pageText = await this.formFiller.currentPage.evaluate(() => {
        return document.body.innerText.substring(0, 1000);
      });

      // 检查登录是否成功
      const loginSuccess = this.checkLoginSuccess(currentUrl, pageTitle, pageText);
      
      const result = {
        url: currentUrl,
        title: pageTitle,
        textPreview: pageText.substring(0, 300),
        loginSuccess: loginSuccess.success,
        message: loginSuccess.message,
        timestamp: new Date().toISOString()
      };

      if (loginSuccess.success) {
        this.log(`✅ 登录成功: ${loginSuccess.message}`);
      } else {
        this.log(`❌ 登录失败: ${loginSuccess.message}`);
        throw new Error(`登录验证失败: ${loginSuccess.message}`);
      }

      return result;

    } catch (error) {
      this.log(`❌ 验证登录结果失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 检查登录是否成功
   */
  checkLoginSuccess(url, title, pageText) {
    // 成功指标
    const successIndicators = [
      url.includes('/apply/'),
      url.includes('/dashboard'),
      url.includes('/account/') && !url.includes('/login'),
      title.toLowerCase().includes('application'),
      title.toLowerCase().includes('dashboard'),
      title.toLowerCase().includes('management'),
      pageText.includes('Application Management'),
      pageText.includes('Your Applications'),
      pageText.includes('Logout'),
      pageText.includes(this.studentData.firstName),
      pageText.includes(this.studentData.lastName)
    ];

    // 失败指标
    const failureIndicators = [
      url.includes('/login') && pageText.includes('error'),
      pageText.toLowerCase().includes('invalid'),
      pageText.toLowerCase().includes('incorrect'),
      pageText.toLowerCase().includes('login failed'),
      pageText.toLowerCase().includes('authentication failed'),
      title.toLowerCase().includes('login') && pageText.includes('error')
    ];

    const successCount = successIndicators.filter(Boolean).length;
    const failureCount = failureIndicators.filter(Boolean).length;

    if (failureCount > 0) {
      return {
        success: false,
        message: '检测到登录错误信息'
      };
    }

    if (successCount >= 2) {
      return {
        success: true,
        message: `检测到${successCount}个成功指标`
      };
    }

    if (url.includes('/apply/') || pageText.includes('Application Management')) {
      return {
        success: true,
        message: '已到达应用管理页面'
      };
    }

    return {
      success: false,
      message: '无法确认登录状态'
    };
  }

  /**
   * 截图
   */
  async takeScreenshot(name) {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `unr-login-${this.studentData.firstName}-${this.studentData.lastName}-${name}-${timestamp}`;
      
      await this.formFiller.currentPage.screenshot({
        path: `${this.options.screenshotPath}/${filename}.png`,
        fullPage: true
      });
      
      this.log(`📸 截图保存: ${filename}.png`);
      
    } catch (error) {
      this.log(`⚠️  截图失败: ${error.message}`);
    }
  }

  /**
   * 日志输出
   */
  log(message) {
    if (this.options.enableLogging) {
      console.log(message);
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    if (this.formFiller) {
      await this.formFiller.close();
    }
  }
}

/**
 * 从Excel处理UNR登录
 */
async function processUNRLoginFromExcel(excelFilePath = 'test.xlsx') {
  console.log('🎓 UNR大学登录自动化 - 从Excel处理');
  console.log('==================================');

  try {
    // 读取学生数据
    const fullPath = path.join(process.cwd(), excelFilePath);
    console.log(`📁 读取文件: ${fullPath}`);
    
    const rawData = await ExcelReader.readExcelFile(fullPath);
    const studentsData = ExcelReader.convertToUNRFormat(rawData);

    if (studentsData.length === 0) {
      console.log('❌ 没有找到有效的学生数据');
      return;
    }

    console.log(`📝 准备处理 ${studentsData.length} 个学生的登录流程\n`);

    // 获取要处理的学生索引（从命令行参数或环境变量）
    const studentIndex = process.env.STUDENT_INDEX ? parseInt(process.env.STUDENT_INDEX) : 1; // 默认处理第二个学生（索引1）

    if (studentIndex >= studentsData.length) {
      console.log(`❌ 学生索引 ${studentIndex} 超出范围，总共只有 ${studentsData.length} 个学生`);
      return;
    }

    // 处理指定的学生
    const student = studentsData[studentIndex];
    console.log(`🎯 处理学生 #${studentIndex + 1}: ${student.firstName} ${student.lastName} (${student.email})`);

    const loginHandler = new UNRLoginHandler({
      enableLogging: true,
      enableScreenshots: true,
      waitAfterLogin: 3000,
      loginTimeout: 30000
    });

    try {
      await loginHandler.initialize();
      const result = await loginHandler.executeLogin(student);
      
      if (result.success) {
        console.log('\n🎉 登录流程处理完成！');
        console.log('========================');
        console.log(`✅ 学生: ${result.studentData.firstName} ${result.studentData.lastName}`);
        console.log(`📧 邮箱: ${result.studentData.email}`);
        console.log(`📍 当前页面: ${result.loginResult.url}`);
        console.log(`📄 页面标题: ${result.loginResult.title}`);
      } else {
        console.log('\n❌ 登录流程处理失败');
        console.log(`错误: ${result.error}`);
      }

      return result;

    } finally {
      await loginHandler.cleanup();
    }

  } catch (error) {
    console.error('❌ 处理失败:', error.message);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  processUNRLoginFromExcel().catch(error => {
    console.error('❌ 程序执行失败:', error.message);
    process.exit(1);
  });
}

module.exports = {
  UNRLoginHandler,
  processUNRLoginFromExcel
};
